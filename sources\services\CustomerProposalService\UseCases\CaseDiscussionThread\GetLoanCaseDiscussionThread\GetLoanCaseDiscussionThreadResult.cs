using CustomerProposalService.Domain.Enums;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.GetLoanCaseDiscussionThread;

public record GetLoanCaseDiscussionThreadResult(
    string Id,
    DateTimeOffset RegisteredAt,
    string ThreadName,
    string RegistrantId,
    string RegistrantName,
    CaseDiscussionPurpose Purpose,
    string? Person,
    bool? IsPersonOfPower,
    string ReasonForGuaranty,
    string HowToImproveForGuaranty,
    string? MentionTargetsHtml,
    IEnumerable<string>? MentionTargetUserIds,
    IEnumerable<string>? MentionTargetTeamIds,
    string Description,
    IEnumerable<Domain.Entities.CaseDiscussionThreadFile>? Files,
    string Version
);
