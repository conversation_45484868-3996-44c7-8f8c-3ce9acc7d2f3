using Shared.Application;
using Shared.Messaging;

namespace BusinessCustomerUnderstandingService.Externals.CustomerProposal;

[MessageEndpoint("/customer-proposal/team-members/find-by-team-id")]
public record FindTeamMemberByTeamId : PageQuery, ISendRequest<IEnumerable<FindTeamMemberByTeamIdResult>>
{
    public IEnumerable<string>? TeamIds { get; set; }
}

public record FindTeamMemberByTeamIdResult(
    string Id,
    string TeamId,
    string StaffId,
    bool IsLeader,
    string Version
);
