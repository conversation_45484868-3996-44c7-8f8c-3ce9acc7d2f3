using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class ExternalEnvironmentMasterConfiguration : IEntityTypeConfiguration<ExternalEnvironmentMaster>
{
    public void Configure(EntityTypeBuilder<ExternalEnvironmentMaster> builder)
    {
        builder.Property(u => u.Id)
            .IsRequired();

        builder.Property(u => u.Name)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(u => u.Score)
            .IsRequired();
    }
}
