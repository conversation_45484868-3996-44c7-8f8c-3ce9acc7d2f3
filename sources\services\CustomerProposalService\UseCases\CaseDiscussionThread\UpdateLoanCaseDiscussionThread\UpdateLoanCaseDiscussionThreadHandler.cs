using System.Text;
using CustomerProposalService.Domain;
using CustomerProposalService.Domain.Enums;
using CustomerProposalService.Services.Domain;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;
using Shared.Domain;
using Shared.Services;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.UpdateLoanCaseDiscussionThread;

public class UpdateLoanCaseDiscussionThreadHandler : IRequestHandler<UpdateLoanCaseDiscussionThreadCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDateTimeService;
    private readonly ICaseDiscussionThreadUtility _utility;
    private readonly IFileProcessingService _fileProcessingService;

    private readonly string _folderName = "case-discussion-thread";
    private readonly string _containerName = "case-discussion";

    public UpdateLoanCaseDiscussionThreadHandler(
        IUnitOfWork unitOfWork,
        ICurrentDateTimeService currentDateTimeService,
        ICaseDiscussionThreadUtility utility,
        IFileProcessingService fileProcessingService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
        _utility = utility ?? throw new ArgumentNullException(nameof(utility));
        _fileProcessingService = fileProcessingService ?? throw new ArgumentNullException(nameof(fileProcessingService));
    }

    public async Task<Result<string>> Handle(UpdateLoanCaseDiscussionThreadCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 既存データを取得します。
        var repository = _unitOfWork.GetRepository<Domain.Entities.LoanCaseDiscussionThread, string>();
        var getResult = await repository.SingleAsync(
            new FindByIdSpecification<Domain.Entities.LoanCaseDiscussionThread, string>(request.Id)
            .Include(c => c.Files)
            ).ConfigureAwait(false);

        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();
        currentData.ThreadName = request.ThreadName;
        currentData.Purpose = request.Purpose;
        currentData.Person = request.Person;
        currentData.IsPersonOfPower = request.IsPersonOfPower;
        currentData.ReasonForGuaranty = request.ReasonForGuaranty;
        currentData.HowToImproveForGuaranty = request.HowToImproveForGuaranty;
        currentData.MentionTargetsHtml = request.MentionTargetsHtml;
        currentData.MentionTargetUserIds = request.MentionTargetUserIds;
        currentData.MentionTargetTeamIds = request.MentionTargetTeamIds;
        currentData.Description = request.Description;

        // 協議一覧表示用にまとめる
        var displayText = new StringBuilder();
        displayText.AppendLine($"【目的】{request.Purpose.DisplayName()}");
        if (request.Purpose == Domain.Enums.CaseDiscussionPurpose.External)
        {
            displayText.AppendLine($"【相手】{request.Person}");
            if (request.IsPersonOfPower == true)
            {
                displayText.AppendLine($"【実権者】☑");
            }
        }

        displayText.AppendLine($"【保証契約が必要となる具体的な理由】{request.ReasonForGuaranty}");
        displayText.AppendLine($"【どのような改善を図れば保証契約の変更・解除の可能性が高まるか】{request.HowToImproveForGuaranty}");
        displayText.AppendLine($"【協議コメント】");
        displayText.AppendLine($"{request.Description}");
        currentData.DisplayText = displayText.ToString();

        // 案件更新情報の最終更新日を更新する
        var updateResult = await _utility.UpdateCaseLastUpdatedAt(currentData.CaseId, _currentDateTimeService.NowDateTimeOffset());
        if (updateResult.IsError) return updateResult;

        // UploadCaseThreadFilesを共通サービス化。ここで呼び出す。
        if (request.UploadFiles != null || request.FilesToRemove != null)
        {
            // ファイルを更新
            var fileResult = await _fileProcessingService.UpdateFiles<Domain.Entities.CaseDiscussionThread, Domain.Entities.CaseDiscussionThreadFile>(currentData, _containerName, _folderName, request.UploadFiles, request.FilesToRemove).ConfigureAwait(false);
            if (fileResult.IsError) return fileResult;
        }

        // バージョン番号は、同時実行制御を有効にするために引数で受け取ったデータの値で必ず上書きします。
        currentData.Version = request.Version;

        // 通知処理を行います。
        await _utility.SendNotifyAsync(currentData, request.MentionTargetTeamMemberUserIds, request.CustomerName, request.CustomerStaffId, NotificationType.Update).ConfigureAwait(false);

        return await repository
            .UpdateAsync(currentData) // データを更新として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
