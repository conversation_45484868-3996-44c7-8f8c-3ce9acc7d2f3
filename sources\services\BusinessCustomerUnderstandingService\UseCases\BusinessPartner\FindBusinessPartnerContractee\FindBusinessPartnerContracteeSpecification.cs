using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartner.FindBusinessPartnerContractee;

public class FindBusinessPartnerContracteeSpecification : BaseSpecification<Domain.Entities.BusinessUnderstanding>
{
    public FindBusinessPartnerContracteeSpecification(Guid customerIdentificationId)
    {
        Query
            .Include(x => x.BusinessCustomer)
            .Include(x => x.BusinessPartners)
            .Where(x => x.BusinessPartners.Any(x => x.BusinessPartnerCustomerIdentificationId == customerIdentificationId))
            .AsNoTracking();
    }
}
