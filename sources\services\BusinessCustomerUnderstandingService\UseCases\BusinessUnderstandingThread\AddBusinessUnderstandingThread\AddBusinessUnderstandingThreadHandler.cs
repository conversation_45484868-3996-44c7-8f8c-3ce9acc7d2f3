using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Externals.BusinessCustomerUnderstanding;
using BusinessCustomerUnderstandingService.Externals.CustomerFixedInformation;
using BusinessCustomerUnderstandingService.Services.Domain;
using MediatR;
using Nut.Results;
using Shared.Domain;
using Shared.Messaging;
using Shared.Services;
using SharedKernel.ExternalApi.MessageContract.Notification;
using SharedKernel.ExternalApi.Domain.Notification;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThread.AddBusinessUnderstandingThread;

public class AddBusinessUnderstandingThreadHandler : IRequestHandler<AddBusinessUnderstandingThreadCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDatetimeService;
    private readonly ICurrentUserService _currentUserService;
    private readonly IMessageSender<FindCustomerStaff, IEnumerable<FindCustomerStaffResult>> _customerStaffMessageSender;
    private readonly IMessageSender<AddBusinessUnderstandingCommentReplyNotificationQuery, string> _sender;
    private readonly IMessageSender<UpdateBusinessUnderstandingUpdatedDateTime> _businessUnderstandingUpdateMessageSender;
    private readonly IFileProcessingService _fileProcessingService;
    private readonly IQuillImageService _quillImageService;

    private readonly string _containerName = "business-understanding";
    private readonly string _folderName = "thread";

    public AddBusinessUnderstandingThreadHandler(
        IUnitOfWork unitOfWork,
        ICurrentDateTimeService currentDatetimeService,
        ICurrentUserService currentUserService,
        IMessageSender<FindCustomerStaff, IEnumerable<FindCustomerStaffResult>> customerStaffMessageSender,
        IMessageSender<AddBusinessUnderstandingCommentReplyNotificationQuery, string> sender,
        IMessageSender<UpdateBusinessUnderstandingUpdatedDateTime> businessUnderstandingUpdateMessageSender,
        IFileProcessingService fileProcessingService,
        IQuillImageService quillImageService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentDatetimeService = currentDatetimeService ?? throw new ArgumentNullException(nameof(currentDatetimeService));
        _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
        _customerStaffMessageSender = customerStaffMessageSender ?? throw new ArgumentNullException(nameof(customerStaffMessageSender));
        _sender = sender ?? throw new ArgumentNullException(nameof(sender));
        _businessUnderstandingUpdateMessageSender = businessUnderstandingUpdateMessageSender ?? throw new ArgumentNullException(nameof(businessUnderstandingUpdateMessageSender));
        _fileProcessingService = fileProcessingService ?? throw new ArgumentNullException(nameof(fileProcessingService));
        _quillImageService = quillImageService ?? throw new ArgumentNullException(nameof(quillImageService));
    }
    public async Task<Result<string>> Handle(AddBusinessUnderstandingThreadCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 更新する値を作成します。
        var newData = new Domain.Entities.BusinessUnderstandingThread()
        {
            // キーの値をUlidで生成します。
            Id = Ulid.NewUlid().ToString(),
            Title = request.Title,
            RegisteredDateTime = _currentDatetimeService.NowDateTimeOffset(),
            Registrant = request.Registrant,
            RegistrantId = (await _currentUserService.GetAsync()).UserId!,
            BusinessUnderstandingId = request.BusinessUnderstandingId,
            MentionTargetsHtml = request.MentionTargetsHtml,
            MentionTargetUserIds = request.MentionTargetUserIds,
            MentionTargetTeamIds = request.MentionTargetTeamIds,
            Purpose = request.Purpose,
            Person = request.Person,
            IsPersonOfPower = request.IsPersonOfPower,
            CorrespondenceDate = request.CorrespondenceDate,
            IsCorporateDepositTheme = request.IsCorporateDepositTheme,
            IsFundSettlementTheme = request.IsFundSettlementTheme
        };

        // 本文添付ファイルの処理
        var newDelta = await _quillImageService.ArrangeContentByUrlWhenSaveAsync(_containerName, _folderName, request.Description, newData.Id);
        if (newDelta.IsError) return newDelta;
        newData.Description = newDelta.Get();

        // 通知先
        var notifyTargetUserIds = new HashSet<string>();
        var notifyMentionTargetUserIds = new HashSet<string>();

        var businessUnderstandingRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding>();
        var getBusinessUnderstandingResult = await businessUnderstandingRepository.SingleAsync(new FindByIdSpecification<Domain.Entities.BusinessUnderstanding, string>(newData.BusinessUnderstandingId));
        if (getBusinessUnderstandingResult.IsError) return getBusinessUnderstandingResult.PreserveErrorAs<string>();
        var businessUnderstanding = getBusinessUnderstandingResult.Get();

        // 顧客担当者
        var businessCustomerRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessCustomer>();
        var getBusinessCustomerResult = await businessCustomerRepository.SingleAsync(new FindByIdSpecification<Domain.Entities.BusinessCustomer, string>(businessUnderstanding.BusinessCustomerId));
        if (getBusinessCustomerResult.IsError) return getBusinessCustomerResult.PreserveErrorAs<string>();
        var businessCustomer = getBusinessCustomerResult.Get();

        var findCustomerStaffResult = await _customerStaffMessageSender.SendAsync(new FindCustomerStaff { CustomerIdentificationIds = new List<Guid>() { businessCustomer.CustomerIdentificationId } });
        if (findCustomerStaffResult.IsError) return findCustomerStaffResult.PreserveErrorAs<string>();
        var staffId = findCustomerStaffResult.Get().FirstOrDefault()?.StaffId;

        if (!string.IsNullOrEmpty(staffId) && newData.RegistrantId != staffId && !notifyTargetUserIds.Contains(staffId))
        {
            notifyTargetUserIds.Add(staffId);
        }

        // メンション
        if (request.MentionTargetUserIds is not null)
        {
            var mentionTargetUserIds = request.MentionTargetUserIds.Where(m => m != null && m != string.Empty);
            if (mentionTargetUserIds.Any())
            {
                notifyMentionTargetUserIds.UnionWith(mentionTargetUserIds.Where(x => !x.Equals(newData.RegistrantId)));
            }
        }

        if (request.MentionTargetTeamMemberUserIds is not null)
        {
            var mentionTargetUserIds = request.MentionTargetTeamMemberUserIds.Where(m => m != null && m != string.Empty);
            if (mentionTargetUserIds.Any())
            {
                notifyMentionTargetUserIds.UnionWith(mentionTargetUserIds.Where(x => !x.Equals(newData.RegistrantId)));
            }
        }

        // メンション通知
        if (notifyMentionTargetUserIds.Any())
        {
            var notificationResult =
            await _sender
                .SendAsync(new AddBusinessUnderstandingCommentReplyNotificationQuery()
                {
                    TriggerdUserId = newData.RegistrantId,
                    TriggeredUserName = newData.Registrant,
                    CommentId = newData.Id,
                    CustomerName = request.CustomerName,
                    ThreadId = newData.Id,
                    ThreadTitle = request.Title,
                    Items = notifyMentionTargetUserIds.Select(x => new AddBusinessUnderstandingCommentReplyNotificationCommandItem(businessUnderstanding.Id, x)),
                    Type = NotificationType.Mention
                })
                .ConfigureAwait(false);
        }

        // メンション通知が優先のため、自動通知先から除外する
        notifyTargetUserIds = new HashSet<string>(notifyTargetUserIds.Except(notifyMentionTargetUserIds));

        // 自動通知
        if (notifyTargetUserIds.Any())
        {
            var notificationResult =
            await _sender
                .SendAsync(new AddBusinessUnderstandingCommentReplyNotificationQuery()
                {
                    TriggerdUserId = newData.RegistrantId,
                    TriggeredUserName = newData.Registrant,
                    CommentId = newData.Id,
                    CustomerName = request.CustomerName,
                    ThreadId = newData.Id,
                    ThreadTitle = request.Title,
                    Items = notifyTargetUserIds.Select(x => new AddBusinessUnderstandingCommentReplyNotificationCommandItem(businessUnderstanding.Id, x)),
                    Type = NotificationType.Post
                })
                .ConfigureAwait(false);
        }

        // 事業性理解最終更新日の更新
        var updateBusinessUnderstandingResult = await _businessUnderstandingUpdateMessageSender.SendAsync(new UpdateBusinessUnderstandingUpdatedDateTime()
        {
            BusinessUnderstandingId = businessUnderstanding.Id,
            UpdaterId = (await _currentUserService.GetAsync()).UserId!,
            UpdaterName = request.Registrant
        });
        if (updateBusinessUnderstandingResult.IsError) return updateBusinessUnderstandingResult.PreserveErrorAs<string>();

        // ファイルをアップロードします。
        if (request.UploadFiles != null)
        {
            // ファイルアップロード
            var uploadResult = await _fileProcessingService.UpdateFiles<Domain.Entities.BusinessUnderstandingThread, Domain.Entities.BusinessUnderstandingThreadFile>(newData, _containerName, _folderName, request.UploadFiles, null);
            if (uploadResult.IsError) return uploadResult;
        }

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingThread>();
        return await repository
            .AddAsync(newData) // データを追加します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(newData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
