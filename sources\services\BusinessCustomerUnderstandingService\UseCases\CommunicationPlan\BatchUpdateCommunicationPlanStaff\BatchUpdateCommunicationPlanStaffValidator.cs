using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.BatchUpdateCommunicationPlanStaff;

public class BatchUpdateCommunicationPlanStaffValidator : AbstractValidator<BatchUpdateCommunicationPlanStaffCommand>
{
    public BatchUpdateCommunicationPlanStaffValidator()
    {
        RuleFor(v => v.CustomerIdentificationIdList).NotEmpty();
        RuleFor(v => v.CurrentStaffId).NotEmpty();
        RuleFor(v => v.ChangeStaffId).NotEmpty();
        RuleFor(v => v.ChangeStaffName).NotEmpty();
    }
}
