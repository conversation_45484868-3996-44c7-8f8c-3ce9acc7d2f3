using System.Text;
using CustomerProposalService.Configurations;
using CustomerProposalService.Domain;
using CustomerProposalService.Infrastructure.Storage;
using Microsoft.AspNetCore.Http;
using Nut.Results;
using Shared.AzureBlob.QuillContents;
using Shared.Domain;
using Shared.Messaging;
using SharedKernel.ExternalApi.MessageContract.Notification;

namespace CustomerProposalService.UseCases.CaseDiscussionReply;
public class CaseDiscussionReplyUtility : ICaseDiscussionReplyUtility
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IStorageClientProvider _objectStorageClientProvider;
    private readonly IMessageSender<AddCaseDiscussionNotificationQuery, IEnumerable<string>> _sender;
    private readonly ServiceSettings _serviceSettings;
    private readonly IQuillContentsUtility _quillContentsUtility;
    private readonly string _baseUrl;

    private const string ContainerName = "case-discussion";
    private const string ImageAddressPrefix = "case-discussion-reply";

    public CaseDiscussionReplyUtility(
        IUnitOfWork unitOfWork,
        IStorageClientProvider objectStorageClientProvider,
        IMessageSender<AddCaseDiscussionNotificationQuery, IEnumerable<string>> sender,
        ServiceSettings serviceSettings,
        IQuillContentsUtility quillContentsUtility)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
        _sender = sender ?? throw new ArgumentNullException(nameof(sender));
        _serviceSettings = serviceSettings ?? throw new ArgumentNullException(nameof(serviceSettings));
        _quillContentsUtility = quillContentsUtility ?? throw new ArgumentNullException(nameof(quillContentsUtility));
        _baseUrl = _serviceSettings.Blob.AttachedContentsBaseUrl ?? string.Empty;
    }

    public async Task<Result<IEnumerable<string>>> SendNotifyAsync<T>(
        T caseDiscussionReply,
        IEnumerable<string>? mentionTargetTeamMemberUserIds,
        string caseId,
        string customerName,
        string? customerStaffId
        ) where T : Domain.Entities.CaseDiscussionReply
    {
        var notifyTargetUserIds = new HashSet<string>();
        var notifyMentionTargetUserIds = new HashSet<string>();

        // メンション先は手動通知先
        if (caseDiscussionReply.MentionTargetUserIds is not null)
        {
            var mentionTargetUserIds = caseDiscussionReply.MentionTargetUserIds.Where(m => !string.IsNullOrWhiteSpace(m));
            notifyMentionTargetUserIds.UnionWith(mentionTargetUserIds);
        }

        // チームメンションは手動通知先
        if (mentionTargetTeamMemberUserIds is not null &&
            mentionTargetTeamMemberUserIds.Any())
        {
            notifyMentionTargetUserIds.UnionWith(mentionTargetTeamMemberUserIds);
        }

        // 登録者はメンションしないように
        notifyMentionTargetUserIds = notifyMentionTargetUserIds.Where(x => !x.Equals(caseDiscussionReply.RegistrantId)).ToHashSet();

        // 案件担当者は自動通知先（二重通知を避け、手動通知を優先）
        var caseRepository = _unitOfWork.GetRepository<Domain.Entities.Case>();
        var caseResult = await caseRepository.SingleAsync(new FindByIdSpecification<Domain.Entities.Case, string>(caseId));
        if (caseResult.IsError) return caseResult.PreserveErrorAs<IEnumerable<string>>();
        var caseInfo = caseResult.Get();
        if (!string.IsNullOrEmpty(caseInfo.StaffId) && caseDiscussionReply.RegistrantId != caseInfo.StaffId)
        {
            notifyTargetUserIds.Add(caseInfo.StaffId);
        }

        // 顧客担当者は自動通知先（二重通知を避け、手動通知を優先）
        if (!string.IsNullOrEmpty(customerStaffId) && caseDiscussionReply.RegistrantId != customerStaffId && customerStaffId != "undefined")
        {
            notifyTargetUserIds.Add(customerStaffId);
        }

        var caseDiscussionThreadRepository = _unitOfWork.GetRepository<Domain.Entities.CaseDiscussionThread>();
        var caseDiscussionThreadResult = await caseDiscussionThreadRepository.SingleAsync(new FindByIdSpecification<Domain.Entities.CaseDiscussionThread, string>(caseDiscussionReply.CaseDiscussionThreadId));
        if (caseDiscussionThreadResult.IsError) return caseDiscussionThreadResult.PreserveErrorAs<IEnumerable<string>>();
        var caseDiscussionThreadInfo = caseDiscussionThreadResult.Get();

        // スレッドを取得
        var threadRepository = _unitOfWork.GetRepository<Domain.Entities.CaseDiscussionThread>();
        var threadResult = await threadRepository
            .SingleAsync(new FindByIdSpecification<Domain.Entities.CaseDiscussionThread, string>(caseDiscussionThreadInfo.Id)
            .Include(t => t.Replies)
            );
        if (threadResult.IsError) return threadResult.PreserveErrorAs<IEnumerable<string>>();
        var thread = threadResult.Get();

        // スレッド登録者
        if (caseDiscussionReply.RegistrantId != thread.RegistrantId)
        {
            notifyTargetUserIds.Add(thread.RegistrantId);
        }

        var linkPath = $"/customer-proposal/customer-case/case-discussion?id={caseId}&customerIdentificationId={caseInfo.CustomerIdentificationId}&threadId={caseDiscussionReply.CaseDiscussionThreadId}";

        var iBPHeaderText = "";
        var teamsHeaderText = "";

        var newLineSpace = new string(' ', 2);

        // メンション通知
        if (notifyMentionTargetUserIds.Any())
        {
            iBPHeaderText = $"社内外協議にて{caseDiscussionReply.RegistrantName}がメンションしました。";
            teamsHeaderText = $"{caseDiscussionReply.RegistrantName}がメンションしました。";

            var teamsMessageBuilder = new StringBuilder()
                    .AppendLine($"{teamsHeaderText}" + newLineSpace)
                    .AppendLine($"{customerName}" + newLineSpace)
                    .AppendLine($"{caseInfo.CaseCategory.DisplayName()}" + newLineSpace)
                    .AppendLine($"{caseInfo.CaseName}" + newLineSpace);

            if (caseInfo.StaffName.Length > 0)
            {

                teamsMessageBuilder.AppendLine($"案件担当者：{caseInfo.StaffName}" + newLineSpace);
            }

            teamsMessageBuilder.AppendLine($"[{caseDiscussionThreadInfo.ThreadName}](https://{_serviceSettings.Bot.HostName}{linkPath})" + newLineSpace)
                .ToString();

            var teamsMessage = teamsMessageBuilder.ToString();

            await _sender.SendAsync(new AddCaseDiscussionNotificationQuery()
            {
                FromUserId = caseDiscussionReply.RegistrantId,
                IBPNotificationTitle = $"{caseInfo.CaseName}",
                IBPNotificationMessage = $"{customerName}【{caseInfo.CaseCategory.DisplayName()}】 {iBPHeaderText} 【{caseDiscussionThreadInfo.ThreadName}】",
                TeamsMessage = teamsMessage,
                LinkPath = linkPath,
                ToUserIds = notifyMentionTargetUserIds,
            }).ConfigureAwait(false);
        }

        // メンション通知が優先のため、自動通知先から除外する
        notifyTargetUserIds = notifyTargetUserIds.Except(notifyMentionTargetUserIds).ToHashSet();

        // 自動通知 (返信)
        if (notifyTargetUserIds.Any())
        {
            iBPHeaderText = $"{caseDiscussionReply.RegistrantName}が社内外協議に返信しました";
            teamsHeaderText = $"{caseDiscussionReply.RegistrantName}が社内外協議に返信しました。";

            var teamsMessageBuilder = new StringBuilder()
                    .AppendLine($"{teamsHeaderText}" + newLineSpace)
                    .AppendLine($"{customerName}" + newLineSpace)
                    .AppendLine($"{caseInfo.CaseCategory.DisplayName()}" + newLineSpace)
                    .AppendLine($"{caseInfo.CaseName}" + newLineSpace);

            if (caseInfo.StaffName.Length > 0)
            {

                teamsMessageBuilder.AppendLine($"案件担当者：{caseInfo.StaffName}" + newLineSpace);
            }

            teamsMessageBuilder.AppendLine($"[{caseDiscussionThreadInfo.ThreadName}](https://{_serviceSettings.Bot.HostName}{linkPath})" + newLineSpace)
                .ToString();

            var teamsMessage = teamsMessageBuilder.ToString();

            await _sender.SendAsync(new AddCaseDiscussionNotificationQuery()
            {
                FromUserId = caseDiscussionReply.RegistrantId,
                IBPNotificationTitle = $"{caseInfo.CaseName}",
                IBPNotificationMessage = $"{customerName}【{caseInfo.CaseCategory.DisplayName()}】 {iBPHeaderText} 【{caseDiscussionThreadInfo.ThreadName}】",
                TeamsMessage = teamsMessage,
                LinkPath = linkPath,
                ToUserIds = notifyTargetUserIds
            }).ConfigureAwait(false);
        }

        return Result.Ok(Enumerable.Empty<string>());
    }

    public async Task<Result<string>> DeleteAllFiles(Domain.Entities.CaseDiscussionReply currentData)
    {
        // Blob、DBとの接続を確立
        var storageClient = await _objectStorageClientProvider.CreateAsync(ContainerName).ConfigureAwait(false);
        var fileRepository = _unitOfWork.GetRepository<Domain.Entities.CaseDiscussionReplyFile>();

        if (currentData.Files?.Any() == true)
        {
            // 取得した添付ファイルをBlobから削除
            foreach (var file in currentData.Files)
            {
                // DB削除
                var deleteResult = await fileRepository.DeleteAsync(file).ConfigureAwait(false);
                if (deleteResult.IsError) return deleteResult.PreserveErrorAs<string>();

                var fileName = Path.GetFileName(file.FileName);
                var deleteFileResult = await storageClient.DeleteAsync($"case-discussion-reply/{currentData.Id}/{fileName}").ConfigureAwait(false);
                if (deleteFileResult.IsError) return deleteFileResult.PreserveErrorAs<string>();
            }
        }
        return Result.Ok("");
    }

    public async Task<Result<string>> UpdateCaseLastUpdatedAt(string caseId, DateTimeOffset updateTime)
    {
        // 案件更新情報のデータを取得
        var caseUpdateInformationRepository = _unitOfWork.GetRepository<Domain.Entities.CaseUpdateInformation>();
        var getResult = await caseUpdateInformationRepository.SingleAsync(
            new GetCaseUpdateInformationByCaseIdSpecification(caseId)
            ).ConfigureAwait(false);

        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        // 案件更新情報の最終更新日を更新
        var currentData = getResult.Get();
        currentData.LastUpdatedAt = updateTime;

        await caseUpdateInformationRepository
            .UpdateAsync(currentData)
            .ConfigureAwait(false);
        return Result.Ok("");
    }

    public async Task<Result<string>> ArrangeReplyContentByUrlWhenSaveAsync(string delta, string replyId)
    {
        return await _quillContentsUtility.ArrangeDeltaAndUploadContentsAsync(_objectStorageClientProvider, _baseUrl, ContainerName, GetImageAddress(replyId), delta);
    }

    public async Task<Result<string>> GetArrangedDeltaWhenPublish(string delta)
    {
        return await _quillContentsUtility.GetArrangedDeltaWhenPublish(delta);
    }

    public async Task<Result<string>> DeleteAllContentsAsync(string replyId)
    {
        return await _quillContentsUtility.DeleteAllContentsAsync(_objectStorageClientProvider, ContainerName, GetImageAddress(replyId));
    }

    private string GetImageAddress(string replyId)
    {
        return $"{ImageAddressPrefix}/{replyId}/discription";
    }
}
