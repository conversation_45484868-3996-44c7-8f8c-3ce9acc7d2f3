using BusinessCustomerUnderstandingService.Domain.Enums;
using BusinessCustomerUnderstandingService.Externals.CustomerIdentifiying;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.UseCases.CommercialDistribution.GetCommercialDistributionSupportingData;
using Microsoft.EntityFrameworkCore;
using Nut.Results;
using Shared.Messaging;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;

public class GetCommercialDistributionSupportingDataQueryService : IGetCommercialDistributionSupportingDataQueryService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMessageSender<FindBVCustomerByCustomerIdentificationIdQuery, List<FindBVCustomerByCustomerIdentificationIdResult>> _messageSender;

    public GetCommercialDistributionSupportingDataQueryService(
        ApplicationDbContext dbContext,
        IMessageSender<FindBVCustomerByCustomerIdentificationIdQuery, List<FindBVCustomerByCustomerIdentificationIdResult>> messageSender
        )
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        _messageSender = messageSender ?? throw new ArgumentNullException(nameof(messageSender));
    }

    public async Task<Result<IEnumerable<GetCommercialDistributionSupportingDataResult>>> Handle(GetCommercialDistributionSupportingDataQuery request)
    {
        // 商流図から対象のNodeと事業性理解IDを取得します
        var commercialDistributions = await _dbContext.CommercialDistributions
        .Where(x => x.BusinessUnderstandingId != request.BusinessUnderstandingId)
        .Select(x => new
        {
            BusinessUnderstandingId = x.BusinessUnderstandingId,
            Nodes = x.Nodes
        })
        .SelectMany(commercialDistribution => commercialDistribution.Nodes, (commercialDistribution, node) => new
        {
            BusinessUnderstandingId = commercialDistribution.BusinessUnderstandingId,
            Node = node,
        })
        .Where(n => n.Node.BranchNumber == request.BranchNumber && n.Node.CifNumber == request.CifNumber
        && (n.Node.NodeType == CommercialDistributionNodeType.Supplier // 仕入れ先
        || n.Node.NodeType == CommercialDistributionNodeType.Buyer) // 販売先
        )
        .AsNoTracking()
        .ToListAsync();

        if (!commercialDistributions.Any())
        {
            return Result.Ok(Enumerable.Empty<GetCommercialDistributionSupportingDataResult>());
        }

        // 顧客識別IDを取得
        var businessCustomers = _dbContext.BusinessUnderstandings
            .Where(x => x.Id != request.BusinessUnderstandingId)
            .Join(_dbContext.BusinessCustomers,
            bu => bu.BusinessCustomerId,
            bc => bc.Id,
            (bu, bc) => new
            {
                CustomerIdentificationId = bc.CustomerIdentificationId,
                BusinessUnderstandingId = bu.Id
            })
            .AsNoTracking()
            .ToList();

        // 事業性理解IDをキーに、対象の商流図の顧客識別IDを取得
        var customerIdentificationIds = businessCustomers
            .Join(commercialDistributions,
            bc => bc.BusinessUnderstandingId,
            cd => cd.BusinessUnderstandingId,
            (bc, cd) => new
            {
                CustomerIdentificationId = bc.CustomerIdentificationId,
            })
            .Select(x => x.CustomerIdentificationId)
            .ToList();

        // 顧客名を取得
        var customersResult = await _messageSender.SendAsync(new FindBVCustomerByCustomerIdentificationIdQuery
        {
            CustomerIdentificationIds = customerIdentificationIds
        });

        if (customersResult.IsError) return customersResult.PreserveErrorAs<IEnumerable<GetCommercialDistributionSupportingDataResult>>();
        var customers = customersResult.Get();

        // 「businessCustomers」と「customers」を結合して顧客名と紐づける
        var customerList = businessCustomers
            .Join(customers,
            bu => bu.CustomerIdentificationId,
            ct => ct.CustomerIdentificationId,
            (ci, ct) => new
            {
                CustomerIdentificationId = ci.CustomerIdentificationId,
                BusinessUnderstandingId = ci.BusinessUnderstandingId,
                NameKana = ct.NameKana,
                NameKanji = ct.NameKanji,
                BranchNumber = ct.BranchNumber,
                CifNumber = ct.CifNumber,
                MainIndustryCode = ct.MainIndustryCode,
            });

        return Result.Ok(commercialDistributions.Select(x =>
        {
            var customer = customerList.FirstOrDefault(cl => cl.BusinessUnderstandingId == x.BusinessUnderstandingId);
            return new GetCommercialDistributionSupportingDataResult(
            x.Node.Id,
            customer?.CustomerIdentificationId ?? Guid.Empty,
            customer?.BranchNumber ?? "",
            customer?.CifNumber ?? "",
            customer?.NameKana ?? "",
            customer?.NameKanji ?? "",
            customer?.MainIndustryCode ?? "",
            x.Node.CommercialDistributionId,
            x.Node.NodeType,
            x.Node.Title
            );
        }));
    }
}


