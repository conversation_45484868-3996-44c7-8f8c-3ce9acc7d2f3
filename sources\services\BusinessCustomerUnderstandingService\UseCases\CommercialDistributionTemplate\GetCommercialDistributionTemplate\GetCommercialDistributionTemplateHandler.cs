using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistributionTemplate.GetCommercialDistributionTemplate;

public class GetCommercialDistributionTemplateHandler : IRequestHandler<GetCommercialDistributionTemplateQuery, Result<GetCommercialDistributionTemplateResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetCommercialDistributionTemplateHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetCommercialDistributionTemplateResult>> Handle(GetCommercialDistributionTemplateQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CommercialDistributionTemplate, string>();

        return repository.SingleAsync(new GetCommercialDistributionTemplateSpecification(request.Id))
            .Map(v => new GetCommercialDistributionTemplateResult(
                    v.Id,
                    v.TemplateName,
                    v.CanvasColor,
                    v.Nodes,
                    v.Edges,
                    v.Version));
    }
}
