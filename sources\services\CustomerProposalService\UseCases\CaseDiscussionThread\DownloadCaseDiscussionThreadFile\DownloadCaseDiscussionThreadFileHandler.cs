using CustomerProposalService.Infrastructure.Storage;
using MediatR;
using Nut.Results;
using Shared.ObjectStorage;
using Shared.Results.Errors;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.DownloadCaseDiscussionThreadFile;
public class DownloadCaseDiscussionThreadFileHandler : IRequestHandler<DownloadCaseDiscussionThreadFileQuery, Result<DownloadCaseDiscussionThreadFileResult>>
{
    private readonly ICaseDiscussionThreadStorageClientProvider _objectStorageClientProvider;
    private readonly string _threadLinkedFilePathBase = "case-discussion-thread";

    public DownloadCaseDiscussionThreadFileHandler(ICaseDiscussionThreadStorageClientProvider objectStorageClientProvider)
    {
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
    }

    public async Task<Result<DownloadCaseDiscussionThreadFileResult>> Handle(DownloadCaseDiscussionThreadFileQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // ObjectStorageのクライアントを作成します。
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        // 保存されているオブジェクトの一覧を取得
        var objects = await storageClient.GetObjectInformationsAsync($"{_threadLinkedFilePathBase}/{request.CaseDiscussionThreadId}").ConfigureAwait(false);
        if (objects.IsError) return objects.PreserveErrorAs<DownloadCaseDiscussionThreadFileResult>();

        // 指定されたファイルを取得
        var target = objects.Get().Where(e => e.Name == $"{_threadLinkedFilePathBase}/{request.CaseDiscussionThreadId}/{request.FileName}").FirstOrDefault();
        if (target is null) return Result.Error<DownloadCaseDiscussionThreadFileResult>(new DataNotFoundException());

        var result = await storageClient.GetAsync(target.Name);
        return result.Map(s => new DownloadCaseDiscussionThreadFileResult(s));
    }
}
