using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.UpdateBusinessUnderstandingUpdatedDateTime;

public class UpdateBusinessUnderstandingUpdatedDateTimeValidator : AbstractValidator<UpdateBusinessUnderstandingUpdatedDateTimeCommand>
{
    public UpdateBusinessUnderstandingUpdatedDateTimeValidator()
    {
        RuleFor(v => v.BusinessUnderstandingId).NotEmpty();
        RuleFor(v => v.UpdaterId).NotEmpty();
        RuleFor(v => v.UpdaterName).NotEmpty();
    }
}
