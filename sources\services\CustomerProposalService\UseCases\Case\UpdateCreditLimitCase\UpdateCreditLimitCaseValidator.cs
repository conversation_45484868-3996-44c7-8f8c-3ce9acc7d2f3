using FluentValidation;

namespace CustomerProposalService.UseCases.Case.UpdateCreditLimitCase;

public class UpdateCreditLimitCaseValidator : AbstractValidator<UpdateCreditLimitCaseCommand>
{
    public UpdateCreditLimitCaseValidator()
    {
        // Case Entity
        RuleFor(e => e.Id).NotEmpty();
        RuleFor(e => e.CaseName).NotEmpty().MaximumLength(50);
        RuleFor(e => e.CaseStatus).NotEmpty().IsInEnum().NotEqual(Domain.Enums.CaseStatus.Undefined);
        RuleFor(e => e.CaseOutline).CaseOutline();
        RuleFor(e => e.StaffId).NotEmpty().MaximumLength(50);
        RuleFor(e => e.StaffName).NotEmpty().MaximumLength(50);
        RuleFor(e => e.Version).NotEmpty();
        // CreditLimitCase Entity
        RuleFor(e => e.AccountType).MaximumLength(50);
        RuleFor(e => e.InterestRateCustom).MaximumLength(50);
        RuleFor(e => e.ApplicableBaseInterestRate).MaximumLength(50);
        RuleFor(e => e.LoanPurposeCode).MaximumLength(50);
        RuleFor(e => e.LoanPurposeCustom).MaximumLength(50);
        RuleFor(e => e.OnSitePhysicalConfirmer).MaximumLength(50);
        RuleFor(e => e.RepaymentType).MaximumLength(50);
        RuleFor(e => e.CollateralType).MaximumLength(50);
        RuleFor(e => e.CollateralOrGuaranteeCustom).MaximumLength(50);
        RuleFor(e => e.GuaranteeType).MaximumLength(50);
        RuleFor(e => e.CreditLimitProductCode).MaximumLength(50);
        RuleFor(e => e.CancelTypeOfLoan).IsInEnum();
        RuleFor(e => e.CancelReason).CancelReason();
        RuleFor(e => e.TrafficSource).IsInEnum();
    }
}
