using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;
using Shared.AzureBlob.QuillContents;
using Shared.Domain;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThread.FindBusinessUnderstandingThread;

public class FindBusinessUnderstandingThreadHandler : IRequestHandler<FindBusinessUnderstandingThreadQuery, Result<IEnumerable<FindBusinessUnderstandingThreadResult>>>
{
    private readonly IRepository<Domain.Entities.BusinessUnderstandingThread> _repository;
    private readonly IQuillContentsUtility _quillContentsUtility;

    public FindBusinessUnderstandingThreadHandler(IRepositoryFactory repositoryFactory, IQuillContentsUtility quillContentsUtility)
    {
        if (repositoryFactory is null) throw new ArgumentNullException(nameof(repositoryFactory));
        _repository = repositoryFactory.GetRepository<Domain.Entities.BusinessUnderstandingThread>();
        _quillContentsUtility = quillContentsUtility ?? throw new ArgumentNullException(nameof(quillContentsUtility));
    }

    public async Task<Result<IEnumerable<FindBusinessUnderstandingThreadResult>>> Handle(FindBusinessUnderstandingThreadQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));
        // 検索を行います。
        var spec = new FindBusinessUnderstandingThreadSpecification(
            request.BusinessUnderstandingId,
            request.RegisteredDateTimeFrom,
            request.RegisteredDateTimeTo);

        var repositoryResult = await _repository.FindAsync(spec);
        if (repositoryResult.IsError) return repositoryResult.PreserveErrorAs<IEnumerable<FindBusinessUnderstandingThreadResult>>();

        var findBusinessUnderstandingThreadResults = new List<FindBusinessUnderstandingThreadResult>();
        foreach (var businessUnderstandingThread in repositoryResult.Get())
        {
            var newDelta = await _quillContentsUtility.GetArrangedDeltaWhenPublish(businessUnderstandingThread.Description);
            if (newDelta.IsError) return Result.Error<IEnumerable<FindBusinessUnderstandingThreadResult>>(newDelta.GetError());
            var result = new FindBusinessUnderstandingThreadResult(
                businessUnderstandingThread.Id,
                businessUnderstandingThread.RegisteredDateTime,
                businessUnderstandingThread.Title,
                businessUnderstandingThread.Registrant,
                businessUnderstandingThread.RegistrantId,
                newDelta.Get(),
                businessUnderstandingThread.BusinessUnderstandingId,
                businessUnderstandingThread.Reactions,
                businessUnderstandingThread.Discussions,
                businessUnderstandingThread.Files,
                businessUnderstandingThread.MentionTargetsHtml,
                businessUnderstandingThread.MentionTargetUserIds,
                businessUnderstandingThread.MentionTargetTeamIds,
                businessUnderstandingThread.Purpose,
                businessUnderstandingThread.Person,
                businessUnderstandingThread.IsPersonOfPower,
                businessUnderstandingThread.CorrespondenceDate,
                businessUnderstandingThread.IsCorporateDepositTheme,
                businessUnderstandingThread.IsFundSettlementTheme,
                businessUnderstandingThread.Version);

            findBusinessUnderstandingThreadResults.Add(result);
        }

        return (Result<IEnumerable<FindBusinessUnderstandingThreadResult>>)findBusinessUnderstandingThreadResults;
    }
}
