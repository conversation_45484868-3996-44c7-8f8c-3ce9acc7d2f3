using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartnershipAndCreativeAccountingDetail.UpdateBusinessPartnershipAndCreativeAccountingDetail;

[WithDefaultBehaviors]
public record UpdateBPAndCADetailCommand(
    string Id,
    bool? HasBusinessPartnershipWithOurCompany,
    string? FeatureOfBusinessPartnership,
    bool? HasCreativeAccountingIncident,
    string? DescriptionOfCreativeAccountingIncident,
    string StaffId,
    string StaffName,
    string Version
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
