using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class ExternalEnvironmentHistoryConfiguration : IEntityTypeConfiguration<ExternalEnvironmentHistory>
{
    public void Configure(EntityTypeBuilder<ExternalEnvironmentHistory> builder)
    {
        builder.HasOne<ExternalEnvironment>()
            .WithMany(entity => entity.Histories)
            .HasForeignKey(entity => entity.OriginalId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .HasMaxLength(26);
    }
}
