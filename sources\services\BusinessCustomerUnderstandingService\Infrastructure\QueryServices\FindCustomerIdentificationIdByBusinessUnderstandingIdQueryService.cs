using Microsoft.EntityFrameworkCore;
using Nut.Results;
using BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindCustomerIdentificationIdsByBusinessUnderstandingIds;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;

internal class FindCustomerIdentificationIdsByBusinessUnderstandingIdsQueryService : IFindCustomerIdentificationIdsByBusinessUnderstandingIdsQueryService
{
    private readonly ApplicationDbContext _dbContext;

    public FindCustomerIdentificationIdsByBusinessUnderstandingIdsQueryService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    public async Task<Result<List<FindCustomerIdentificationIdsByBusinessUnderstandingIdsQueryServiceResult>>> Handle(FindCustomerIdentificationIdsByBusinessUnderstandingIdsQuery request)
    {
        var maxCount = 1000; // IN句1000件制限
        var customerIdentificationIds = new List<FindCustomerIdentificationIdsByBusinessUnderstandingIdsQueryServiceResult>();

        // 1000件の塊がいくつあるか
        var chunkCount = (request.BusinessUnderstandingIds.Any()) ? request.BusinessUnderstandingIds.Count / maxCount : 0;
        if (0 < chunkCount)
        {
            for (var i = 0; i < chunkCount; i++)
            {
                var getResult = await GetBusinessUnderstandings(request.BusinessUnderstandingIds, i * maxCount, maxCount);
                customerIdentificationIds.AddRange(getResult.ToArray());
            }
        }

        // 1000件に満たないデータの取得
        var remainder = request.BusinessUnderstandingIds.Count % maxCount;
        if (remainder != 0)
        {
            var getResult = await GetBusinessUnderstandings(request.BusinessUnderstandingIds, chunkCount * maxCount, maxCount);
            customerIdentificationIds.AddRange(getResult.ToArray());
        }

        return Result.Ok(customerIdentificationIds);
    }

    private async Task<List<FindCustomerIdentificationIdsByBusinessUnderstandingIdsQueryServiceResult>> GetBusinessUnderstandings(List<string> ids, int index, int takeCount)
    {
        return await _dbContext.BusinessUnderstandings
            .Where(bu => ids.Skip(index).Take(takeCount).Contains(bu.Id))
            .Join(_dbContext.BusinessCustomers,
            bu => bu.BusinessCustomerId,
            bc => bc.Id,
            (bu, bc) =>
            new FindCustomerIdentificationIdsByBusinessUnderstandingIdsQueryServiceResult(
                    bu.Id,
                    bc.Id,
                    bc.CustomerIdentificationId
                )
            ).ToListAsync();

    }
}
