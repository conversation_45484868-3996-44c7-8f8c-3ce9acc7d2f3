using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.GetNewLeaseCase;

public class GetNewLeaseCaseHandler : IRequestHandler<GetNewLeaseCaseQuery, Result<GetNewLeaseCaseResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetNewLeaseCaseHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetNewLeaseCaseResult>> Handle(GetNewLeaseCaseQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.NewLeaseCase, string>();
        return repository.SingleAsync(new GetNewLeaseCaseSpecification(request))
            .Map(v =>
                new GetNewLeaseCaseResult
                (
                    // Case Entity
                    v.Id,
                    v.CustomerIdentificationId,
                    v.CaseCategory,
                    v.CaseName,
                    v.CaseStatus,
                    v.CaseOutline,
                    v.ExpiredAt,
                    v.StaffId,
                    v.StaffName,
                    v.RegisteredAt,
                    v.CaseUpdatedAt,
                    v.CaseUpdateInformation.LastUpdatedAt,
                    v.CaseFiles,
                    v.CaseLinks,
                    v.Version,
                    // NewLeaseCase Entity
                    v.LeaseCaseType,
                    v.Amount,
                    v.LeaseSituations,
                    v.LeaseStaffId,
                    v.LeaseStaffName,
                    v.CancelType,
                    v.CancelReason,
                    v.PropertyCategory,
                    v.PropertyStatus,
                    v.PropertyContractTypes,
                    v.ContractScheduledAt,
                    v.BusinessMeetingNumber,
                    v.QuotationNumber,
                    v.LeaseDetails,
                    v.PaymentCycle,
                    v.ResidualValueSettings,
                    v.QuotationProcurement,
                    v.ScheduledMonthlyMileage,
                    v.InstallationLocation,
                    v.InstallationLocationCustom,
                    v.QuotationNote,
                    v.ContractDate,
                    v.AcceptanceCertificateDate,
                    v.CaseInputStatus,
                    v.CaseInputCompletedAt,
                    v.IsConsultationTarget,
                    v.ConsultationStatus,
                    v.ConsultationCompletedAt,
                    v.QuotationCreateStaffId,
                    v.QuotationCreateStaffName,
                    v.QuotationCreateStatus,
                    v.QuotationCreateCompletedAt,
                    v.QuotationScrutinizeStaffId,
                    v.QuotationScrutinizeStaffName,
                    v.QuotationScrutinizeStatus,
                    v.QuotationScrutinizeCompletedAt,
                    v.CustomerProposalResult,
                    v.CustomerProposalCompletedAt,
                    v.IndividualApplicationStaffId,
                    v.IndividualApplicationStaffName,
                    v.IndividualApplicationStatus,
                    v.IndividualApplicationCompletedAt,
                    v.IsEarthquakeRelated,
                    v.TrafficSource
                    ));
    }
}
