using MediatR;
using Nut.Results;
using Shared.Application;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThread.FindBusinessUnderstandingThread;

[WithDefaultBehaviors]
public record FindBusinessUnderstandingThreadQuery : PageQuery, IRequest<Result<IEnumerable<FindBusinessUnderstandingThreadResult>>>
{
    public string? BusinessUnderstandingId { get; set; } = default!;

    public DateTimeOffset? RegisteredDateTimeFrom { get; set; }

    public DateTimeOffset? RegisteredDateTimeTo { get; set; }
}
