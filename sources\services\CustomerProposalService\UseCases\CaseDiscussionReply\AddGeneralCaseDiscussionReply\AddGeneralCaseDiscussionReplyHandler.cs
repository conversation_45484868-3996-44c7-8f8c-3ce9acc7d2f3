using CustomerProposalService.Domain;
using CustomerProposalService.Services.Domain;
using MediatR;
using Nut.Results;
using Shared.Services;

namespace CustomerProposalService.UseCases.CaseDiscussionReply.AddGeneralCaseDiscussionReply;

public class AddGeneralCaseDiscussionReplyHandler : IRequestHandler<AddGeneralCaseDiscussionReplyCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDateTimeService;
    private readonly ICaseDiscussionReplyUtility _utility;
    private readonly IFileProcessingService _fileProcessingService;

    private readonly string _containerName = "case-discussion";
    private readonly string _folderName = "case-discussion-reply";

    public AddGeneralCaseDiscussionReplyHandler(
        IUnitOfWork unitOfWork,
        ICurrentDateTimeService currentDateTimeService,
        ICaseDiscussionReplyUtility utility,
        IFileProcessingService fileProcessingService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
        _utility = utility ?? throw new ArgumentNullException(nameof(utility));
        _fileProcessingService = fileProcessingService ?? throw new ArgumentNullException(nameof(fileProcessingService));
    }

    public async Task<Result<string>> Handle(AddGeneralCaseDiscussionReplyCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var now = _currentDateTimeService.NowDateTimeOffset();

        // 更新する値を作成します。
        var newData = new Domain.Entities.CaseDiscussionReply()
        {
            // キーの値をUlidで生成します。
            Id = Ulid.NewUlid().ToString(),
            CaseDiscussionThreadId = request.CaseDiscussionThreadId,
            RegistrantId = request.RegistrantId,
            RegistrantName = request.RegistrantName,
            RegisteredAt = now,
            Purpose = request.Purpose,
            Person = request.Person,
            IsPersonOfPower = request.IsPersonOfPower,
            MentionTargetUserIds = request.MentionTargetUserIds,
            MentionTargetTeamIds = request.MentionTargetTeamIds,
            Description = request.Description,
        };

        // 案件更新情報の最終更新日を更新する
        var updateResult = await _utility.UpdateCaseLastUpdatedAt(request.CaseId, now);
        if (updateResult.IsError) return updateResult;

        // ファイルをアップロードします。
        if (request.UploadFiles != null)
        {
            var uploadResult = await _fileProcessingService.UpdateFiles<Domain.Entities.CaseDiscussionReply, Domain.Entities.CaseDiscussionReplyFile>(newData, _containerName, _folderName, request.UploadFiles, null);
            if (uploadResult.IsError) return uploadResult;
        }

        // 本文添付ファイルの処理
        var newDelta = await _utility.ArrangeReplyContentByUrlWhenSaveAsync(request.Description, newData.Id);
        if (newDelta.IsError) return newDelta;
        newData.Description = newDelta.Get();

        // 通知処理を行います。
        await _utility.SendNotifyAsync(newData, request.MentionTargetTeamMemberUserIds, request.CaseId, request.CustomerName, request.CustomerStaffId).ConfigureAwait(false);

        var repository = _unitOfWork.GetRepository<Domain.Entities.CaseDiscussionReply>();
        return await repository
            .AddAsync(newData) // データを追加します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(newData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
