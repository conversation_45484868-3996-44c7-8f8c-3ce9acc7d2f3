using FluentValidation;

namespace CustomerProposalService.UseCases.CaseDiscussionThreadReaction.AddCaseDiscussionThreadReaction;

public class AddCaseDiscussionThreadReactionValidator : AbstractValidator<AddCaseDiscussionThreadReactionCommand>
{
    public AddCaseDiscussionThreadReactionValidator()
    {
        RuleFor(v => v.CaseDiscussionThreadId).NotEmpty();
        RuleFor(v => v.ReactionType).IsInEnum();
        RuleFor(v => v.UpdaterId).NotEmpty();
        RuleFor(v => v.UpdaterName).NotEmpty();
        RuleFor(v => v.UpdatedDateTime).NotEmpty();
    }
}
