using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;

public class SharingOfFinanceThreadFileConfiguration : IEntityTypeConfiguration<SharingOfFinanceThreadFile>
{
    public void Configure(EntityTypeBuilder<SharingOfFinanceThreadFile> builder)
    {
        builder.HasOne<SharingOfFinanceThread>()
            .WithMany(l => l.Files)
            .HasForeignKey(j => j.ThreadId)
            .HasConstraintName("fk_sharing_of_finance_thread_sharing_of_finance_thread_file_id")
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.FileName)
            .IsRequired()
            .HasMaxLength(300);
    }
}
