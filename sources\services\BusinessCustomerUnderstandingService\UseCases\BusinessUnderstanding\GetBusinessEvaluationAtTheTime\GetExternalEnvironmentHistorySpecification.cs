using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessEvaluationAtTheTime;

public class GetExternalEnvironmentHistorySpecification : BaseSpecification<Domain.Entities.ExternalEnvironmentHistory>
{
    public GetExternalEnvironmentHistorySpecification(string originalId, DateTimeOffset targetDateTime)
    {
        Query.Where(x => x.OriginalId == originalId && x.UpdatedDateTime == targetDateTime).AsNoTracking();
    }
}
