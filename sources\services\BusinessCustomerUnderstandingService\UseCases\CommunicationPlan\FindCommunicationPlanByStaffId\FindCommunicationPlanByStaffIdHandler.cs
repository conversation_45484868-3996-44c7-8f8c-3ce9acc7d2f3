using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Domain.Enums;
using BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.FindCommunicationPlanByStaffIds;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.FindCommunicationPlanByStaffId;

public class FindCommunicationPlanByStaffIdHandler : IRequestHandler<FindCommunicationPlanByStaffIdQuery, Result<IEnumerable<FindCommunicationPlanByStaffIdResult>>>
{
    private readonly IUnitOfWork _unitOfWork;

    public FindCommunicationPlanByStaffIdHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<IEnumerable<FindCommunicationPlanByStaffIdResult>>> Handle(FindCommunicationPlanByStaffIdQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var list = new List<FindCommunicationPlanByStaffIdResult>();

        // 当社の方針
        var ourPolicyRepository = _unitOfWork.GetRepository<Domain.Entities.OurPolicyUnderstanding>();
        var getOurPolicyResult = await ourPolicyRepository.FindAsync(new FindOurPolicyUnderstandingByStaffIdSpecification(request.StaffId));
        if (getOurPolicyResult.IsError) return getOurPolicyResult.PreserveErrorAs<IEnumerable<FindCommunicationPlanByStaffIdResult>>();
        var ourPolicyList = getOurPolicyResult.Get();
        foreach (var ourpolicy in ourPolicyList)
        {
            var task = new FindCommunicationPlanByStaffIdResult(
                ourpolicy.Id,
                CommunicationPlanType.OurPolicyUnderstanding,
                ourpolicy.Title,
                ourpolicy.StaffId,
                ourpolicy.StaffName,
                ourpolicy.ExpiredAt,
                ourpolicy.BusinessUnderstandingId);
            list.Add(task);
        }

        // お客さまの考え方
        var customerIdeasRepository = _unitOfWork.GetRepository<Domain.Entities.CustomerIdeasUnderstanding>();
        var customerIdeasResult = await customerIdeasRepository.FindAsync(new FindCustomerIdeasUnderstandingByStaffIdSpecification(request.StaffId));
        if (customerIdeasResult.IsError) return customerIdeasResult.PreserveErrorAs<IEnumerable<FindCommunicationPlanByStaffIdResult>>();
        var customerIdeasList = customerIdeasResult.Get();
        foreach (var customerIdeas in customerIdeasList)
        {
            var task = new FindCommunicationPlanByStaffIdResult(
                customerIdeas.Id,
                CommunicationPlanType.CustomerIdeasUnderstanding,
                customerIdeas.Title,
                customerIdeas.StaffId,
                customerIdeas.StaffName,
                customerIdeas.ExpiredAt,
                customerIdeas.BusinessUnderstandingId);
            list.Add(task);
        }

        // 財務の共有
        var sharingOfFinanceRepository = _unitOfWork.GetRepository<Domain.Entities.SharingOfFinance>();
        var sharingOfFinanceResult = await sharingOfFinanceRepository.FindAsync(new FindSharingOfFinanceByStaffIdSpecification(request.StaffId));
        if (sharingOfFinanceResult.IsError) return sharingOfFinanceResult.PreserveErrorAs<IEnumerable<FindCommunicationPlanByStaffIdResult>>();
        var sharingOfFinanceList = sharingOfFinanceResult.Get();
        foreach (var sharingOfFinance in sharingOfFinanceList)
        {
            var task = new FindCommunicationPlanByStaffIdResult(
                sharingOfFinance.Id,
                CommunicationPlanType.SharingOfFinance,
                sharingOfFinance.Title,
                sharingOfFinance.StaffId,
                sharingOfFinance.StaffName,
                sharingOfFinance.ExpiredAt,
                sharingOfFinance.BusinessUnderstandingId);
            list.Add(task);
        }

        // 課題の仮説協議
        var hypotheticalDiscussionRepository = _unitOfWork.GetRepository<Domain.Entities.HypotheticalDiscussionOfIssues>();
        var hypotheticalDiscussionResult = await hypotheticalDiscussionRepository.FindAsync(new FindHypotheticalDiscussionOfIssuesByStaffIdSpecification(request.StaffId));
        if (hypotheticalDiscussionResult.IsError) return hypotheticalDiscussionResult.PreserveErrorAs<IEnumerable<FindCommunicationPlanByStaffIdResult>>();
        var hypotheticalDiscussionList = hypotheticalDiscussionResult.Get();
        foreach (var hypotheticalDiscussion in hypotheticalDiscussionList)
        {
            var task = new FindCommunicationPlanByStaffIdResult(
                hypotheticalDiscussion.Id,
                CommunicationPlanType.HypotheticalDiscussionOfIssues,
                hypotheticalDiscussion.Title,
                hypotheticalDiscussion.StaffId,
                hypotheticalDiscussion.StaffName,
                hypotheticalDiscussion.ExpiredAt,
                hypotheticalDiscussion.BusinessUnderstandingId);
            list.Add(task);
        }

        // ToDo
        var toDoRepository = _unitOfWork.GetRepository<Domain.Entities.ToDo>();
        var toDoResult = await toDoRepository.FindAsync(new FindToDoByStaffIdSpecification(request.StaffId));
        if (toDoResult.IsError) return toDoResult.PreserveErrorAs<IEnumerable<FindCommunicationPlanByStaffIdResult>>();
        var toDoList = toDoResult.Get();
        foreach (var toDo in toDoList)
        {
            var task = new FindCommunicationPlanByStaffIdResult(
                toDo.Id,
                CommunicationPlanType.ToDo,
                toDo.Title,
                toDo.StaffId,
                toDo.StaffName,
                toDo.ExpiredAt,
                toDo.BusinessUnderstandingId);
            list.Add(task);
        }

        return list;
    }
}
