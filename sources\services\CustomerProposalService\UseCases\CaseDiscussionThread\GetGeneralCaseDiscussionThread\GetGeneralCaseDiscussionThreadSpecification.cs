using Shared.Spec;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.GetGeneralCaseDiscussionThread;

public class GetGeneralCaseDiscussionThreadSpecification : BaseSpecification<Domain.Entities.GeneralCaseDiscussionThread>
{
    public GetGeneralCaseDiscussionThreadSpecification(GetGeneralCaseDiscussionThreadQuery request)
    {
        Query
            .Where(x => x.Id == request.Id)
            .Include(x => x.Files)
            .AsNoTracking();
    }
}
