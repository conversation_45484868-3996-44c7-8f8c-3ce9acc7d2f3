using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindBusinessEvaluationUpdatedDateTimeHistory;

public class FindBusinessEvaluationUpdatedDateTimeHistoryHandler : IRequestHandler<FindBusinessEvaluationUpdatedDateTimeHistoryQuery, Result<List<FindBusinessEvaluationUpdatedDateTimeHistoryResult>>>
{
    private readonly IUnitOfWork _unitOfWork;

    public FindBusinessEvaluationUpdatedDateTimeHistoryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<List<FindBusinessEvaluationUpdatedDateTimeHistoryResult>>> Handle(FindBusinessEvaluationUpdatedDateTimeHistoryQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var spec = new FindBusinessEvaluationUpdatedDateTimeHistorySpecification(request.Id);

        // 経営計画～外部環境の履歴は全て同じ更新日時で作成されるため代表して経営計画の履歴を取得する
        var repository = _unitOfWork.GetRepository<Domain.Entities.ManagementPlan, string>();
        var getManagementPlanResult = await repository.SingleAsync(spec).ConfigureAwait(false);
        if (getManagementPlanResult.IsError) return getManagementPlanResult.PreserveErrorAs<List<FindBusinessEvaluationUpdatedDateTimeHistoryResult>>();
        var management = getManagementPlanResult.Get();

        return Result.Ok(management.Histories
            .Select(x => new FindBusinessEvaluationUpdatedDateTimeHistoryResult(
                x.UpdatedDateTime,
                x.UpdaterName))
            .OrderByDescending(x=>x.UpdatedDateTime).ToList()
            );
    }
}
