using Microsoft.EntityFrameworkCore;
using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistribution.GetCommercialDistribution;

public class GetCommercialDistributionSpecification : BaseSpecification<Domain.Entities.CommercialDistribution>
{
    public GetCommercialDistributionSpecification(string id)
    {
        Query
            .Where(x => x.Id == id)
            .Include(x => x.Nodes)
            .Include(x => x.Edges)
            .AsNoTracking();
    }
}
