using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;
using Shared.Services;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingMaterializedViewQueue.AddBusinessUnderstandingMaterializedViewQueue;

public class AddBusinessUnderstandingMaterializedViewQueueHandler : IRequestHandler<AddBusinessUnderstandingMaterializedViewQueueCommand, Result<AddBusinessUnderstandingMaterializedViewQueueResult>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDateTimeService;

    public AddBusinessUnderstandingMaterializedViewQueueHandler(
        IUnitOfWork unitOfWork,
        ICurrentDateTimeService currentDateTimeService
        )
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
    }

    public async Task<Result<AddBusinessUnderstandingMaterializedViewQueueResult>> Handle(AddBusinessUnderstandingMaterializedViewQueueCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var now = _currentDateTimeService.NowDateTimeOffset();

        var bizUnderstandingRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding>();
        var getBizUnderstandingResult = await bizUnderstandingRepository.FindAsync(
            new FindBusinessUnderstandingSpecification(request.CustomerIdentificationIds)
            ).ConfigureAwait(false);
        if (getBizUnderstandingResult.IsError)
        {
            return getBizUnderstandingResult.PreserveErrorAs<AddBusinessUnderstandingMaterializedViewQueueResult>();
        }
        var bizUnderstandings = getBizUnderstandingResult.Get();

        var queueRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingMaterializedViewQueue>();
        foreach (var bizUnderstanding in bizUnderstandings)
        {
            var queue = new Domain.Entities.BusinessUnderstandingMaterializedViewQueue()
            {
                Id = Ulid.NewUlid().ToString(),
                BusinessUnderstandingId = bizUnderstanding.Id,
                CustomerIdentificationId = bizUnderstanding.BusinessCustomer!.CustomerIdentificationId,
                NumberOfRetries = 0,
                UpdaterId = request.UpdaterId,
                UpdaterName = request.UpdaterName,
                UpdatedDateTime = now
            };

            await queueRepository.AddAsync(queue).ConfigureAwait(false);
        }

        var saveResult = await _unitOfWork.SaveEntitiesAsync().ConfigureAwait(false);
        if (saveResult.IsError) return saveResult.PreserveErrorAs<AddBusinessUnderstandingMaterializedViewQueueResult>();

        var result = new AddBusinessUnderstandingMaterializedViewQueueResult() { UpdatedCount = bizUnderstandings.Count };
        return Result.Ok(result);
    }
}
