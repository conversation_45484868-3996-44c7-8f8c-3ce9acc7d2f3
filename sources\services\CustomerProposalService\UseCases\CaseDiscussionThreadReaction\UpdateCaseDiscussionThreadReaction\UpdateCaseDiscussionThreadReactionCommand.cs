using CustomerProposalService.Domain.Enums;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionThreadReaction.UpdateCaseDiscussionThreadReaction;

[WithDefaultBehaviors]
public record UpdateCaseDiscussionThreadReactionCommand(
    string Id,
    ReactionType ReactionType,
    DateTimeOffset UpdatedDateTime,
    string Version
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
