using Dto = BusinessCustomerUnderstandingService.Domain.Dto.BusinessUnderstanding;
using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessUnderstandingByBusinessCustomerId;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessUnderstanding;

public class GetBusinessUnderstandingByBusinessCustomerIdHandler : IRequestHandler<GetBusinessUnderstandingByBusinessCustomerIdQuery, Result<GetBusinessUnderstandingByBusinessCustomerIdResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetBusinessUnderstandingByBusinessCustomerIdHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetBusinessUnderstandingByBusinessCustomerIdResult>> Handle(GetBusinessUnderstandingByBusinessCustomerIdQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding, string>();

        return repository.SingleAsync(new GetBusinessUnderstandingByBusinessCustomerIdSpecification(request.BusinessCustomerId))
            .Map(v => new GetBusinessUnderstandingByBusinessCustomerIdResult
            (
                v.Id,
                new Dto.ManagementPlan(v.ManagementPlan!),
                new Dto.Management(v.Management!),
                new Dto.FiveForceFramework(v.FiveForceFramework!),
                new Dto.FiveStepFrameWork(v.FiveStepFrameWork!),
                new Dto.ESGAndSDGs(v.ESGAndSDGs!),
                new Dto.ExternalEnvironment(v.ExternalEnvironment!),
                new Dto.RelationLevel(v.RelationLevel!),
                new Dto.ThreeCAnalysis(v.ThreeCAnalysis!),
                new Dto.SWOTAnalysis(v.SWOTAnalysis!),
                new Dto.CommercialDistribution(v.CommercialDistribution!),
                new Dto.FamilyTree(v.FamilyTree!),
                v.BusinessUnderstandingMaterializedView is not null ? v.BusinessUnderstandingMaterializedView.TransactionPolicy : string.Empty,
                v.TransactionPolicyConfirmerId,
                v.TransactionPolicyConfirmedDateTime,
                v.BusinessCustomerId,
                v.Version));
    }
}
