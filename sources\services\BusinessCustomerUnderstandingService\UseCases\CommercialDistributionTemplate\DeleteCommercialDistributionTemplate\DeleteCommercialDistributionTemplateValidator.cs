using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistributionTemplate.DeleteCommercialDistributionTemplate;

public class DeleteCommercialDistributionTemplateValidator : AbstractValidator<DeleteCommercialDistributionTemplateCommand>
{
    public DeleteCommercialDistributionTemplateValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.Version).NotEmpty();
    }
}
