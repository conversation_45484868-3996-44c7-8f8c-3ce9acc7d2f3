using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;

public class ExternalEnvironmentMasterUploadProgressConfiguration : IEntityTypeConfiguration<ExternalEnvironmentMasterUploadProgress>
{
    public void Configure(EntityTypeBuilder<ExternalEnvironmentMasterUploadProgress> builder)
    {
        builder.Property(u => u.Id)
            .HasMaxLength(26);

        builder.Property(u => u.ResultMessage)
            .HasMaxLength(100);
    }
}
