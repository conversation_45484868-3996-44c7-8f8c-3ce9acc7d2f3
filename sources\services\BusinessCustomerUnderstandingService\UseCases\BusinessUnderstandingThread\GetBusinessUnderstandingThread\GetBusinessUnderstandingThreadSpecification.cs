using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThread.GetBusinessUnderstandingThread;
public class GetBusinessUnderstandingThreadSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingThread>
{
    public GetBusinessUnderstandingThreadSpecification(string id)
    {
        Query
            .Where(e => e.Id == id)
            .Include(x => x.Discussions, x => x.ThenInclude(x => x!.Files))
            .Include(x => x.Discussions, x => x.ThenInclude(x => x!.Reactions))
            .Include(x => x.Files)
            .Include(x => x.Reactions)
            .AsNoTracking();
    }
}
