using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingFile.GetBUFByBusinessUnderstandingId;
public class GetBUFByBusinessUnderstandingIdSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingFile>
{
    public GetBUFByBusinessUnderstandingIdSpecification(string businessUnderstandingId)
    {
        Query
            .Where(x => x.BusinessUnderstandingId == businessUnderstandingId)
            .AsNoTracking();
    }
}
