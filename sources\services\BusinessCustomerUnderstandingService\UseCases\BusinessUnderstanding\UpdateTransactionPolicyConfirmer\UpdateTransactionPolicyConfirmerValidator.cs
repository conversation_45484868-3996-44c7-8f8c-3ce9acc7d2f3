using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.UpdateTransactionPolicyConfirmer;

public class UpdateTransactionPolicyConfirmerValidator : AbstractValidator<UpdateTransactionPolicyConfirmerCommand>
{
    public UpdateTransactionPolicyConfirmerValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.UpdaterId).NotEmpty();
        RuleFor(v => v.UpdaterName).NotEmpty();
        RuleFor(v => v.Version).NotEmpty();       
    }
}
