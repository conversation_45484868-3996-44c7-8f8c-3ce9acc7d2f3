using CustomerProposalService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.UpdateLoanCaseDiscussionThread;

[WithDefaultBehaviors]
public record UpdateLoanCaseDiscussionThreadCommand(
    string Id,
    Guid CustomerIdentificationId,
    string CustomerName,
    string CustomerStaffId,
    string RegistrantId,
    string ThreadName,
    CaseDiscussionPurpose Purpose,
    string? Person,
    bool? IsPersonOfPower,
    string ReasonForGuaranty,
    string HowToImproveForGuaranty,
    string? MentionTargetsHtml,
    IEnumerable<string>? MentionTargetUserIds,
    IEnumerable<string>? MentionTargetTeamIds,
    IEnumerable<string>? MentionTargetTeamMemberUserIds,
    string Description,
    IEnumerable<IFormFile>? UploadFiles,
    IEnumerable<string>? FilesToRemove,
    string Version
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
