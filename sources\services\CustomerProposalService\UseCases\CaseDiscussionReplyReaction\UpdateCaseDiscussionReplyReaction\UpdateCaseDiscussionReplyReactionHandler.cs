using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionReplyReaction.UpdateCaseDiscussionReplyReaction;

public class UpdateCaseDiscussionReplyReactionHandler : IRequestHandler<UpdateCaseDiscussionReplyReactionCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;

    public UpdateCaseDiscussionReplyReactionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<string>> Handle(UpdateCaseDiscussionReplyReactionCommand request, CancellationToken cancellationToken)
    {
        if (request is null) throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CaseDiscussionReplyReaction, string>();
        var getResult = await repository.GetAsync(request.Id, true);
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();
        currentData.ReactionType = request.ReactionType;
        currentData.UpdatedDateTime = request.UpdatedDateTime;
        currentData.Version = request.Version;

        return await repository
            .UpdateAsync(currentData)
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync())
            .FlatMap(() => Result.Ok(currentData.Id))
            .ConfigureAwait(false);
    }
}
