using Nut.MediatR;
using Shared.Services;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussion.UpdateBusinessUnderstandingDiscussion;
public class UpdateBusinessUnderstandingDiscussionAuthorizer : IAuthorizer<UpdateBusinessUnderstandingDiscussionCommand>
{
    private readonly ICurrentUserService _currentUserService;

    public UpdateBusinessUnderstandingDiscussionAuthorizer(ICurrentUserService currentUserService)
    {
        _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
    }
    public async Task<AuthorizationResult> AuthorizeAsync(UpdateBusinessUnderstandingDiscussionCommand request, CancellationToken cancellationToken)
    {
        var currentUser = await _currentUserService.GetAsync().ConfigureAwait(false);

        if (currentUser.UserId == request.RegistrantId)
        {
            return AuthorizationResult.Success();
        }

        return AuthorizationResult.Failed("不正なリクエストです。");
    }
}
