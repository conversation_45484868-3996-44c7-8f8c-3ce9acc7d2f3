using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussion.AddBusinessUnderstandingDiscussion;
public class AddBusinessUnderstandingDiscussionSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingDiscussion>
{
    public AddBusinessUnderstandingDiscussionSpecification(string threadId)
    {
        Query
            .Where(x => x.ThreadId == threadId)
            .AsNoTracking();
    }
}
