using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Externals.BusinessCustomerUnderstanding;
using MediatR;
using Nut.Results;
using Shared.Domain;
using Shared.Messaging;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartner.UpdateBusinessPartner;

public class UpdateBusinessPartnerHandler : IRequestHandler<UpdateBusinessPartnerCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageSender<UpdateBusinessUnderstandingUpdatedDateTime> _businessUnderstandingUpdateMessageSender;

    public UpdateBusinessPartnerHandler(IUnitOfWork unitOfWork, IMessageSender<UpdateBusinessUnderstandingUpdatedDateTime> businessUnderstandingUpdateMessageSender)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _businessUnderstandingUpdateMessageSender = businessUnderstandingUpdateMessageSender ?? throw new ArgumentNullException(nameof(businessUnderstandingUpdateMessageSender));
    }

    public async Task<Result<string>> Handle(UpdateBusinessPartnerCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessPartner>();

        // 既存データを取得します。
        var spec = new FindByIdSpecification<Domain.Entities.BusinessPartner, string>(request.Id)
            .Include(x => x.BusinessPartnerIndustry);
        var getResult = await repository.SingleAsync(spec);

        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentBusinessPartner = getResult.Get();
        currentBusinessPartner.BusinessPartnerCustomerIdentificationId = request.BusinessPartnerCustomerIdentificationId;
        currentBusinessPartner.BusinessPartnerCustomerName = request.BusinessPartnerCustomerName;
        currentBusinessPartner.BusinessPartnerIndustry.SubIndustryCode = request.BusinessPartnerIndustry.SubIndustryCode;
        currentBusinessPartner.BusinessPartnerIndustry.DetailIndustryCode = request.BusinessPartnerIndustry.DetailIndustryCode;
        currentBusinessPartner.BusinessPartnerIndustry.IndustryCode = request.BusinessPartnerIndustry.IndustryCode;
        currentBusinessPartner.Note = request.Note;

        currentBusinessPartner.Version = request.Version;

        var updateBusinessUnderstandingResult = await UpdateBusinessUnderstandingUpdatedDateTime(currentBusinessPartner.BusinessUnderstandingId, request.staffId, request.staffName);
        if (updateBusinessUnderstandingResult.IsError) return updateBusinessUnderstandingResult.PreserveErrorAs<string>();

        return await repository
            .UpdateAsync(currentBusinessPartner) // データを更新として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentBusinessPartner.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }

    private async Task<Result> UpdateBusinessUnderstandingUpdatedDateTime(string businessUnderstandingId, string updaterId, string updaterName)
    {
        var updateBusinessUnderstandingResult = await _businessUnderstandingUpdateMessageSender.SendAsync(
            new UpdateBusinessUnderstandingUpdatedDateTime()
            {
                BusinessUnderstandingId = businessUnderstandingId,
                UpdaterId = updaterId,
                UpdaterName = updaterName,
            }
        );

        return updateBusinessUnderstandingResult;
    }
}
