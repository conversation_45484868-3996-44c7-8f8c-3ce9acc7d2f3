using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;

public class OurPolicyUnderstandingThreadFileConfiguration : IEntityTypeConfiguration<OurPolicyUnderstandingThreadFile>
{
    public void Configure(EntityTypeBuilder<OurPolicyUnderstandingThreadFile> builder)
    {
        builder.HasOne<OurPolicyUnderstandingThread>()
            .WithMany(l => l.Files)
            .HasForeignKey(j => j.ThreadId)
            .HasConstraintName("fk_our_policy_understanding_thread_our_policy_understanding_thread_file_id")
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.FileName)
            .IsRequired()
            .HasMaxLength(300);
    }
}
