using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class SWOTAnalysisConfiguration : IEntityTypeConfiguration<SWOTAnalysis>
{
    public void Configure(EntityTypeBuilder<SWOTAnalysis> builder)
    {
        builder.HasOne<BusinessUnderstanding>()
            .WithOne(l => l.SWOTAnalysis)
            .HasForeignKey<SWOTAnalysis>(j => j.BusinessUnderstandingId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .HasMaxLength(26);

        builder.Property(u => u.Strengths)
            .HasMaxLength(1000);

        builder.Property(u => u.Weaknesses)
            .HasMaxLength(1000);

        builder.Property(u => u.Opportunities)
            .HasMaxLength(1000);

        builder.Property(u => u.Threats)
            .HasMaxLength(1000);

    }
}
