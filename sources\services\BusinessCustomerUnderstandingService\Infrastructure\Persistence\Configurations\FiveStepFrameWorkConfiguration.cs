using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class FiveStepFrameWorkConfiguration : IEntityTypeConfiguration<FiveStepFrameWork>
{
    public void Configure(EntityTypeBuilder<FiveStepFrameWork> builder)
    {
        builder.Property(u => u.Id)
            .HasMaxLength(26);

        builder.Property(u => u.Ideal)
            .HasMaxLength(1000);

        builder.Property(u => u.Issue)
            .HasMaxLength(1000);

        builder.Property(u => u.CostReductionComment)
            .HasMaxLength(1000);

        builder.Property(u => u.ManagementComment)
            .HasMaxLength(1000);

        builder.Property(u => u.ICTAndBPRComment)
            .HasMaxLength(1000);

        builder.Property(u => u.HumanResourcesAndEvaluationAndDevelopmentComment)
            .HasMaxLength(1000);

        builder.Property(u => u.MarketingThinkingComment)
            .HasMaxLength(1000);

    }
}
