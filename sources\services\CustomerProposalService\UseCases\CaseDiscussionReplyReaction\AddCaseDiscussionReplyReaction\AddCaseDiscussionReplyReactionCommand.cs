using CustomerProposalService.Domain.Enums;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionReplyReaction.AddCaseDiscussionReplyReaction;

[WithDefaultBehaviors]
public record AddCaseDiscussionReplyReactionCommand(
    string CaseDiscussionReplyId,
    string UpdaterId,
    string UpdaterName,
    ReactionType ReactionType,
    DateTimeOffset UpdatedDateTime
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
