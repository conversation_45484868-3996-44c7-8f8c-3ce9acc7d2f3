using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThread.UpdateBusinessUnderstandingThread;

public class UpdateBusinessUnderstandingThreadSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingDiscussion>
{
    public UpdateBusinessUnderstandingThreadSpecification(string threadId)
    {
        Query
            .Where(x => x.ThreadId == threadId)
            .AsNoTracking();
    }
}
