using System.Text;
using CustomerProposalService.Configurations;
using CustomerProposalService.Domain;
using CustomerProposalService.Domain.Enums;
using Microsoft.AspNetCore.Http;
using Nut.Results;
using Shared.Domain;
using Shared.Messaging;
using SharedKernel.ExternalApi.MessageContract.Notification;

namespace CustomerProposalService.UseCases.CaseDiscussionThread;
public class CaseDiscussionThreadUtility : ICaseDiscussionThreadUtility
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageSender<AddCaseDiscussionNotificationQuery, IEnumerable<string>> _sender;
    private readonly ServiceSettings _serviceSettings;

    public CaseDiscussionThreadUtility(
        IUnitOfWork unitOfWork,
        IMessageSender<AddCaseDiscussionNotificationQuery, IEnumerable<string>> sender,
        ServiceSettings serviceSettings)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _sender = sender ?? throw new ArgumentNullException(nameof(sender));
        _serviceSettings = serviceSettings ?? throw new ArgumentNullException(nameof(serviceSettings));
    }

    public async Task<Result<IEnumerable<string>>> SendNotifyAsync<T>(
        T caseDiscussionThread,
        IEnumerable<string>? mentionTargetTeamMemberUserIds,
        string customerName,
        string? customerStaffId,
        NotificationType Type
        ) where T : Domain.Entities.CaseDiscussionThread
    {
        var notifyTargetUserIds = new HashSet<string>();
        var notifyMentionTargetUserIds = new HashSet<string>();

        // メンション先は手動通知先
        if (caseDiscussionThread.MentionTargetUserIds is not null)
        {
            var mentionTargetUserIds = caseDiscussionThread.MentionTargetUserIds.Where(m => !string.IsNullOrWhiteSpace(m));
            notifyMentionTargetUserIds.UnionWith(mentionTargetUserIds);
        }

        // チームメンションは手動通知先
        if (mentionTargetTeamMemberUserIds?.Any() == true)
        {
            notifyMentionTargetUserIds.UnionWith(mentionTargetTeamMemberUserIds);
        }

        // 登録者はメンションしないように
        notifyMentionTargetUserIds = notifyMentionTargetUserIds.Where(x => !x.Equals(caseDiscussionThread.RegistrantId)).ToHashSet();

        // 案件担当者は自動通知先（二重通知を避け、手動通知を優先）
        var caseRepository = _unitOfWork.GetRepository<Domain.Entities.Case>();
        var caseResult = await caseRepository.SingleAsync(new FindByIdSpecification<Domain.Entities.Case, string>(caseDiscussionThread.CaseId));
        if (caseResult.IsError) return caseResult.PreserveErrorAs<IEnumerable<string>>();
        var caseInfo = caseResult.Get();
        if (!string.IsNullOrEmpty(caseInfo.StaffId) && caseDiscussionThread.RegistrantId != caseInfo.StaffId)
        {
            notifyTargetUserIds.Add(caseInfo.StaffId);
        }

        // 顧客担当者は自動通知先（二重通知を避け、手動通知を優先）
        if (!string.IsNullOrEmpty(customerStaffId) && caseDiscussionThread.RegistrantId != customerStaffId && customerStaffId != "undefined")
        {
            notifyTargetUserIds.Add(customerStaffId);
        }

        var linkPath = $"/customer-proposal/customer-case/case-discussion?id={caseDiscussionThread.CaseId}&customerIdentificationId={caseInfo.CustomerIdentificationId}&threadId={caseDiscussionThread.Id}";

        var iBPHeaderText = "";
        var teamsHeaderText = "";

        var newLineSpace = new string(' ', 2);

        // メンション通知
        if (notifyMentionTargetUserIds.Any())
        {
            iBPHeaderText = $"社内外協議にて{caseDiscussionThread.RegistrantName}がメンションしました。";
            teamsHeaderText = $"{caseDiscussionThread.RegistrantName}がメンションしました。";

            var teamsMessageBuilder = new StringBuilder()
                    .AppendLine($"{teamsHeaderText}" + newLineSpace)
                    .AppendLine($"{customerName}" + newLineSpace)
                    .AppendLine($"{caseInfo.CaseCategory.DisplayName()}" + newLineSpace)
                    .AppendLine($"{caseInfo.CaseName}" + newLineSpace);

            if (caseInfo.StaffName.Length > 0)
            {

                teamsMessageBuilder.AppendLine($"案件担当者：{caseInfo.StaffName}" + newLineSpace);
            }

            teamsMessageBuilder.AppendLine($"[{caseDiscussionThread.ThreadName}](https://{_serviceSettings.Bot.HostName}{linkPath})" + newLineSpace)
                .ToString();

            var teamsMessage = teamsMessageBuilder.ToString();

            await _sender.SendAsync(new AddCaseDiscussionNotificationQuery()
            {
                FromUserId = caseDiscussionThread.RegistrantId,
                IBPNotificationTitle = $"{caseInfo.CaseName}",
                IBPNotificationMessage = $"{customerName}【{caseInfo.CaseCategory.DisplayName()}】 {iBPHeaderText} 【{caseDiscussionThread.ThreadName}】",
                TeamsMessage = teamsMessage,
                LinkPath = linkPath,
                ToUserIds = notifyMentionTargetUserIds
            }).ConfigureAwait(false);
        }

        // メンション通知が優先のため、自動通知先から除外する
        notifyTargetUserIds = notifyTargetUserIds.Except(notifyMentionTargetUserIds).ToHashSet();

        if (Type == NotificationType.Post) // 投稿
        {
            iBPHeaderText = $"社内外協議に投稿がありました。";
            teamsHeaderText = $"{caseDiscussionThread.RegistrantName}が社内外協議に投稿しました";
        }
        else if (Type == NotificationType.Reply) // 返信
        {
            iBPHeaderText = $"{caseDiscussionThread.RegistrantName}が社内外協議に返信しました";
            teamsHeaderText = $"{caseDiscussionThread.RegistrantName}が社内外協議に返信しました。";
        }
        else if (Type == NotificationType.Update)
        {
            iBPHeaderText = $"{caseDiscussionThread.RegistrantName}が社内外協議を編集しました";
            teamsHeaderText = $"{caseDiscussionThread.RegistrantName}が社内外協議を編集しました。";
        }
        else
        {
            return Result.Error<IEnumerable<string>>(new Exception("NotificationTypeが正しくありません。"));
        }

        // 自動通知
        if (notifyTargetUserIds.Any())
        {
            var teamsMessageBuilder = new StringBuilder()
                    .AppendLine($"{teamsHeaderText}" + newLineSpace)
                    .AppendLine($"{customerName}" + newLineSpace)
                    .AppendLine($"{caseInfo.CaseCategory.DisplayName()}" + newLineSpace)
                    .AppendLine($"{caseInfo.CaseName}" + newLineSpace);

            if (caseInfo.StaffName.Length > 0)
            {

                teamsMessageBuilder.AppendLine($"案件担当者：{caseInfo.StaffName}" + newLineSpace);
            }

            teamsMessageBuilder.AppendLine($"[{caseDiscussionThread.ThreadName}](https://{_serviceSettings.Bot.HostName}{linkPath})" + newLineSpace)
                .ToString();

            var teamsMessage = teamsMessageBuilder.ToString();

            await _sender.SendAsync(new AddCaseDiscussionNotificationQuery()
            {
                FromUserId = caseDiscussionThread.RegistrantId,
                IBPNotificationTitle = $"{caseInfo.CaseName}",
                IBPNotificationMessage = $"{customerName}【{caseInfo.CaseCategory.DisplayName()}】 {iBPHeaderText} 【{caseDiscussionThread.ThreadName}】",
                TeamsMessage = teamsMessage,
                LinkPath = linkPath,
                ToUserIds = notifyTargetUserIds
            }).ConfigureAwait(false);
        }

        return Result.Ok(Enumerable.Empty<string>());
    }

    public async Task<Result<string>> UpdateCaseLastUpdatedAt(string caseId, DateTimeOffset updateTime)
    {
        // 案件更新情報のデータを取得
        var caseUpdateInformationRepository = _unitOfWork.GetRepository<Domain.Entities.CaseUpdateInformation>();
        var getResult = await caseUpdateInformationRepository.SingleAsync(
            new GetCaseUpdateInformationByCaseIdSpecification(caseId)
            ).ConfigureAwait(false);

        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        // 案件更新情報の最終更新日を更新
        var currentData = getResult.Get();
        currentData.LastUpdatedAt = updateTime;

        await caseUpdateInformationRepository
            .UpdateAsync(currentData)
            .ConfigureAwait(false);

        return Result.Ok("");
    }
}
