using System.Text.Json;
using CustomerProposalService.Domain;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;
using Shared.Domain;
using Shared.Services;

namespace CustomerProposalService.UseCases.Case.UpdateDeedLoanCase;

public class UpdateDeedLoanCaseHandler : IRequestHandler<UpdateDeedLoanCaseCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDateTimeService;
    private readonly ICaseUtility _caseUtility;

    public UpdateDeedLoanCaseHandler(
        IUnitOfWork unitOfWork,
        ICurrentDateTimeService currentDateTimeService,
        ICaseUtility caseUtility)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
        _caseUtility = caseUtility ?? throw new ArgumentNullException(nameof(caseUtility));
    }

    public async Task<Result<string>> Handle(UpdateDeedLoanCaseCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var caseLinks = new List<Domain.Entities.CaseLink>();
        if (request.CaseLinks?.Any() == true)
        {
            foreach (var caseLink in request.CaseLinks)
            {
                var objectLink = JsonSerializer.Deserialize<Domain.Entities.CaseLink>(caseLink);
                caseLinks.Add(objectLink!);
            }
        }

        // 既存の案件データを取得します。
        var repository = _unitOfWork.GetRepository<Domain.Entities.DeedLoanCase, string>();
        var getResult = await repository.SingleAsync(
            new FindByIdSpecification<Domain.Entities.DeedLoanCase, string>(request.Id)
            .Include(c => c.CaseUpdateInformation)
            .Include(c => c.CaseFiles)
            .Include(c => c.CaseLinks)
            ).ConfigureAwait(false);

        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();
        var now = _currentDateTimeService.NowDateTimeOffset();
        currentData.CaseName = request.CaseName;
        currentData.CaseStatus = request.CaseStatus;
        currentData.CaseOutline = request.CaseOutline?.Replace("\r\n", "\n");
        currentData.ExpiredAt = request.ExpiredAt;
        currentData.StaffId = request.StaffId;
        currentData.StaffName = request.StaffName;
        currentData.CaseUpdatedAt = now;
        currentData.CaseUpdateInformation.LastUpdatedAt = now;
        currentData.AccountType = request.AccountType;
        currentData.Amount = request.Amount;
        currentData.Period = request.Period;
        currentData.ApplicableBaseInterestRate = request.ApplicableBaseInterestRate;
        currentData.InterestRate = request.InterestRate;
        currentData.InterestRateCustom = request.InterestRateCustom;
        currentData.BaseInterestRate = request.BaseInterestRate;
        currentData.Spread = request.Spread;
        currentData.LoanPurposeCode = request.LoanPurposeCode;
        currentData.LoanPurposeCustom = request.LoanPurposeCustom;
        currentData.RelatedEsg = request.RelatedEsg;
        currentData.PreConsultationStandardTarget = request.PreConsultationStandardTarget;
        currentData.IsEarthquakeRelated = request.IsEarthquakeRelated;
        currentData.RepaymentType = request.RepaymentType;
        currentData.RepaymentSourceCode = request.RepaymentSourceCode;
        currentData.CollateralType = request.CollateralType;
        currentData.CollateralOrGuaranteeCustom = request.CollateralOrGuaranteeCustom;
        currentData.GuaranteeType = request.GuaranteeType;
        currentData.CancelTypeOfLoan = request.CancelTypeOfLoan;
        currentData.CancelReason = request.CancelReason;
        currentData.TrafficSource = request.TrafficSource;

        // ファイルをアップロード
        var fileResult = await _caseUtility.ReplaceFiles(_unitOfWork, request.UploadFiles, currentData);
        if (fileResult.IsError) return fileResult;

        // リンクを登録
        var linkResult = await _caseUtility.ReplaceLinks(_unitOfWork, caseLinks, currentData);
        if (linkResult.IsError) return linkResult;

        // バージョン番号は、同時実行制御を有効にするために引数で受け取ったデータの値で必ず上書きします。
        currentData.Version = request.Version;

        return await repository
            .UpdateAsync(currentData) // データを更新として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
