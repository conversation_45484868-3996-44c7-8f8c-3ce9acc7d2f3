using BusinessCustomerUnderstandingService.Domain.Enums;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.Services.Queries;

public interface IGetCommunicationPlanSummaryQueryService
{
    Task<Result<IEnumerable<CommunicationPlanSummary>>> Handle(string businessUnderstandingId);
}

public record CommunicationPlanSummary()
{
    public CommunicationPlanType CommunicationPlanType { get; init; } = default!;
    public int LessThan1MonthCount { get; init; } = default;
    public int LessThan6MonthCount { get; init; } = default;
    public int GraterThanEqual6MonthCount { get; init; } = default;
    public DateTimeOffset? LastRegisteredAt { get; init; } = default!;
}