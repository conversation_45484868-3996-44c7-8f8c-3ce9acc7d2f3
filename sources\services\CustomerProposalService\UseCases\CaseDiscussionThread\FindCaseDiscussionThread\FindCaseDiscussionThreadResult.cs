using CustomerProposalService.Domain.Entities;
using CustomerProposalService.Domain.Enums;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.FindCaseDiscussionThread;

public record FindCaseDiscussionThreadResult(
    string Id,
    string CaseId,
    DateTimeOffset RegisteredAt,
    string RegistrantName,
    string RegistrantId,
    CaseDiscussionThreadType ThreadType,
    string Title,
    string DisplayText,
    IEnumerable<Domain.Entities.CaseDiscussionReply>? Replies,
    IEnumerable<CaseDiscussionThreadFile>? Files,
    IEnumerable<Domain.Entities.CaseDiscussionThreadReaction>? Reactions,
    string? MentionTargetsHtml,
    IEnumerable<string>? MentionTargetUserIds,
    string Version
);
