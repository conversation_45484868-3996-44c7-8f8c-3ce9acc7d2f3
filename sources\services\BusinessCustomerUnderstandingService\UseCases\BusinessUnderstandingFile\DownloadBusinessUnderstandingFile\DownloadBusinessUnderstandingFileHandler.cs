using BusinessCustomerUnderstandingService.Infrastructure.Storage;
using MediatR;
using Nut.Results;
using Shared.ObjectStorage;
using Shared.Results.Errors;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingFile.DownloadBusinessUnderstandingFile;
public class DownloadBusinessUnderstandingFileHandler : IRequestHandler<DownloadBusinessUnderstandingFileQuery, Result<DownloadBusinessUnderstandingFileResult>>
{
    private readonly IBusinessUnderstandingStorageClientProvider _objectStorageClientProvider;

    public DownloadBusinessUnderstandingFileHandler(IBusinessUnderstandingStorageClientProvider objectStorageClientProvider)
    {
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
    }

    public async Task<Result<DownloadBusinessUnderstandingFileResult>> Handle(DownloadBusinessUnderstandingFileQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // ObjectStorageのクライアントを作成します。
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        // 保存されているオブジェクトの一覧を取得
        var objects = await storageClient.GetObjectInformationsAsync($"business-understanding/{request.BusinessUnderstandingId}").ConfigureAwait(false);
        if (objects.IsError) return objects.PreserveErrorAs<DownloadBusinessUnderstandingFileResult>();

        // 指定されたファイルを取得
        var target = objects.Get().Where(e => e.Name == $"business-understanding/{request.BusinessUnderstandingId}/{request.FileName}").FirstOrDefault();
        if (target is null) return Result.Error<DownloadBusinessUnderstandingFileResult>(new DataNotFoundException());

        var result = await storageClient.GetAsync(target.Name);
        return result.Map(s => new DownloadBusinessUnderstandingFileResult(s));

    }
}
