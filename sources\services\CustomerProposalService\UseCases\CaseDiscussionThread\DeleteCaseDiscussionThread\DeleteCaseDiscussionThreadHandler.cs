using CustomerProposalService.Domain;
using CustomerProposalService.Infrastructure.Storage;
using MediatR;
using Nut.Results;
using Shared.Domain;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.DeleteCaseDiscussionThread;

public class DeleteCaseDiscussionThreadHandler : IRequestHandler<DeleteCaseDiscussionThreadCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICaseDiscussionThreadStorageClientProvider _objectStorageClientProvider;

    public DeleteCaseDiscussionThreadHandler(
        IUnitOfWork unitOfWork,
        ICaseDiscussionThreadStorageClientProvider objectStorageClientProvider
)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
    }

    public async Task<Result<string>> Handle(DeleteCaseDiscussionThreadCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CaseDiscussionThread, string>();
        // 既存データを取得します。
        var getResult = await repository
            .SingleAsync(new FindByIdSpecification<Domain.Entities.CaseDiscussionThread, string>(request.Id)
            .Include(x => x.Files)
            // NOTE: RepliesはIncludeしていない。そもそも協議スレッドに紐づく返信があった場合は削除できない仕様のため
            .AsNoTracking()).ConfigureAwait(false);
        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();
        // バージョン番号は、同時実行制御を有効にするために引数で受け取ったデータの値で必ず上書きします。
        currentData.Version = request.Version;

        var caseDiscussionThreadStorage = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        if (currentData.Files?.Any() == true)
        {
            // 取得した添付ファイルをBlobから削除
            foreach (var file in currentData.Files)
            {
                var fileName = Path.GetFileName(file.FileName);
                var deleteFileResult = await caseDiscussionThreadStorage.DeleteAsync($"case-discussion-thread/{currentData.Id}/{fileName}").ConfigureAwait(false);
                if (deleteFileResult.IsError) return deleteFileResult.PreserveErrorAs<string>();
            }
        }

        return await repository
            .DeleteAsync(currentData) // データを削除として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
