using MediatR;
using Nut.MediatR.ServiceLike;
using Nut.Results;
using Shared.UseCase.AsServiceFilters;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.FindCommunicationPlanStaff;

[AsService("/business-customer-understanding/find-communicationplan-staff", typeof(StripResultFilter))]
[WithDefaultBehaviors]
public record FindCommunicationPlanStaffQuery() : IRequest<Result<IEnumerable<FindCommunicationPlanStaffResult>>>
{
}
