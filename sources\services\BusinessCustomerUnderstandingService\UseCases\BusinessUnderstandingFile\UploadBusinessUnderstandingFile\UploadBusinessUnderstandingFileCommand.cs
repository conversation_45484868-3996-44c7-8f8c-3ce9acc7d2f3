using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingFile.UploadBusinessUnderstandingFile;
public record UploadBusinessUnderstandingFileCommand(
    string BusinessUnderstandingId,
    string UpdaterId,
    string UpdaterName,
    List<string> SelectedFileNames,
    List<IFormFile> UploadFiles) : IRequest<Result<List<Domain.Entities.BusinessUnderstandingFile>>>
{
}
