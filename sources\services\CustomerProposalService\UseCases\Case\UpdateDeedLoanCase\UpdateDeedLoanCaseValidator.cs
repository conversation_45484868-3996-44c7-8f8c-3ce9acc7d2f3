using FluentValidation;

namespace CustomerProposalService.UseCases.Case.UpdateDeedLoanCase;

public class UpdateDeedLoanCaseValidator : AbstractValidator<UpdateDeedLoanCaseCommand>
{
    public UpdateDeedLoanCaseValidator()
    {
        // Case Entity
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.CaseName).NotEmpty().MaximumLength(50);
        RuleFor(v => v.CaseStatus).NotEmpty().IsInEnum();
        RuleFor(e => e.CaseOutline).CaseOutline();
        RuleFor(v => v.StaffId).NotEmpty().MaximumLength(50);
        RuleFor(v => v.StaffName).NotEmpty().MaximumLength(50);
        // DeedLoanCase Entity
        RuleFor(e => e.AccountType).MaximumLength(50);
        RuleFor(e => e.ApplicableBaseInterestRate).MaximumLength(50);
        RuleFor(e => e.LoanPurposeCode).MaximumLength(50);
        RuleFor(e => e.RepaymentType).MaximumLength(50);
        RuleFor(e => e.RepaymentSourceCode).MaximumLength(50);
        RuleFor(e => e.CollateralType).MaximumLength(50);
        RuleFor(e => e.GuaranteeType).MaximumLength(50);
        RuleFor(e => e.CancelTypeOfLoan).IsInEnum();
        RuleFor(e => e.CancelReason).CancelReason();
        RuleFor(e => e.TrafficSource).IsInEnum();
        RuleFor(v => v.Version).NotEmpty();
    }
}
