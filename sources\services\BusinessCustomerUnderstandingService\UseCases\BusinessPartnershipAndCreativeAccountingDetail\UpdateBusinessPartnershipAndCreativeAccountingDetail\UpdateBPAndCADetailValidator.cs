using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartnershipAndCreativeAccountingDetail.UpdateBusinessPartnershipAndCreativeAccountingDetail;

public class UpdateBPAndCADetailValidator : AbstractValidator<UpdateBPAndCADetailCommand>
{
    public UpdateBPAndCADetailValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.FeatureOfBusinessPartnership).MaximumLength(500);
        RuleFor(v => v.DescriptionOfCreativeAccountingIncident).MaximumLength(500);
        RuleFor(v => v.StaffId).NotEmpty();
        RuleFor(v => v.StaffName).NotEmpty();
        RuleFor(v => v.Version).NotEmpty();
    }
}
