using MediatR;
using Nut.Results;
using Shared.Application;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.FindBusinessUnderstandingApproach;

[WithDefaultBehaviors]
public record FindBusinessUnderstandingApproachQuery : PageQuery, IRequest<Result<PaginatedResult<FindBusinessUnderstandingApproachResult>>>
{
    public Guid? CustomerIdentificationId { get; set; }
}
