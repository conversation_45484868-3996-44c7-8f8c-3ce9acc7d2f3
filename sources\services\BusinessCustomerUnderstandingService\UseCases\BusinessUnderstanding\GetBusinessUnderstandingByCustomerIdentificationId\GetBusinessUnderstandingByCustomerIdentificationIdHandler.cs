using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;
using Shared.Results.Errors;
using Dto = BusinessCustomerUnderstandingService.Domain.Dto.BusinessUnderstanding;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessUnderstandingByCustomerIdentificationId;

public class GetBusinessUnderstandingByCustomerIdentificationIdHandler : IRequestHandler<GetBusinessUnderstandingByCustomerIdentificationIdQuery, Result<GetBusinessUnderstandingByCustomerIdentificationIdResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetBusinessUnderstandingByCustomerIdentificationIdHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<GetBusinessUnderstandingByCustomerIdentificationIdResult>> Handle(GetBusinessUnderstandingByCustomerIdentificationIdQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 法人情報取得
        var bizCustomerRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessCustomer, string>();
        var getBizCustomerResult = await bizCustomerRepository.SingleAsync(new GetBusinessCustomerByCustomerIdentificationIdSpecification(request.CustomerIdentificationId)).ConfigureAwait(false);
        if (getBizCustomerResult.IsError) return getBizCustomerResult.PreserveErrorAs<GetBusinessUnderstandingByCustomerIdentificationIdResult>();
        var bizCustomer = getBizCustomerResult.Get();

        // 事業性理解の存在確認
        if (bizCustomer.BusinessUnderstanding == null)
           return Result.Error<GetBusinessUnderstandingByCustomerIdentificationIdResult>(new DataNotFoundException());

        // 外部環境マスターに登録されているScoreの最大値を取得
        var maxScoreOfEEM = 0;
        try
        {
            var eemRepository = _unitOfWork.GetRepository<Domain.Entities.ExternalEnvironmentMaster, string>();
            var getResult = await eemRepository.AllAsync().ConfigureAwait(false);
            if (getResult.IsError) return getResult.PreserveErrorAs<GetBusinessUnderstandingByCustomerIdentificationIdResult>();

            var eemEntity = getResult.Get().OrderByDescending(s => s.Score).FirstOrDefault();
            maxScoreOfEEM = (eemEntity != null) ? eemEntity.Score : 0;
        }
        catch
        {
            maxScoreOfEEM = 0;
        }

        // データを取得
        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding, string>();
        return await repository.SingleAsync(new GetBusinessUnderstandingSpecification(bizCustomer.BusinessUnderstanding.Id))
            .Map(v => new GetBusinessUnderstandingByCustomerIdentificationIdResult(
                v.Id,
                new Dto.ManagementPlan(v.ManagementPlan!),
                new Dto.Management(v.Management!),
                new Dto.FiveForceFramework(v.FiveForceFramework!),
                new Dto.FiveStepFrameWork(v.FiveStepFrameWork!),
                new Dto.ESGAndSDGs(v.ESGAndSDGs!),
                new Dto.ExternalEnvironment(v.ExternalEnvironment!),
                new Dto.RelationLevel(v.RelationLevel!),
                new Dto.ThreeCAnalysis(v.ThreeCAnalysis!),
                new Dto.SWOTAnalysis(v.SWOTAnalysis!),
                new Dto.CommercialDistribution(v.CommercialDistribution!),
                new Dto.FamilyTree(v.FamilyTree!),
                new Dto.CalculationResult(v.ManagementPlan!, v.Management!, v.FiveForceFramework!, v.FiveStepFrameWork!, v.ExternalEnvironment!, v.ESGAndSDGs!, maxScoreOfEEM, v.RelationLevel!),
                v.BusinessUnderstandingMaterializedView is not null ? v.BusinessUnderstandingMaterializedView.TransactionPolicy : string.Empty,
                v.TransactionPolicyConfirmerId,
                v.TransactionPolicyConfirmedDateTime,
                v.BusinessCustomer.Id,
                v.BusinessUnderstandingMaterializedView is not null ? v.BusinessUnderstandingMaterializedView.UpdatedDateTime : null,
                v.Version));
    }
}
