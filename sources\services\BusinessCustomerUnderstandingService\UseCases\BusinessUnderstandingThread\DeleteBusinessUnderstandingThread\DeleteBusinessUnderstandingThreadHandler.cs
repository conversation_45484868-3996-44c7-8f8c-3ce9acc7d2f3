using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Infrastructure.Storage;
using BusinessCustomerUnderstandingService.Services.Domain;
using MediatR;
using Nut.Results;
using Shared.Domain;
using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThread.DeleteBusinessUnderstandingThread;

public class DeleteBusinessUnderstandingThreadHandler : IRequestHandler<DeleteBusinessUnderstandingThreadCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IBusinessUnderstandingStorageClientProvider _objectStorageClientProvider;
    private readonly IQuillImageService _quillImageService;
    private readonly string _containerName = "business-understanding";
    private readonly string _threadFolderName = "thread";
    private readonly string _discussionFolderName = "discussion";

    public DeleteBusinessUnderstandingThreadHandler(
        IUnitOfWork unitOfWork,
        IBusinessUnderstandingStorageClientProvider objectStorageClientProvider,
        IQuillImageService quillImageService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
        _quillImageService = quillImageService ?? throw new ArgumentNullException(nameof(quillImageService));

    }

    public async Task<Result<string>> Handle(DeleteBusinessUnderstandingThreadCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingThread, string>();
        // 既存データを取得します。
        var getResult = await repository
            .SingleAsync(new FindByIdSpecification<Domain.Entities.BusinessUnderstandingThread, string>(request.Id)
            .Include(x => x.Files).AsNoTracking()
            .Include(x => x.Discussions, x => x.ThenInclude(x => x.Files)))
            .ConfigureAwait(false);
        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();

        // バージョン番号は、同時実行制御を有効にするために引数で受け取ったデータの値で必ず上書きします。
        currentData.Version = request.Version;

        // Blobに保持する返信本文コンテンツをすべて削除
        // スレッド側の削除
        var deleteThreadContentsResult = await _quillImageService.DeleteAllContentsAsync(_containerName, _threadFolderName, currentData.Id);
        if (deleteThreadContentsResult.IsError) return Result.Error<string>(deleteThreadContentsResult.GetError());
        // コメント側の削除
        var deleteCommentContentsResult = await _quillImageService.DeleteAllContentsAsync(_containerName, _discussionFolderName, currentData.Id);
        if (deleteCommentContentsResult.IsError) return Result.Error<string>(deleteCommentContentsResult.GetError());

        // ==============================================================
        // スレッドの添付ファイルをblobから削除
        // ==============================================================
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        // スレッドファイルをBlobから削除
        foreach (var files in currentData.Files)
        {
            var deleteFileResult = await storageClient.DeleteAsync($"{_threadFolderName}/{request.Id}/{files.FileName}").ConfigureAwait(false);
            if (deleteFileResult.IsError) return deleteFileResult.PreserveErrorAs<string>();
        }

        // ==============================================================
        // コメントの添付ファイルをblobから削除
        // ==============================================================
        // ディスカッションファイルをBlobから削除
        foreach (var discussion in currentData.Discussions)
        {
            foreach (var discussionFile in discussion.Files)
            {
                var deleteFileResult = await storageClient.DeleteAsync($"{_discussionFolderName}/{discussion.Id}/{discussionFile.FileName}").ConfigureAwait(false);
                if (deleteFileResult.IsError) return deleteFileResult.PreserveErrorAs<string>();
            }
        }

        return await repository
            .DeleteAsync(currentData) // データを削除として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
