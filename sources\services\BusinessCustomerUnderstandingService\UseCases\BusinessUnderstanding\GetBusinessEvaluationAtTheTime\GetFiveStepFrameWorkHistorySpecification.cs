using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessEvaluationAtTheTime;

public class GetFiveStepFrameWorkHistorySpecification : BaseSpecification<Domain.Entities.FiveStepFrameWorkHistory>
{
    public GetFiveStepFrameWorkHistorySpecification(string originalId, DateTimeOffset targetDateTime)
    {
        Query.Where(x => x.OriginalId == originalId && x.UpdatedDateTime == targetDateTime).AsNoTracking();
    }
}
