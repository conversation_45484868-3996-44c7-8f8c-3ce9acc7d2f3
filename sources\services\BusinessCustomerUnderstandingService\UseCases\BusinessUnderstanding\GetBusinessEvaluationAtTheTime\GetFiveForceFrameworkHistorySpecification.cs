using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessEvaluationAtTheTime;

public class GetFiveForceFrameworkHistorySpecification : BaseSpecification<Domain.Entities.FiveForceFrameworkHistory>
{
    public GetFiveForceFrameworkHistorySpecification(string originalId, DateTimeOffset targetDateTime)
    {
        Query.Where(x => x.OriginalId == originalId && x.UpdatedDateTime == targetDateTime).AsNoTracking();
    }
}
