using System.Collections.Concurrent;
using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Resources;
using Microsoft.EntityFrameworkCore;
using Nut.Results;
using Shared.Domain;
using Shared.EntityFrameworkCore;
using Shared.Results.Errors;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence;

/// <summary>
/// 管理している <see cref="IRepository{TEntity, TId}"/> の変更を纏めます。
/// </summary>
public class UnitOfWork : IUnitOfWork
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ConcurrentDictionary<Type, object> _withIdPool = new();
    private readonly ConcurrentDictionary<Type, object> _pool = new();

    /// <summary>
    /// <see cref="ApplicationDbContext"/> を指定してインスタンスを初期化します。
    /// </summary>
    /// <param name="dbContext">利用する <see cref="ApplicationDbContext"/></param>
    public UnitOfWork(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    /// <inheritdoc />
    public IRepository<TEntity, TId> GetRepository<TEntity, TId>()
        where TEntity : class, IEntity<TId>
        // リポジトリはキャッシュする
        => (EntityFrameworkRepository<TEntity, TId>)_withIdPool.GetOrAdd(typeof(TEntity), _ =>
        {
            // DbContextから対応するエンティティがあるかチェックする
            if (_dbContext.Model.FindEntityType(typeof(TEntity)) is null)
            {
                // ない場合はエラー
                throw new InvalidOperationException(
                Strings.EntityNotManagedInThisContext(
                    typeof(TEntity).Name, nameof(ApplicationDbContext)));
            }

            // ある場合はリポジトリを生成して返す。
            return new EntityFrameworkRepository<TEntity, TId>(_dbContext);
        });

    /// <inheritdoc />
    public IRepository<TEntity> GetRepository<TEntity>()
        where TEntity : class
        // リポジトリはキャッシュする
        => (EntityFrameworkRepository<TEntity>)_pool.GetOrAdd(typeof(TEntity), _ =>
        {
            // DbContextから対応するエンティティがあるかチェックする
            if (_dbContext.Model.FindEntityType(typeof(TEntity)) is null)
            {
                // ない場合はエラー
                throw new InvalidOperationException(
                Strings.EntityNotManagedInThisContext(
                    typeof(TEntity).Name, nameof(ApplicationDbContext)));
            }

            // ある場合はリポジトリを生成して返す。
            return new EntityFrameworkRepository<TEntity>(_dbContext);
        });

    /// <inheritdoc />
    public Task<Result> SaveEntitiesAsync(CancellationToken cancellationToken = default)
        => Result.Try(
            async () =>
            {
                await _dbContext.SaveChangesAsync(cancellationToken);
            }).FlatMapError(ex => Result.Error(ex switch
            {
                DbUpdateConcurrencyException => new ChangeConflictException(ex),
                _ => ex
            }));
}
