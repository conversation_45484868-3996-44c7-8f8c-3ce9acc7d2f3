using FluentValidation;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.UpdateLoanCaseDiscussionThread;

public class UpdateLoanCaseDiscussionThreadValidator : AbstractValidator<UpdateLoanCaseDiscussionThreadCommand>
{
    public UpdateLoanCaseDiscussionThreadValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.CustomerIdentificationId).NotEmpty();
        RuleFor(v => v.CustomerName).NotEmpty();
        RuleFor(v => v.RegistrantId).NotEmpty();
        RuleFor(v => v.ThreadName).NotEmpty().MaximumLength(50);
        RuleFor(v => v.Purpose).IsInEnum().NotEqual(Domain.Enums.CaseDiscussionPurpose.Undefined);
        RuleFor(v => v.Person).MaximumLength(20);
        RuleFor(v => v.ReasonForGuaranty).NotEmpty().MaximumLength(500);
        RuleFor(v => v.HowToImproveForGuaranty).NotEmpty().MaximumLength(500);
        RuleFor(v => v.Description).NotEmpty().MaximumLength(10000);
        RuleFor(v => v.Version).NotEmpty();
    }
}
