using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Infrastructure.Storage;
using BusinessCustomerUnderstandingService.Services.Domain;
using MediatR;
using Nut.Results;
using Shared.Domain;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussion.DeleteBusinessUnderstandingDiscussion;

public class DeleteBusinessUnderstandingDiscussionHandler : IRequestHandler<DeleteBusinessUnderstandingDiscussionCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IBusinessUnderstandingStorageClientProvider _objectStorageClientProvider;
    private readonly IQuillImageService _quillImageService;
    private readonly string _containerName = "business-understanding";
    private readonly string _folderName = "discussion";

    public DeleteBusinessUnderstandingDiscussionHandler(
        IUnitOfWork unitOfWork,
        IBusinessUnderstandingStorageClientProvider objectStorageClientProvider,
        IQuillImageService quillImageService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
        _quillImageService = quillImageService ?? throw new ArgumentNullException(nameof(quillImageService));
    }

    public async Task<Result<string>> Handle(DeleteBusinessUnderstandingDiscussionCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingDiscussion, string>();
        // 既存データを取得します。
        var getResult = await repository
            .SingleAsync(new FindByIdSpecification<Domain.Entities.BusinessUnderstandingDiscussion, string>(request.Id)
            .Include(x => x.Files).AsNoTracking())
            .ConfigureAwait(false);
        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();

        // Blobに保持する返信本文コンテンツをすべて削除
        var deleteContentsResult = await _quillImageService.DeleteAllContentsAsync(_containerName, _folderName, currentData.Id);
        if (deleteContentsResult.IsError) return Result.Error<string>(deleteContentsResult.GetError());

        // バージョン番号は、同時実行制御を有効にするために引数で受け取ったデータの値で必ず上書きします。
        currentData.Version = request.Version;

        var discussionStorageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        // 取得した添付ファイルをBlobから削除
        foreach (var file in currentData.Files)
        {
            var fileName = Path.GetFileName(file.FileName);
            var deleteFileResult = await discussionStorageClient.DeleteAsync($"{_folderName}/{request.Id}/{fileName}").ConfigureAwait(false);
            if (deleteFileResult.IsError) return deleteFileResult.PreserveErrorAs<string>();
        }

        return await repository
            .DeleteAsync(currentData) // データを削除として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
