using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingMaterializedView.UpdateBusinessUnderstandingMaterializedView;

public class FindBusinessUnderstandingMaterializedViewSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingMaterializedView>
{
    public FindBusinessUnderstandingMaterializedViewSpecification(string BusinessUnderstandingId)
    {
        Query
            .Where(x => x.BusinessUnderstandingId == BusinessUnderstandingId)
            .AsNoTracking();
    }
}
