using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class CustomerReactionConfiguration : IEntityTypeConfiguration<CustomerReaction>
{
    public void Configure(EntityTypeBuilder<CustomerReaction> builder)
    {
        builder.HasOne(x => x.HypotheticalDiscussionOfIssues)
            .WithOne(l => l.CustomerReaction)
            .HasForeignKey<CustomerReaction>(l => l.HypotheticalDiscussionOfIssuesId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(e => e.Id)
                .HasMaxLength(26);

        builder.Property(e => e.Content)
                .HasMaxLength(4000)
                .IsRequired();

    }
}
