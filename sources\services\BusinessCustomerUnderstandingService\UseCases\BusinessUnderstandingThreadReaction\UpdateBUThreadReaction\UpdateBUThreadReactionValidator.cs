using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThreadReaction.UpdateBUThreadReaction;

public class UpdateBUThreadReactionValidator : AbstractValidator<UpdateBUThreadReactionCommand>
{
    public UpdateBUThreadReactionValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.ReactionType).NotEmpty();
        RuleFor(v => v.UpdatedDateTime).NotEmpty();
        RuleFor(v => v.Version).NotEmpty();
    }
}
