using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussion.UpdateBusinessUnderstandingDiscussion;

public class UpdateBusinessUnderstandingDiscussionSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingDiscussion>
{
    public UpdateBusinessUnderstandingDiscussionSpecification(string threadId)
    {
        Query
            .Where(x => x.ThreadId == threadId)
            .AsNoTracking();
    }
}
