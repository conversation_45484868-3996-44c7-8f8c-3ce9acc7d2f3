using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussion.AddBusinessUnderstandingDiscussion;

public class AddBusinessUnderstandingDiscussionValidator : AbstractValidator<AddBusinessUnderstandingDiscussionCommand>
{
    public AddBusinessUnderstandingDiscussionValidator()
    {
        RuleFor(v => v.Description).NotEmpty();
        RuleFor(v => v.Purpose).NotEmpty().NotEqual(Domain.Enums.DiscussionPurpose.Undefined);
        RuleFor(v => v.Person).MaximumLength(20);
        When(v => v.Purpose.Equals(Domain.Enums.DiscussionPurpose.Internal), () =>
        {
            RuleFor(v => v.IsCorporateDepositTheme).Null();
            RuleFor(v => v.IsFundSettlementTheme).Null();
        });
        When(v => v.Purpose.Equals(Domain.Enums.DiscussionPurpose.External), () =>
        {
            RuleFor(v => v.IsCorporateDepositTheme).NotNull();
            RuleFor(v => v.IsFundSettlementTheme).NotNull();
        });
    }
}
