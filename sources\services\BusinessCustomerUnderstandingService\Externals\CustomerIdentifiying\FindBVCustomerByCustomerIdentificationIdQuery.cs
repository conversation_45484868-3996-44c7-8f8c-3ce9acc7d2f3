using Shared.Messaging;

namespace BusinessCustomerUnderstandingService.Externals.CustomerIdentifiying;

[MessageEndpoint("/customer-identification/find-bv-customer")]
public class FindBVCustomerByCustomerIdentificationIdQuery : ISendRequest<List<FindBVCustomerByCustomerIdentificationIdResult>>
{
    public List<Guid> CustomerIdentificationIds { get; set; } = default!;
}

public record FindBVCustomerByCustomerIdentificationIdResult(
    Guid Id,
    string BranchNumber,
    string CifNumber,
    string NameKana,
    string NameKanji,
    string? MainIndustryCode,
    Guid CustomerIdentificationId
);
