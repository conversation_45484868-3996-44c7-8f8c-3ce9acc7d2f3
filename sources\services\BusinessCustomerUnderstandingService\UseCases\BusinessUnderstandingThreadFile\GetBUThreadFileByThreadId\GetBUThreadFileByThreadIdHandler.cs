using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThreadFile.GetBUThreadFileByThreadId;

public class GetBUThreadFileByThreadIdHandler : IRequestHandler<GetBUThreadFileByThreadIdQuery, Result<List<GetBUThreadFileByThreadIdResult>>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetBUThreadFileByThreadIdHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<List<GetBUThreadFileByThreadIdResult>>> Handle(GetBUThreadFileByThreadIdQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingThreadFile, string>();

        var result = repository.FindAsync(new GetBUThreadFileByThreadIdSpecification(request.ThreadId));

        return result.Map(val => val.Select(v => new GetBUThreadFileByThreadIdResult(
                v.Id,
                v.FileName,
                v.UpdatedDateTime,
                v.UpdaterId,
                v.UpdaterName,
                v.ThreadId,
                v.Version
                )).ToList());
    }
}
