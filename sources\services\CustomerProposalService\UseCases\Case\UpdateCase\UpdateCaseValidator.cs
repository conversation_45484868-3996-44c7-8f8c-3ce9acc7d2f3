using FluentValidation;

namespace CustomerProposalService.UseCases.Case.UpdateCase;

public class UpdateCaseValidator : AbstractValidator<UpdateCaseCommand>
{
    public UpdateCaseValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.CaseName).NotEmpty().MaximumLength(50);
        RuleFor(v => v.CaseStatus).NotEmpty().IsInEnum();
        RuleFor(v => v.CaseOutline).CaseOutline();
        RuleFor(v => v.StaffId).NotEmpty().MaximumLength(50);
        RuleFor(v => v.StaffName).NotEmpty().MaximumLength(50);
        RuleFor(v => v.Version).NotEmpty();
    }
}
