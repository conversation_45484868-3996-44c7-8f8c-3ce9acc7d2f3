using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.UpdateBusinessUnderstandingApproach;

public class UpdateBusinessUnderstandingApproachValidator : AbstractValidator<UpdateBusinessUnderstandingApproachCommand>
{
    public UpdateBusinessUnderstandingApproachValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.ApproachType).IsInEnum().NotEqual(Domain.Enums.BusinessUnderstandingApproachType.Undefined);
        RuleFor(v => v.Version).NotEmpty();
    }
}
