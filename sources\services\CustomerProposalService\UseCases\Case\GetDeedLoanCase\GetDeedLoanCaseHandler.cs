using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.GetDeedLoanCase;

public class GetDeedLoanCaseHandler : IRequestHandler<GetDeedLoanCaseQuery, Result<GetDeedLoanCaseResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetDeedLoanCaseHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetDeedLoanCaseResult>> Handle(GetDeedLoanCaseQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.DeedLoanCase, string>();
        return repository.SingleAsync(new GetDeedLoanCaseSpecification(request))
            .Map(v =>
                new GetDeedLoanCaseResult
                (
                    Id: v.Id,
                    CustomerIdentificationId: v.CustomerIdentificationId,
                    CaseCategory: v.CaseCategory,
                    CaseName: v.CaseName,
                    CaseStatus: v.CaseStatus,
                    CaseOutline: v.CaseOutline,
                    ExpiredAt: v.ExpiredAt,
                    StaffId: v.StaffId,
                    StaffName: v.StaffName,
                    RegisteredAt: v.RegisteredAt,
                    CaseUpdatedAt: v.CaseUpdatedAt,
                    LastUpdatedAt: v.CaseUpdateInformation.LastUpdatedAt,
                    CaseFiles: v.CaseFiles,
                    CaseLinks: v.CaseLinks,
                    Version: v.Version,
                    AccountType: v.AccountType,
                    Amount: v.Amount,
                    Period: v.Period,
                    ApplicableBaseInterestRate: v.ApplicableBaseInterestRate,
                    InterestRate: v.InterestRate,
                    InterestRateCustom: v.InterestRateCustom,
                    BaseInterestRate: v.BaseInterestRate,
                    Spread: v.Spread,
                    LoanPurposeCode: v.LoanPurposeCode,
                    LoanPurposeCustom: v.LoanPurposeCustom,
                    RelatedEsg: v.RelatedEsg,
                    PreConsultationStandardTarget: v.PreConsultationStandardTarget,
                    IsEarthquakeRelated: v.IsEarthquakeRelated,
                    RepaymentType: v.RepaymentType,
                    RepaymentSourceCode: v.RepaymentSourceCode,
                    CollateralType: v.CollateralType,
                    CollateralOrGuaranteeCustom: v.CollateralOrGuaranteeCustom,
                    GuaranteeType: v.GuaranteeType,
                    CancelTypeOfLoan: v.CancelTypeOfLoan,
                    CancelReason: v.CancelReason,
                    TrafficSource: v.TrafficSource
                ));
    }
}
