
using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.GetOtherLeaseCase;

public class GetOtherLeaseCaseHandler : IRequestHandler<GetOtherLeaseCaseQuery, Result<GetOtherLeaseCaseResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetOtherLeaseCaseHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetOtherLeaseCaseResult>> Handle(GetOtherLeaseCaseQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.OtherLeaseCase, string>();
        return repository.SingleAsync(new GetOtherLeaseCaseSpecification(request))
            .Map(v =>
                new GetOtherLeaseCaseResult
                (
                    v.Id,
                    v.CustomerIdentificationId,
                    v.CaseCategory,
                    v.CaseName,
                    v.CaseStatus,
                    v.CaseOutline,
                    v.ExpiredAt,
                    v.StaffId,
                    v.StaffName,
                    v.RegisteredAt,
                    v.CaseUpdatedAt,
                    v.CaseUpdateInformation.LastUpdatedAt,
                    v.CaseFiles,
                    v.CaseLinks,
                    v.IsEarthquakeRelated,
                    v.Version
                    ));
    }
}
