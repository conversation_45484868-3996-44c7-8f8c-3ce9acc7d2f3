using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Domain.Entities;
using BusinessCustomerUnderstandingService.Services.Queries;
using MediatR;
using Nut.Results;
using Shared.Domain;
using Shared.Services;

namespace BusinessCustomerUnderstandingService.UseCases.CustomerIdeasUnderstanding.AddCustomerIdeasUnderstanding;

public class AddCustomerIdeasUnderstandingHandler : IRequestHandler<AddCustomerIdeasUnderstandingCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IGetCommunicationPlanCountQueryService _queryService;
    private readonly ICurrentDateTimeService _currentDatetimeService;

    public AddCustomerIdeasUnderstandingHandler(IUnitOfWork unitOfWork, IGetCommunicationPlanCountQueryService queryService, ICurrentDateTimeService currentDateTimeService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
        _currentDatetimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
    }

    public async Task<Result<string>> Handle(AddCustomerIdeasUnderstandingCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 更新する値を作成します。
        var newData = new Domain.Entities.CustomerIdeasUnderstanding()
        {
            // キーの値をUlidで生成します。
            Id = Ulid.NewUlid().ToString(),
            Title = request.Title,
            Status = request.Status,
            Order = request.Order,
            ExpiredAt = request.ExpiredAt,
            StaffId = request.StaffId,
            StaffName = request.StaffName,
            TargetPerson = request.TargetPerson,
            Description = request.Description,
            Note = request.Note,
            RegisteredDateTime = _currentDatetimeService.NowDateTimeOffset(),
            UpdatedDateTime = request.UpdatedDateTime,
            UpdaterId = request.UpdaterId,
            UpdaterName = request.UpdaterName,
            RegistrantId = request.RegistrantId,
            RegistrantName = request.RegistrantName,
            BusinessUnderstandingId = request.BusinessUnderstandingId,
        };

        if (newData.Status == Domain.Enums.Status.Completed)
        {
            newData.CompletedDateTime = _currentDatetimeService.NowDateTimeOffset();
        }

        var repository = _unitOfWork.GetRepository<Domain.Entities.CustomerIdeasUnderstanding>();
        var addResult = await repository.AddAsync(newData);
        if (addResult.IsError) return addResult.PreserveErrorAs<string>();

        // 保存
        await _unitOfWork.SaveEntitiesAsync();

        // 事業性理解の取得
        var businessUnderstandingRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding>();
        var getBusinessUnderstandingResult = await businessUnderstandingRepository.SingleAsync(
            new FindByIdSpecification<Domain.Entities.BusinessUnderstanding, string>(request.BusinessUnderstandingId)
            .Include(x => x.BusinessCustomer)
            );
        if (getBusinessUnderstandingResult.IsError) return getBusinessUnderstandingResult.PreserveErrorAs<string>();
        var businessUnderstanding = getBusinessUnderstandingResult.Get();

        // キューの登録
        var queue = new Domain.Entities.BusinessUnderstandingMaterializedViewQueue()
        {
            Id = Ulid.NewUlid().ToString(),
            BusinessUnderstandingId = request.BusinessUnderstandingId,
            CustomerIdentificationId = businessUnderstanding.BusinessCustomer.CustomerIdentificationId,
            NumberOfRetries = 0,
            UpdaterId = request.UpdaterId!,
            UpdaterName = request.UpdaterName!,
            UpdatedDateTime = _currentDatetimeService.NowDateTimeOffset()
        };
        var queueRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingMaterializedViewQueue>();
        await queueRepository.AddAsync(queue).ConfigureAwait(false);

        // 保存
        await _unitOfWork.SaveEntitiesAsync();

        return Result.Ok(newData.Id);
    }
}
