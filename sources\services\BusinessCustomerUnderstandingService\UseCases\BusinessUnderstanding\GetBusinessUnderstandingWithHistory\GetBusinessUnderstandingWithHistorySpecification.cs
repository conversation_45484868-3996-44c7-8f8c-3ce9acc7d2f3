using Microsoft.EntityFrameworkCore;
using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessUnderstandingWithHistory;

public class GetBusinessUnderstandingWithHistorySpecification : BaseSpecification<Domain.Entities.BusinessUnderstanding>
{
    public GetBusinessUnderstandingWithHistorySpecification(string id)
    {
        Query
            .Where(x => x.Id == id)
            .Include(x => x.ManagementPlan)
            .Include(x => x.Management)
            .Include(x => x.FiveForceFramework)
            .Include(x => x.FiveStepFrameWork)
            .Include(x => x.ESGAndSDGs)
            .Include(x => x.ExternalEnvironment, x => x.ThenInclude(x => x!.ExternalEnvironmentMaster))
            .Include(x => x.RelationLevel)
            .Include(x => x.BusinessCustomer)
            .Include(x => x.Histories)
            .Include(x => x.BusinessUnderstandingMaterializedView)
            .AsNoTracking();
    }
}
