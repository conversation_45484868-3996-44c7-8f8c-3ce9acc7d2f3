using BusinessCustomerUnderstandingService.Infrastructure.Storage;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.UploadBatchAddCommunicationPlanFile;

public class UploadBatchAddCommunicationPlanFileHandler : IRequestHandler<UploadBatchAddCommunicationPlanFileCommand, Result<string>>
{
    private readonly IBatchAddCommunicationPlanStorageClientProvider _objectStorageClientProvider;

    // 取り込みファイル名
    private readonly string _batchAddCommunicationPlanFileName = "batchAddCommunicationPlan.csv";


    public UploadBatchAddCommunicationPlanFileHandler(IBatchAddCommunicationPlanStorageClientProvider objectStorageClientProvider)
    {
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
    }

    public async Task<Result<string>> Handle(UploadBatchAddCommunicationPlanFileCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // Blobとの接続を確立
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        // Blobに登録
        var folderName = DateTime.Now.ToString("yyyyMMddHHmmss");
        var blobResult = await storageClient.PostAsync($"{folderName}/{_batchAddCommunicationPlanFileName}", request.Data).ConfigureAwait(false);
        if (blobResult.IsError)
            return blobResult.PreserveErrorAs<string>();

        return Result.Ok(folderName);
    }
}
