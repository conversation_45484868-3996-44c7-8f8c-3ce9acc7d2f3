using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThreadReaction.GetBUThreadReaction;

public class GetBUThreadReactionHandler : IRequestHandler<GetBUThreadReactionQuery, Result<GetBUThreadReactionResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetBUThreadReactionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetBUThreadReactionResult>> Handle(GetBUThreadReactionQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingThreadReaction, string>();
        return repository.GetAsync(request.Id)
            .Map(v => new GetBUThreadReactionResult(
                v.Id,
                v.ThreadId,
                v.StaffId,
                v.Staff<PERSON>ame,
                v.ReactionType,
                v.UpdatedDateTime,
                v.Version));
    }
}
