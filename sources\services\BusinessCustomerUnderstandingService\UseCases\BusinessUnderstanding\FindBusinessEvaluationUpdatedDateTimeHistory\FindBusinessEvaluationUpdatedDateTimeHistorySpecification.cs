using Microsoft.EntityFrameworkCore;
using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindBusinessEvaluationUpdatedDateTimeHistory;

public class FindBusinessEvaluationUpdatedDateTimeHistorySpecification : BaseSpecification<Domain.Entities.ManagementPlan>
{
    public FindBusinessEvaluationUpdatedDateTimeHistorySpecification(string businessUnderstandingId)
    {
        Query
            .Where(x => x.BusinessUnderstandingId == businessUnderstandingId)
            .Include(x => x.Histories)
            .AsNoTracking();
    }
}
