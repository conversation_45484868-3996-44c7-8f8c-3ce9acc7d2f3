using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThreadReaction.AddBUThreadReaction;

public class AddBUThreadReactionHandler : IRequestHandler<AddBUThreadReactionCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;

    public AddBUThreadReactionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<string>> Handle(AddBUThreadReactionCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 更新する値を作成します。
        var newData = new Domain.Entities.BusinessUnderstandingThreadReaction()
        {
            // キーの値をUlidで生成します。
            Id = Ulid.NewUlid().ToString(),
            ThreadId = request.ThreadId,
            ReactionType = request.ReactionType,
            StaffId = request.StaffId,
            StaffName = request.StaffName,
            UpdatedDateTime = request.UpdatedDateTime,
        };

        var reactionRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingThreadReaction>();

        // 通知を飛ばすタスクで使う
        //var issueProjectDiscussionThreadRepo = _unitOfWork.GetRepository<Domain.Entities.IssueProjectDiscussionThread>();
        //var issueProjectDiscussionThreadGetResult = await issueProjectDiscussionThreadRepo.SingleAsync(
        //    new FindByIdSpecification<Domain.Entities.IssueProjectDiscussionThread, string>(request.IsssueProjectDiscussionThreadId))
        //    .ConfigureAwait(false);
        //if (issueProjectDiscussionThreadGetResult.IsError) return issueProjectDiscussionThreadGetResult.PreserveErrorAs<string>();
        //var issueProjectDiscussionThread = issueProjectDiscussionThreadGetResult.Get();

        //var notificationTargetUserId = issueProjectDiscussionThread.RegistrantId;

        return await reactionRepository
            .AddAsync(newData) // データを追加します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(newData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
