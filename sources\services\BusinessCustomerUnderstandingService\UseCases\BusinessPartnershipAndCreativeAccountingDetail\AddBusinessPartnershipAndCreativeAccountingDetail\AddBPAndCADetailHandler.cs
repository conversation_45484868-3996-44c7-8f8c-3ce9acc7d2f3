using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Externals.BusinessCustomerUnderstanding;
using BusinessCustomerUnderstandingService.UseCases.BusinessPartnershipAndCreativeAccountingDetail.GetBusinessPartnershipAndCreativeAccountingDetail;
using MediatR;
using Nut.Results;
using Shared.Messaging;
using Shared.Results.Errors;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartnershipAndCreativeAccountingDetail.AddBusinessPartnershipAndCreativeAccountingDetail;

public class AddBPAndCADetailHandler : IRequestHandler<AddBPAndCADetailCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageSender<UpdateBusinessUnderstandingUpdatedDateTime> _businessUnderstandingUpdateMessageSender;

    public AddBPAndCADetailHandler(
        IUnitOfWork unitOfWork,
        IMessageSender<UpdateBusinessUnderstandingUpdatedDateTime> businessUnderstandingUpdateMessageSender
        )
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _businessUnderstandingUpdateMessageSender = businessUnderstandingUpdateMessageSender ?? throw new ArgumentNullException(nameof(businessUnderstandingUpdateMessageSender));
    }

    public async Task<Result<string>> Handle(AddBPAndCADetailCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessPartnershipAndCreativeAccountingDetail>();

        // 既存データを取得します。
        var getExistingData = await repository.FindAsync(
            new GetBPAndCADetailSpecification(null, request.BusinessUnderstandingId)
            ).ConfigureAwait(false);

        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getExistingData.IsError) return Result.Error<string>(getExistingData.GetError());

        if (getExistingData.Get().Any())
        {
            return Result.Error<string>(new ChangeConflictException("すでに別のユーザーがデータを登録しているため保存できませんでした。"));
        }

        // 更新する値を作成します。
        var newData = new Domain.Entities.BusinessPartnershipAndCreativeAccountingDetail()
        {
            // キーの値をUlidで生成します。
            Id = Ulid.NewUlid().ToString(),
            BusinessUnderstandingId = request.BusinessUnderstandingId,
            HasBusinessPartnershipWithOurCompany = request.HasBusinessPartnershipWithOurCompany,
            FeatureOfBusinessPartnership = request.FeatureOfBusinessPartnership,
            HasCreativeAccountingIncident = request.HasCreativeAccountingIncident,
            DescriptionOfCreativeAccountingIncident = request.DescriptionOfCreativeAccountingIncident
        };

        // 事業性理解の最終更新日更新
        var updateBusinessUnderstandingResult = await UpdateBusinessUnderstandingUpdatedDateTime(request);
        if (updateBusinessUnderstandingResult.IsError) return updateBusinessUnderstandingResult.PreserveErrorAs<string>();

        return await repository
            .AddAsync(newData) // データを追加します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(newData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }

    // 事業性理解の最終更新日を更新
    private async Task<Result> UpdateBusinessUnderstandingUpdatedDateTime(AddBPAndCADetailCommand request)
    {
        var updateBusinessUnderstandingResult = await _businessUnderstandingUpdateMessageSender.SendAsync(new UpdateBusinessUnderstandingUpdatedDateTime()
        {
            BusinessUnderstandingId = request.BusinessUnderstandingId,
            UpdaterId = request.StaffId,
            UpdaterName = request.StaffName,
        });

        return updateBusinessUnderstandingResult;
    }
}
