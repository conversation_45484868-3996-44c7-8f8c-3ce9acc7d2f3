using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessEvaluationAtTheTime;

public class GetManagementHistorySpecification : BaseSpecification<Domain.Entities.ManagementHistory>
{
    public GetManagementHistorySpecification(string originalId, DateTimeOffset targetDateTime)
    {
        Query.Where(x => x.OriginalId == originalId && x.UpdatedDateTime == targetDateTime).AsNoTracking();
    }
}
