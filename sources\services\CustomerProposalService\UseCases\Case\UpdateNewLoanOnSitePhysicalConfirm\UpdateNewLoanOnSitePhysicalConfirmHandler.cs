using CustomerProposalService.Domain;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;
using Shared.Domain;
using Shared.Services;

namespace CustomerProposalService.UseCases.Case.UpdateNewLoanOnSitePhysicalConfirm;

public class UpdateNewLoanOnSitePhysicalConfirmHandler : IRequestHandler<UpdateNewLoanOnSitePhysicalConfirmCommand, Result<UpdateNewLoanOnSitePhysicalConfirmResult>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDateTimeService;

    public UpdateNewLoanOnSitePhysicalConfirmHandler(
        IUnitOfWork unitOfWork,
        ICurrentDateTimeService currentDateTimeService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
    }

    public async Task<Result<UpdateNewLoanOnSitePhysicalConfirmResult>> Handle(UpdateNewLoanOnSitePhysicalConfirmCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.NewLoanCase, string>();

        var getResult = await repository.SingleAsync(
            new FindByIdSpecification<Domain.Entities.NewLoanCase, string>(request.Id)
                .Include(x => x.CaseUpdateInformation));
        if (getResult.IsError) return getResult.PreserveErrorAs<UpdateNewLoanOnSitePhysicalConfirmResult>();

        var currentData = getResult.Get();

        currentData.OnSitePhysicalConfirmer = request.OnSitePhysicalConfirmer;
        currentData.OnSitePhysicalConfirmationDateTime = request.OnSitePhysicalConfirmationDateTime;
        currentData.CaseUpdateInformation.LastUpdatedAt = _currentDateTimeService.NowDateTimeOffset();
        currentData.Version = request.Version;

        return await repository
            .UpdateAsync(currentData)
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync())
            .FlatMap(() => Result.Ok(new UpdateNewLoanOnSitePhysicalConfirmResult(currentData.Version)))
            .ConfigureAwait(false);
    }
}
