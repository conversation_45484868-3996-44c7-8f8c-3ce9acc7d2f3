using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Domain.Entities;
using MediatR;
using Nut.Results;
using Shared.Services;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.AddBusinessUnderstanding;

public class AddBusinessUnderstandingHandler : IRequestHandler<AddBusinessUnderstandingCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDateTimeService;

    public AddBusinessUnderstandingHandler(IUnitOfWork unitOfWork, ICurrentDateTimeService currentDateTimeService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
    }

    public async Task<Result<string>> Handle(AddBusinessUnderstandingCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var now = _currentDateTimeService.NowDateTimeOffset();

        // 顧客識別IDを持つ法人が存在しないかチェック
        var businessCustomerRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessCustomer>();
        var getResult = await businessCustomerRepository.FindAsync(new FindBusinessCustomerSpecification(request.CustomerIdentificationId)).ConfigureAwait(false);
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();
        // 存在している場合はエラーを返す
        if (getResult.Get().Any()) return Result.Error<string>(new Exception("法人データが既に存在しています。"));

        // 法人を登録
        var businessCustomer = new Domain.Entities.BusinessCustomer()
        {
            // キーの値をUlidで生成します。
            Id = Ulid.NewUlid().ToString(),
            CustomerIdentificationId = request.CustomerIdentificationId,
        };

        var addBusinessCustomerResult = await businessCustomerRepository.AddAsync(businessCustomer).ConfigureAwait(false);
        if (addBusinessCustomerResult.IsError) return addBusinessCustomerResult.PreserveErrorAs<string>();

        // 事業性理解を登録
        var businessUnderstanding = new Domain.Entities.BusinessUnderstanding()
        {
            // キーの値をUlidで生成します。
            Id = Ulid.NewUlid().ToString(),
            BusinessCustomerId = businessCustomer.Id,
            ManagementPlan = new ManagementPlan { Id = Ulid.NewUlid().ToString(), UpdatedDateTime = now, UpdaterId = "System", UpdaterName = "System" },
            Management = new Management { Id = Ulid.NewUlid().ToString(), UpdatedDateTime = now, UpdaterId = "System", UpdaterName = "System" },
            FiveForceFramework = new FiveForceFramework { Id = Ulid.NewUlid().ToString(), UpdatedDateTime = now, UpdaterId = "System", UpdaterName = "System" },
            FiveStepFrameWork = new FiveStepFrameWork { Id = Ulid.NewUlid().ToString(), UpdatedDateTime = now, UpdaterId = "System", UpdaterName = "System" },
            ESGAndSDGs = new ESGAndSDGs { Id = Ulid.NewUlid().ToString(), UpdatedDateTime = now, UpdaterId = "System", UpdaterName = "System" },
            ExternalEnvironment = new Domain.Entities.ExternalEnvironment { Id = Ulid.NewUlid().ToString(), ExternalEnvironmentMasterId = request.IndustryCode, UpdatedDateTime = now, UpdaterId = "System", UpdaterName = "System" },
            RelationLevel = new Domain.Entities.RelationLevel { Id = Ulid.NewUlid().ToString(), UpdatedDateTime = now, UpdaterId = "System", UpdaterName = "System" },
            ThreeCAnalysis = new Domain.Entities.ThreeCAnalysis { Id = Ulid.NewUlid().ToString(), UpdatedDateTime = now, UpdaterId = "System", UpdaterName = "System" },
            SWOTAnalysis = new Domain.Entities.SWOTAnalysis { Id = Ulid.NewUlid().ToString(), UpdatedDateTime = now, UpdaterId = "System", UpdaterName = "System" },
            CommercialDistribution = new Domain.Entities.CommercialDistribution { Id = Ulid.NewUlid().ToString(), CanvasColor = "#fff", UpdatedDateTime = now, UpdaterId = "System", UpdaterName = "System" },
            FamilyTree = new Domain.Entities.FamilyTree { Id = Ulid.NewUlid().ToString(), UpdatedDateTime = now, UpdaterId = "System", UpdaterName = "System" },
            // 取引方針確認者・確認日時
            TransactionPolicyConfirmerId = null,
            TransactionPolicyConfirmedDateTime = null,

            // ToDo:BusinessUnderstandingMaterializedViewQueueの本番適用後に削除
            TransactionPolicy = string.Empty,
            CommunicationPlanCount = 0,
        };

        var businessUnderstandingRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding>();
        var addBusinessUnderstandingResult = await businessUnderstandingRepository.AddAsync(businessUnderstanding).ConfigureAwait(false);
        if (addBusinessUnderstandingResult.IsError) return addBusinessUnderstandingResult.PreserveErrorAs<string>();

        // キュー登録
        var queue = new Domain.Entities.BusinessUnderstandingMaterializedViewQueue()
        {
            Id = Ulid.NewUlid().ToString(),
            BusinessUnderstandingId = businessUnderstanding.Id,
            CustomerIdentificationId = request.CustomerIdentificationId,
            NumberOfRetries = 0,
            UpdaterId = "System",
            UpdaterName = "System",
            UpdatedDateTime = now
        };
        var queueRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingMaterializedViewQueue>();
        var addQueueResult = await queueRepository.AddAsync(queue).ConfigureAwait(false);
        if (addQueueResult.IsError) return addQueueResult.PreserveErrorAs<string>();

        // 保存
        var result = await _unitOfWork.SaveEntitiesAsync().ConfigureAwait(false);
        if (result.IsError) return result.PreserveErrorAs<string>();

        return await Task.FromResult(Result.Ok(businessUnderstanding.Id));
    }
}
