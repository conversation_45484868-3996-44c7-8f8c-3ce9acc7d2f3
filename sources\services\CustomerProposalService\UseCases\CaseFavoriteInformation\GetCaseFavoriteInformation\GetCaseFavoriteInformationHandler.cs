using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseFavoriteInformation.GetCaseFavoriteInformation;

public class GetCaseFavoriteInformationHandler : IRequestHandler<GetCaseFavoriteInformationQuery, Result<GetCaseFavoriteInformationResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetCaseFavoriteInformationHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetCaseFavoriteInformationResult>> Handle(GetCaseFavoriteInformationQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CaseFavoriteInformation, string>();
        return repository.GetAsync(request.Id)
            .Map(v => new GetCaseFavoriteInformationResult(
                v.Id,
                v.UserId,
                v.CaseId,
                v.IsFavorite,
                v.Version
                ));
    }
}
