using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingFile.FindBusinessUnderstandingFile;

public class FindBusinessUnderstandingFileSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingFile>
{
    public FindBusinessUnderstandingFileSpecification(FindBusinessUnderstandingFileQuery request)
    {
        Query
            .Where(e => e.BusinessUnderstandingId == request.BusinessUnderstandingId)
            .AsNoTracking();
    }
}
