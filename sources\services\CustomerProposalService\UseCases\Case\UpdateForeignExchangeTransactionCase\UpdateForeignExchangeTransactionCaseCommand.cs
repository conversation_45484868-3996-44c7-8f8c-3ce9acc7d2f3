using CustomerProposalService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.UpdateForeignExchangeTransactionCase;

[WithDefaultBehaviors]
public record UpdateForeignExchangeTransactionCaseCommand(
    // Case Entity
    string Id,
    string CaseName,
    CaseStatus CaseStatus,
    string? CaseOutline,
    DateTimeOffset? ExpiredAt,
    string StaffId,
    string StaffName,
    IEnumerable<IFormFile>? UploadFiles,
    IEnumerable<string>? CaseLinks,
    // ForeignExchangeTransactionCase Entity
    string? ApprovalType,
    decimal? Amount,
    bool RelatedEsg,
    bool PreConsultationStandard,
    bool IsEarthquakeRelated,
    string? CollateralType,
    string? CollateralOrGuaranteeCustom,
    string? GuaranteeType,
    CancelTypeOfLoan? CancelTypeOfLoan,
    string? CancelReason,
    IEnumerable<Domain.Entities.Guarantor>? Guarantors,
    string Version
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
