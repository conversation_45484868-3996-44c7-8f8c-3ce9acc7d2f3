using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Domain.Entities;
using BusinessCustomerUnderstandingService.Externals.ServiceManagement;
using MediatR;
using Nut.Results;
using Shared.Domain;
using Shared.Messaging;
using Shared.Services;
using Shared.Spec;
using Dto = BusinessCustomerUnderstandingService.Domain.Dto.BusinessUnderstanding;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.UpdateBusinessUnderstandingApproach;

public class UpdateBusinessUnderstandingApproachHandler : IRequestHandler<UpdateBusinessUnderstandingApproachCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDateTimeService;
    private readonly IMessageSender<GetAllowanceByBusinessCustomerId, List<GetAllowanceByBusinessCustomerIdResult>> _getAllowanceSender;
    private readonly IMessageSender<DeleteAllowance, string> _deleteAllowanceSender;

    public UpdateBusinessUnderstandingApproachHandler(IUnitOfWork unitOfWork,
        IMessageSender<GetAllowanceByBusinessCustomerId,
        List<GetAllowanceByBusinessCustomerIdResult>> getAllowanceSender,
        IMessageSender<DeleteAllowance, string> deleteAllowanceSender,
        ICurrentDateTimeService currentDateTimeService
        )
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _getAllowanceSender = getAllowanceSender ?? throw new ArgumentNullException(nameof(getAllowanceSender));
        _deleteAllowanceSender = deleteAllowanceSender ?? throw new ArgumentNullException(nameof(deleteAllowanceSender));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
    }

    public async Task<Result<string>> Handle(UpdateBusinessUnderstandingApproachCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingApproach, string>();
        // 既存データを取得します。
        var getResult = await repository.GetAsync(request.Id, true);
        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();
        var isUpdatedApproachType = currentData.ApproachType != request.ApproachType;
        var isUpdatedComment = currentData.Comment != request.Comment;

        // 変更なしならば何もしない
        if (!isUpdatedApproachType && !isUpdatedComment)
            return Result.Ok(currentData.Id);

        // 更新
        currentData.ApproachType = request.ApproachType;
        currentData.Comment = request.Comment;
        currentData.Version = request.Version;
        await repository.UpdateAsync(currentData);

        // 事業性理解への取組方針が変わっていない場合は以降の処理は行わない
        if (!isUpdatedApproachType)
        {
            // DB保存
            var result = await _unitOfWork.SaveEntitiesAsync();
            if (result.IsError)
                return result.PreserveErrorAs<string>();
            return Result.Ok(currentData.Id);
        }

        // 事業性理解取得
        var bizUnderstandingRepo = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding, string>();
        var bizUnderstandingGetResult = await bizUnderstandingRepo.SingleAsync(
            new FindByIdSpecification<Domain.Entities.BusinessUnderstanding, string>(request.BusinessUnderstandingId)
            .Include(x => x.ExternalEnvironment, x => x.ThenInclude(x => x!.ExternalEnvironmentMaster))
            .Include(x => x.BusinessCustomer)
            .Include(x => x.Histories)
            ).ConfigureAwait(false);
        if (bizUnderstandingGetResult.IsError) return bizUnderstandingGetResult.PreserveErrorAs<string>();
        var bizUnderstanding = bizUnderstandingGetResult.Get();

        var history = (bizUnderstanding.Histories.Any()) ? bizUnderstanding.Histories.OrderByDescending(x => x.UpdatedDateTime).First() : null;

        // 履歴の追加
        var newHistory = new BusinessUnderstandingHistory()
        {
            Id = Ulid.NewUlid().ToString(),
            // 取引方針
            TransactionPolicy = history is not null ? history.TransactionPolicy : string.Empty,
            // 事業性理解評点
            BusinessEvaluation = history is not null ? history.BusinessEvaluation : 0,
            ManagementPlanScore = history is not null ? history.ManagementPlanScore : 0,
            ManagementScore = history is not null ? history.ManagementScore : 0,
            FiveForceScore = history is not null ? history.FiveForceScore : 0,
            FiveStepScore = history is not null ? history.FiveStepScore : 0,
            ExternalEnvironmentScore = history is not null ? history.ExternalEnvironmentScore : bizUnderstanding.ExternalEnvironment!.ExternalEnvironmentMaster!.Score,
            ESGAndSDGsScore = history is not null ? history.ESGAndSDGsScore : 0,
            // リレーションレベル評点
            RelationLevelEvaluation = history is not null ? history.RelationLevelEvaluation : 0,
            AuthorityScore = history is not null ? history.AuthorityScore : 0,
            RelationScore = history is not null ? history.RelationScore : 0,
            DisclosureScore = history is not null ? history.DisclosureScore : 0,
            // 事業整理解の取り組み方
            ApproachType = request.ApproachType,
            // 更新日時等
            UpdatedDateTime = _currentDateTimeService.NowDateTimeOffset(),
            UpdaterDisplayName = request.UpdaterName,
            UpdaterId = request.UpdaterId,
            OriginalId = bizUnderstanding.Id,
        };
        var historyRepository = _unitOfWork.GetRepository<BusinessUnderstandingHistory>();
        await historyRepository.AddAsync(newHistory);

        // キューに登録
        var queue = new Domain.Entities.BusinessUnderstandingMaterializedViewQueue()
        {
            Id = Ulid.NewUlid().ToString(),
            BusinessUnderstandingId = bizUnderstanding.Id,
            CustomerIdentificationId = bizUnderstanding.BusinessCustomer.CustomerIdentificationId,
            NumberOfRetries = 0,
            UpdaterId = request.UpdaterId,
            UpdaterName = request.UpdaterName,
            UpdatedDateTime = _currentDateTimeService.NowDateTimeOffset()
        };
        var queueRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingMaterializedViewQueue>();
        await queueRepository.AddAsync(queue).ConfigureAwait(false);

        // DB保存
        var saveResult = await _unitOfWork.SaveEntitiesAsync();
        if (saveResult.IsError)
            return saveResult.PreserveErrorAs<string>();

        return Result.Ok(request.Id);
    }
}
