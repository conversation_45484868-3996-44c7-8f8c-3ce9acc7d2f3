using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Services.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThread.GetBusinessUnderstandingThread;

public class GetBusinessUnderstandingThreadHandler : IRequestHandler<GetBusinessUnderstandingThreadQuery, Result<GetBusinessUnderstandingThreadResult>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IQuillImageService _quillImageService;


    public GetBusinessUnderstandingThreadHandler(IUnitOfWork unitOfWork, IQuillImageService quillImageService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _quillImageService = quillImageService ?? throw new ArgumentNullException(nameof(quillImageService));

    }

    public async Task<Result<GetBusinessUnderstandingThreadResult>> Handle(GetBusinessUnderstandingThreadQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingThread, string>();
        var repositoryResult = await repository.SingleAsync(new GetBusinessUnderstandingThreadSpecification(request.Id));
        if (repositoryResult.IsError) return Result.Error<GetBusinessUnderstandingThreadResult>(repositoryResult.GetError());

        var data = repositoryResult.Get();

        // 協議返信コメントを再取得して返却
        var newDelta = await _quillImageService.GetArrangedDeltaWhenPublish(data.Description);

        return newDelta.IsError
            ? Result.Error<GetBusinessUnderstandingThreadResult>(newDelta.GetError())
            : (Result<GetBusinessUnderstandingThreadResult>)new GetBusinessUnderstandingThreadResult(
                data.Id,
                data.RegisteredDateTime,
                data.Title,
                data.Registrant,
                data.RegistrantId,
                newDelta.Get(),
                data.IsCorporateDepositTheme,
                data.IsFundSettlementTheme,
                data.BusinessUnderstandingId,
                data.Discussions,
                data.Reactions,
                data.Files,
                data.MentionTargetsHtml,
                data.MentionTargetUserIds,
                data.Purpose,
                data.Person,
                data.IsPersonOfPower,
                data.CorrespondenceDate,
                data.Version);
    }
}
