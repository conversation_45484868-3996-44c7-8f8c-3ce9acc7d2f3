﻿// <auto-generated />
using System;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250818070415_initial")]
    partial class initial
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.19")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessCustomer", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<Guid>("CustomerIdentificationId")
                        .HasMaxLength(36)
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("customer_identification_id");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_business_customers");

                    b.HasIndex("CustomerIdentificationId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CustomerIdentificationId"), false);

                    b.ToTable("business_customers");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessPartner", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<Guid?>("BusinessPartnerCustomerIdentificationId")
                        .HasMaxLength(36)
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("business_partner_customer_identification_id");

                    b.Property<string>("BusinessPartnerCustomerName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("business_partner_customer_name");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("note");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_business_partners");

                    b.HasIndex("BusinessUnderstandingId")
                        .HasDatabaseName("ix_business_partners_business_understanding_id");

                    b.ToTable("business_partners");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessPartnerIndustry", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessPartnerId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_partner_id");

                    b.Property<string>("DetailIndustryCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("detail_industry_code");

                    b.Property<string>("IndustryCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("industry_code");

                    b.Property<string>("SubIndustryCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("sub_industry_code");

                    b.HasKey("Id")
                        .HasName("pk_business_partner_industries");

                    b.HasIndex("BusinessPartnerId")
                        .IsUnique()
                        .HasDatabaseName("ix_business_partner_industries_business_partner_id");

                    b.ToTable("business_partner_industries");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessPartnershipAndCreativeAccountingDetail", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("DescriptionOfCreativeAccountingIncident")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_of_creative_accounting_incident");

                    b.Property<string>("FeatureOfBusinessPartnership")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("feature_of_business_partnership");

                    b.Property<bool?>("HasBusinessPartnershipWithOurCompany")
                        .HasColumnType("bit")
                        .HasColumnName("has_business_partnership_with_our_company");

                    b.Property<bool?>("HasCreativeAccountingIncident")
                        .HasColumnType("bit")
                        .HasColumnName("has_creative_accounting_incident");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_business_partnership_and_creative_accounting_details");

                    b.HasIndex("BusinessUnderstandingId")
                        .IsUnique()
                        .HasDatabaseName("ix_business_partnership_and_creative_accounting_details_business_understanding_id");

                    b.ToTable("business_partnership_and_creative_accounting_details");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessCustomerId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_customer_id");

                    b.Property<int>("CommunicationPlanCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0)
                        .HasColumnName("communication_plan_count");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("CustomerStaffId")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("customer_staff_id");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("TransactionPolicy")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)")
                        .HasColumnName("transaction_policy");

                    b.Property<DateTimeOffset?>("TransactionPolicyConfirmedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("transaction_policy_confirmed_date_time");

                    b.Property<string>("TransactionPolicyConfirmerId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("transaction_policy_confirmer_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_business_understandings");

                    b.HasIndex("BusinessCustomerId")
                        .HasDatabaseName("ix_business_understandings_business_customer_id");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("BusinessCustomerId"), false);

                    b.ToTable("business_understandings");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingApproach", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<int>("ApproachType")
                        .HasColumnType("int")
                        .HasColumnName("approach_type");

                    b.Property<string>("Comment")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("comment");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<Guid>("CustomerIdentificationId")
                        .HasMaxLength(36)
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("customer_identification_id");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_business_understanding_approaches");

                    b.ToTable("business_understanding_approaches");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingDiscussion", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<bool?>("IsCorporateDepositTheme")
                        .HasColumnType("bit")
                        .HasColumnName("is_corporate_deposit_theme");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool?>("IsFundSettlementTheme")
                        .HasColumnType("bit")
                        .HasColumnName("is_fund_settlement_theme");

                    b.Property<bool?>("IsPersonOfPower")
                        .HasColumnType("bit")
                        .HasColumnName("is_person_of_power");

                    b.Property<string>("MentionTargetTeamIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_team_ids");

                    b.Property<string>("MentionTargetUserIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_user_ids");

                    b.Property<string>("Person")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("person");

                    b.Property<string>("Purpose")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("Internal")
                        .HasColumnName("purpose");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("Registrant")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_business_understanding_discussions");

                    b.HasIndex("ThreadId");

                    b.ToTable("business_understanding_discussions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingDiscussionFile", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("DiscussionId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("discussion_id");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)")
                        .HasColumnName("file_name");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_business_understanding_discussion_files");

                    b.HasIndex("DiscussionId");

                    b.ToTable("business_understanding_discussion_files");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingDiscussionReaction", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CommentId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("comment_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<int>("ReactionType")
                        .HasColumnType("int")
                        .HasColumnName("reaction_type");

                    b.Property<string>("StaffId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_business_understanding_discussion_reactions");

                    b.HasIndex("CommentId");

                    b.ToTable("business_understanding_discussion_reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingFile", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("file_name");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_business_understanding_files");

                    b.HasIndex("BusinessUnderstandingId")
                        .HasDatabaseName("ix_business_understanding_files_business_understanding_id");

                    b.ToTable("business_understanding_files");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<int?>("ApproachType")
                        .HasColumnType("int")
                        .HasColumnName("approach_type");

                    b.Property<double>("AuthorityScore")
                        .HasColumnType("float")
                        .HasColumnName("authority_score");

                    b.Property<int>("BusinessEvaluation")
                        .HasColumnType("int")
                        .HasColumnName("business_evaluation");

                    b.Property<double>("DisclosureScore")
                        .HasColumnType("float")
                        .HasColumnName("disclosure_score");

                    b.Property<int>("ESGAndSDGsScore")
                        .HasColumnType("int")
                        .HasColumnName("esg_and_sd_gs_score");

                    b.Property<int>("ExternalEnvironmentScore")
                        .HasColumnType("int")
                        .HasColumnName("external_environment_score");

                    b.Property<int>("FiveForceScore")
                        .HasColumnType("int")
                        .HasColumnName("five_force_score");

                    b.Property<int>("FiveStepScore")
                        .HasColumnType("int")
                        .HasColumnName("five_step_score");

                    b.Property<int>("ManagementPlanScore")
                        .HasColumnType("int")
                        .HasColumnName("management_plan_score");

                    b.Property<int>("ManagementScore")
                        .HasColumnType("int")
                        .HasColumnName("management_score");

                    b.Property<string>("OriginalId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("original_id");

                    b.Property<double>("RelationLevelEvaluation")
                        .HasColumnType("float")
                        .HasColumnName("relation_level_evaluation");

                    b.Property<double>("RelationScore")
                        .HasColumnType("float")
                        .HasColumnName("relation_score");

                    b.Property<string>("TransactionPolicy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("transaction_policy");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdaterDisplayName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_display_name");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.HasKey("Id")
                        .HasName("pk_business_understanding_histories");

                    b.HasIndex("OriginalId");

                    b.ToTable("business_understanding_histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingMaterializedView", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<int?>("BusinessUnderstandingApproachType")
                        .HasColumnType("int")
                        .HasColumnName("business_understanding_approach_type");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<int>("CommunicationPlanCount")
                        .HasColumnType("int")
                        .HasColumnName("communication_plan_count");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<Guid>("CustomerIdentificationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("customer_identification_id");

                    b.Property<string>("CustomerStaffId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("customer_staff_id");

                    b.Property<string>("TransactionPolicy")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("transaction_policy");

                    b.Property<DateTimeOffset?>("TransactionPolicyConfirmedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("transaction_policy_confirmed_date_time");

                    b.Property<string>("TransactionPolicyConfirmerId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("transaction_policy_confirmer_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.HasKey("Id")
                        .HasName("pk_business_understanding_materialized_views");

                    b.HasIndex("BusinessUnderstandingId")
                        .IsUnique()
                        .HasDatabaseName("ix_business_understanding_materialized_views_business_understanding_id");

                    b.ToTable("business_understanding_materialized_views");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingMaterializedViewQueue", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("business_understanding_id");

                    b.Property<Guid>("CustomerIdentificationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("customer_identification_id");

                    b.Property<int>("NumberOfRetries")
                        .HasColumnType("int")
                        .HasColumnName("number_of_retries");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdaterId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.HasKey("Id")
                        .HasName("pk_business_understanding_materialized_view_queues");

                    b.ToTable("business_understanding_materialized_view_queues");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingThread", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<DateTimeOffset?>("CorrespondenceDate")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("correspondence_date");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<bool?>("IsCorporateDepositTheme")
                        .HasColumnType("bit")
                        .HasColumnName("is_corporate_deposit_theme");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool?>("IsFundSettlementTheme")
                        .HasColumnType("bit")
                        .HasColumnName("is_fund_settlement_theme");

                    b.Property<bool?>("IsPersonOfPower")
                        .HasColumnType("bit")
                        .HasColumnName("is_person_of_power");

                    b.Property<string>("MentionTargetTeamIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_team_ids");

                    b.Property<string>("MentionTargetUserIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_user_ids");

                    b.Property<string>("MentionTargetsHtml")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_targets_html");

                    b.Property<string>("Person")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("person");

                    b.Property<string>("Purpose")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("Internal")
                        .HasColumnName("purpose");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("Registrant")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("title");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_business_understanding_threads");

                    b.HasIndex("BusinessUnderstandingId")
                        .HasDatabaseName("ix_business_understanding_threads_business_understanding_id");

                    b.ToTable("business_understanding_threads");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingThreadFile", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)")
                        .HasColumnName("file_name");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_business_understanding_thread_files");

                    b.HasIndex("ThreadId");

                    b.ToTable("business_understanding_thread_files");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingThreadReaction", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<int>("ReactionType")
                        .HasColumnType("int")
                        .HasColumnName("reaction_type");

                    b.Property<string>("StaffId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_business_understanding_thread_reactions");

                    b.HasIndex("ThreadId");

                    b.ToTable("business_understanding_thread_reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistribution", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<string>("CanvasColor")
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)")
                        .HasColumnName("canvas_color");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_commercial_distributions");

                    b.HasIndex("BusinessUnderstandingId")
                        .IsUnique()
                        .HasDatabaseName("ix_commercial_distributions_business_understanding_id");

                    b.ToTable("commercial_distributions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionEdge", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CommercialDistributionId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("commercial_distribution_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("source");

                    b.Property<string>("SourceNode")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("source_node");

                    b.Property<string>("Target")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("target");

                    b.Property<string>("TargetNode")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("target_node");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_commercial_distribution_edges");

                    b.HasIndex("CommercialDistributionId")
                        .HasDatabaseName("ix_commercial_distribution_edges_commercial_distribution_id");

                    b.ToTable("commercial_distribution_edges");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionEdgeHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CommercialDistributionEdgeId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("commercial_distribution_edge_id");

                    b.Property<string>("CommercialDistributionHistoryId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("commercial_distribution_history_id");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("source");

                    b.Property<string>("SourceNode")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("source_node");

                    b.Property<string>("Target")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("target");

                    b.Property<string>("TargetNode")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("target_node");

                    b.HasKey("Id")
                        .HasName("pk_commercial_distribution_edge_histories");

                    b.HasIndex("CommercialDistributionHistoryId")
                        .HasDatabaseName("ix_commercial_distribution_edge_histories_commercial_distribution_history_id");

                    b.ToTable("commercial_distribution_edge_histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CanvasColor")
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)")
                        .HasColumnName("canvas_color");

                    b.Property<string>("OriginalId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("original_id");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.HasKey("Id")
                        .HasName("pk_commercial_distribution_histories");

                    b.HasIndex("OriginalId");

                    b.ToTable("commercial_distribution_histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionNode", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<double?>("Amount")
                        .HasColumnType("float")
                        .HasColumnName("amount");

                    b.Property<string>("Area")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("area");

                    b.Property<string>("BranchNumber")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)")
                        .HasColumnName("branch_number");

                    b.Property<string>("CifNumber")
                        .HasMaxLength(8)
                        .HasColumnType("nvarchar(8)")
                        .HasColumnName("cif_number");

                    b.Property<string>("CityName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("city_name");

                    b.Property<string>("Color")
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)")
                        .HasColumnName("color");

                    b.Property<string>("CommercialDistributionId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("commercial_distribution_id");

                    b.Property<string>("CountryCode")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)")
                        .HasColumnName("country_code");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("CustomerName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("customer_name");

                    b.Property<string>("GroupingId")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("grouping_id");

                    b.Property<double>("Height")
                        .HasColumnType("float")
                        .HasColumnName("height");

                    b.Property<string>("Industry")
                        .HasMaxLength(24)
                        .HasColumnType("nvarchar(24)")
                        .HasColumnName("industry");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool?>("IsInternationalBusinessPartners")
                        .HasColumnType("bit")
                        .HasColumnName("is_international_business_partners");

                    b.Property<double>("Left")
                        .HasColumnType("float")
                        .HasColumnName("left");

                    b.Property<string>("Material")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("material");

                    b.Property<string>("Merchandise")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("merchandise");

                    b.Property<int>("NodeType")
                        .HasColumnType("int")
                        .HasColumnName("node_type");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("note");

                    b.Property<int?>("PersonType")
                        .HasColumnType("int")
                        .HasColumnName("person_type");

                    b.Property<string>("Text")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("text");

                    b.Property<string>("Title")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("title");

                    b.Property<double>("Top")
                        .HasColumnType("float")
                        .HasColumnName("top");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.Property<double>("Width")
                        .HasColumnType("float")
                        .HasColumnName("width");

                    b.HasKey("Id")
                        .HasName("pk_commercial_distribution_nodes");

                    b.HasIndex("CommercialDistributionId")
                        .HasDatabaseName("ix_commercial_distribution_nodes_commercial_distribution_id");

                    b.ToTable("commercial_distribution_nodes");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionNodeHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<double?>("Amount")
                        .HasColumnType("float")
                        .HasColumnName("amount");

                    b.Property<string>("Area")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("area");

                    b.Property<string>("BranchNumber")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)")
                        .HasColumnName("branch_number");

                    b.Property<string>("CifNumber")
                        .HasMaxLength(8)
                        .HasColumnType("nvarchar(8)")
                        .HasColumnName("cif_number");

                    b.Property<string>("CityName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("city_name");

                    b.Property<string>("Color")
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)")
                        .HasColumnName("color");

                    b.Property<string>("CommercialDistributionHistoryId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("commercial_distribution_history_id");

                    b.Property<string>("CommercialDistributionNodeId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("commercial_distribution_node_id");

                    b.Property<string>("CountryCode")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)")
                        .HasColumnName("country_code");

                    b.Property<string>("CustomerName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("customer_name");

                    b.Property<string>("GroupingId")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("grouping_id");

                    b.Property<double>("Height")
                        .HasColumnType("float")
                        .HasColumnName("height");

                    b.Property<string>("Industry")
                        .HasMaxLength(24)
                        .HasColumnType("nvarchar(24)")
                        .HasColumnName("industry");

                    b.Property<bool?>("IsInternationalBusinessPartners")
                        .HasColumnType("bit")
                        .HasColumnName("is_international_business_partners");

                    b.Property<double>("Left")
                        .HasColumnType("float")
                        .HasColumnName("left");

                    b.Property<string>("Material")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("material");

                    b.Property<string>("Merchandise")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("merchandise");

                    b.Property<int>("NodeType")
                        .HasColumnType("int")
                        .HasColumnName("node_type");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("note");

                    b.Property<int?>("PersonType")
                        .HasColumnType("int")
                        .HasColumnName("person_type");

                    b.Property<string>("Text")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("text");

                    b.Property<string>("Title")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("title");

                    b.Property<double>("Top")
                        .HasColumnType("float")
                        .HasColumnName("top");

                    b.Property<double>("Width")
                        .HasColumnType("float")
                        .HasColumnName("width");

                    b.HasKey("Id")
                        .HasName("pk_commercial_distribution_node_histories");

                    b.HasIndex("CommercialDistributionHistoryId")
                        .HasDatabaseName("ix_commercial_distribution_node_histories_commercial_distribution_history_id");

                    b.ToTable("commercial_distribution_node_histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionTemplate", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CanvasColor")
                        .IsRequired()
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)")
                        .HasColumnName("canvas_color");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("TemplateName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("template_name");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_commercial_distribution_templates");

                    b.ToTable("commercial_distribution_templates");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionTemplateEdge", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("source");

                    b.Property<string>("SourceNode")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("source_node");

                    b.Property<string>("Target")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("target");

                    b.Property<string>("TargetNode")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("target_node");

                    b.Property<string>("TemplateId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("template_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_commercial_distribution_template_edges");

                    b.HasIndex("TemplateId");

                    b.ToTable("commercial_distribution_template_edges");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionTemplateNode", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<double?>("Amount")
                        .HasColumnType("float")
                        .HasColumnName("amount");

                    b.Property<string>("Area")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("area");

                    b.Property<string>("BranchNumber")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)")
                        .HasColumnName("branch_number");

                    b.Property<string>("CifNumber")
                        .HasMaxLength(8)
                        .HasColumnType("nvarchar(8)")
                        .HasColumnName("cif_number");

                    b.Property<string>("CityName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("city_name");

                    b.Property<string>("Color")
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)")
                        .HasColumnName("color");

                    b.Property<string>("CountryCode")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)")
                        .HasColumnName("country_code");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("CustomerName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("customer_name");

                    b.Property<string>("GroupingId")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("grouping_id");

                    b.Property<double>("Height")
                        .HasColumnType("float")
                        .HasColumnName("height");

                    b.Property<string>("Industry")
                        .HasMaxLength(24)
                        .HasColumnType("nvarchar(24)")
                        .HasColumnName("industry");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool?>("IsInternationalBusinessPartners")
                        .HasColumnType("bit")
                        .HasColumnName("is_international_business_partners");

                    b.Property<double>("Left")
                        .HasColumnType("float")
                        .HasColumnName("left");

                    b.Property<string>("Material")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("material");

                    b.Property<string>("Merchandise")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("merchandise");

                    b.Property<int>("NodeType")
                        .HasColumnType("int")
                        .HasColumnName("node_type");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("note");

                    b.Property<int?>("PersonType")
                        .HasColumnType("int")
                        .HasColumnName("person_type");

                    b.Property<string>("TemplateId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("template_id");

                    b.Property<string>("Text")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("text");

                    b.Property<string>("Title")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("title");

                    b.Property<double>("Top")
                        .HasColumnType("float")
                        .HasColumnName("top");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.Property<double>("Width")
                        .HasColumnType("float")
                        .HasColumnName("width");

                    b.HasKey("Id")
                        .HasName("pk_commercial_distribution_template_nodes");

                    b.HasIndex("TemplateId");

                    b.ToTable("commercial_distribution_template_nodes");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstanding", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<DateTimeOffset?>("CompletedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("completed_date_time");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .HasMaxLength(10000)
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<DateTimeOffset>("ExpiredAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("expired_at");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Note")
                        .HasMaxLength(10000)
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("note");

                    b.Property<int?>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("RegistrantName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_name");

                    b.Property<string>("StaffId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<int?>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status");

                    b.Property<string>("TargetPerson")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("target_person");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("title");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_customer_ideas_understandings");

                    b.HasIndex("BusinessUnderstandingId")
                        .HasDatabaseName("ix_customer_ideas_understandings_business_understanding_id");

                    b.ToTable("customer_ideas_understandings");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingComment", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<bool>("ContainCustomerReaction")
                        .HasColumnType("bit")
                        .HasColumnName("contain_customer_reaction");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool?>("IsPersonOfPower")
                        .HasColumnType("bit")
                        .HasColumnName("is_person_of_power");

                    b.Property<string>("MentionTargetTeamIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_team_ids");

                    b.Property<string>("MentionTargetUserIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_user_ids");

                    b.Property<string>("Person")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("person");

                    b.Property<string>("Purpose")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("Internal")
                        .HasColumnName("purpose");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("Registrant")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_customer_ideas_understanding_comments");

                    b.HasIndex("ThreadId");

                    b.ToTable("customer_ideas_understanding_comments");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingCommentFile", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CommentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("comment_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)")
                        .HasColumnName("file_name");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_customer_ideas_understanding_comment_files");

                    b.HasIndex("CommentId");

                    b.ToTable("customer_ideas_understanding_comment_files");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingCommentReaction", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CommentId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("comment_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<int>("ReactionType")
                        .HasColumnType("int")
                        .HasColumnName("reaction_type");

                    b.Property<string>("StaffId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_customer_ideas_understanding_comment_reactions");

                    b.HasIndex("CommentId");

                    b.ToTable("customer_ideas_understanding_comment_reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingThread", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<bool>("ContainCustomerReaction")
                        .HasColumnType("bit")
                        .HasColumnName("contain_customer_reaction");

                    b.Property<DateTimeOffset?>("CorrespondenceDate")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("correspondence_date");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("CustomerIdeasUnderstandingId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("customer_ideas_understanding_id");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool?>("IsPersonOfPower")
                        .HasColumnType("bit")
                        .HasColumnName("is_person_of_power");

                    b.Property<string>("MentionTargetTeamIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_team_ids");

                    b.Property<string>("MentionTargetUserIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_user_ids");

                    b.Property<string>("MentionTargetsHtml")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_targets_html");

                    b.Property<string>("Person")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("person");

                    b.Property<string>("Purpose")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("Internal")
                        .HasColumnName("purpose");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("Registrant")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("title");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_customer_ideas_understanding_threads");

                    b.HasIndex("CustomerIdeasUnderstandingId")
                        .HasDatabaseName("ix_customer_ideas_understanding_threads_customer_ideas_understanding_id");

                    b.ToTable("customer_ideas_understanding_threads");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingThreadFile", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)")
                        .HasColumnName("file_name");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_customer_ideas_understanding_thread_files");

                    b.HasIndex("ThreadId");

                    b.ToTable("customer_ideas_understanding_thread_files");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingThreadReaction", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<int>("ReactionType")
                        .HasColumnType("int")
                        .HasColumnName("reaction_type");

                    b.Property<string>("StaffId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_customer_ideas_understanding_thread_reactions");

                    b.HasIndex("ThreadId");

                    b.ToTable("customer_ideas_understanding_thread_reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerLink", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessCustomerId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_customer_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("display_name");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("StaffId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("url");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_customer_links");

                    b.HasIndex("BusinessCustomerId")
                        .HasDatabaseName("ix_customer_links_business_customer_id");

                    b.ToTable("customer_links");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerReaction", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)")
                        .HasColumnName("content");

                    b.Property<string>("HypotheticalDiscussionOfIssuesId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("hypothetical_discussion_of_issues_id");

                    b.Property<string>("Register")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("register");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.HasKey("Id")
                        .HasName("pk_customer_reactions");

                    b.HasIndex("HypotheticalDiscussionOfIssuesId")
                        .IsUnique()
                        .HasDatabaseName("ix_customer_reactions_hypothetical_discussion_of_issues_id");

                    b.ToTable("customer_reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ESGAndSDGs", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<int?>("ConceptOfESGAndSDGs")
                        .HasColumnType("int")
                        .HasColumnName("concept_of_esg_and_sd_gs");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<int?>("DepartmentOfEnvironmentAndManagementRating")
                        .HasColumnType("int")
                        .HasColumnName("department_of_environment_and_management_rating");

                    b.Property<string>("ESGAndSDGsComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("esg_and_sd_gs_comment");

                    b.Property<int?>("EffortsForGreenhouseGasEmissionsRating")
                        .HasColumnType("int")
                        .HasColumnName("efforts_for_greenhouse_gas_emissions_rating");

                    b.Property<string>("EnvironmentAndCarbonNeutralComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("environment_and_carbon_neutral_comment");

                    b.Property<int?>("EnvironmentAndCarbonNeutralRating")
                        .HasColumnType("int")
                        .HasColumnName("environment_and_carbon_neutral_rating");

                    b.Property<string>("Ideal")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ideal");

                    b.Property<bool?>("IsCertificationAndDeclaration")
                        .HasColumnType("bit")
                        .HasColumnName("is_certification_and_declaration");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Issue")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("issue");

                    b.Property<string>("SDGsComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("sd_gs_comment");

                    b.Property<int?>("SDGsRating")
                        .HasColumnType("int")
                        .HasColumnName("sd_gs_rating");

                    b.Property<string>("SubjectsOfCertificationAndDeclaration")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("subjects_of_certification_and_declaration");

                    b.Property<string>("SubjectsOfCertificationAndDeclarationComment")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("subjects_of_certification_and_declaration_comment");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_esg_and_sd_gs");

                    b.HasIndex("BusinessUnderstandingId")
                        .IsUnique()
                        .HasDatabaseName("ix_esg_and_sd_gs_business_understanding_id");

                    b.ToTable("esg_and_sd_gs");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ESGAndSDGsHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<int?>("ConceptOfESGAndSDGs")
                        .HasColumnType("int")
                        .HasColumnName("concept_of_esg_and_sd_gs");

                    b.Property<int?>("DepartmentOfEnvironmentAndManagementRating")
                        .HasColumnType("int")
                        .HasColumnName("department_of_environment_and_management_rating");

                    b.Property<string>("ESGAndSDGsComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("esg_and_sd_gs_comment");

                    b.Property<int?>("EffortsForGreenhouseGasEmissionsRating")
                        .HasColumnType("int")
                        .HasColumnName("efforts_for_greenhouse_gas_emissions_rating");

                    b.Property<string>("EnvironmentAndCarbonNeutralComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("environment_and_carbon_neutral_comment");

                    b.Property<int?>("EnvironmentAndCarbonNeutralRating")
                        .HasColumnType("int")
                        .HasColumnName("environment_and_carbon_neutral_rating");

                    b.Property<string>("Ideal")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ideal");

                    b.Property<bool?>("IsCertificationAndDeclaration")
                        .HasColumnType("bit")
                        .HasColumnName("is_certification_and_declaration");

                    b.Property<string>("Issue")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("issue");

                    b.Property<string>("OriginalId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("original_id");

                    b.Property<string>("SDGsComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("sd_gs_comment");

                    b.Property<int?>("SDGsRating")
                        .HasColumnType("int")
                        .HasColumnName("sd_gs_rating");

                    b.Property<string>("SubjectsOfCertificationAndDeclaration")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("subjects_of_certification_and_declaration");

                    b.Property<string>("SubjectsOfCertificationAndDeclarationComment")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("subjects_of_certification_and_declaration_comment");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.HasKey("Id")
                        .HasName("pk_esg_and_sd_gs_histories");

                    b.HasIndex("OriginalId");

                    b.ToTable("esg_and_sd_gs_histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ExternalEnvironment", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("ExternalEnvironmentMasterId")
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("external_environment_master_id");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_external_environments");

                    b.HasIndex("BusinessUnderstandingId")
                        .IsUnique()
                        .HasDatabaseName("ix_external_environments_business_understanding_id");

                    b.HasIndex("ExternalEnvironmentMasterId")
                        .HasDatabaseName("ix_external_environments_external_environment_master_id");

                    b.ToTable("external_environments");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ExternalEnvironmentHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("ExternalEnvironmentMasterId")
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("external_environment_master_id");

                    b.Property<string>("OriginalId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("original_id");

                    b.Property<int?>("Score")
                        .HasColumnType("int")
                        .HasColumnName("score");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.HasKey("Id")
                        .HasName("pk_external_environment_histories");

                    b.HasIndex("ExternalEnvironmentMasterId")
                        .HasDatabaseName("ix_external_environment_histories_external_environment_master_id");

                    b.HasIndex("OriginalId");

                    b.ToTable("external_environment_histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ExternalEnvironmentMaster", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("name");

                    b.Property<int>("Score")
                        .HasColumnType("int")
                        .HasColumnName("score");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_external_environment_master");

                    b.ToTable("external_environment_masters");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ExternalEnvironmentMasterUploadProgress", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_completed");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset?>("LatestRunDateTimeFrom")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("latest_run_date_time_from");

                    b.Property<DateTimeOffset?>("LatestRunDateTimeTo")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("latest_run_date_time_to");

                    b.Property<string>("ResultMessage")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("result_message");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_external_environment_master_upload_progresses");

                    b.ToTable("external_environment_master_upload_progresses");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTree", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_family_trees");

                    b.HasIndex("BusinessUnderstandingId")
                        .IsUnique()
                        .HasDatabaseName("ix_family_trees_business_understanding_id");

                    b.ToTable("family_trees");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTreeEdge", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("FamilyTreeId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("family_tree_id");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("source");

                    b.Property<string>("SourceNode")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("source_node");

                    b.Property<string>("Target")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("target");

                    b.Property<string>("TargetNode")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("target_node");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_family_tree_edges");

                    b.HasIndex("FamilyTreeId")
                        .HasDatabaseName("ix_family_tree_edges_family_tree_id");

                    b.ToTable("family_tree_edges");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTreeEdgeHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("FamilyTreeEdgeId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("family_tree_edge_id");

                    b.Property<string>("FamilyTreeHistoryId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("family_tree_history_id");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("source");

                    b.Property<string>("SourceNode")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("source_node");

                    b.Property<string>("Target")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("target");

                    b.Property<string>("TargetNode")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("target_node");

                    b.HasKey("Id")
                        .HasName("pk_family_tree_edge_histories");

                    b.HasIndex("FamilyTreeHistoryId")
                        .HasDatabaseName("ix_family_tree_edge_histories_family_tree_history_id");

                    b.ToTable("family_tree_edge_histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTreeFile", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("file_name");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_family_tree_files");

                    b.HasIndex("BusinessUnderstandingId")
                        .HasDatabaseName("ix_family_tree_files_business_understanding_id");

                    b.ToTable("family_tree_files");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTreeHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("OriginalId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("original_id");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.HasKey("Id")
                        .HasName("pk_family_tree_histories");

                    b.HasIndex("OriginalId");

                    b.ToTable("family_tree_histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTreeNode", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("FamilyTreeId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("family_tree_id");

                    b.Property<int>("Height")
                        .HasColumnType("int")
                        .HasColumnName("height");

                    b.Property<bool>("IsAlive")
                        .HasColumnType("bit")
                        .HasColumnName("is_alive");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsGroup")
                        .HasColumnType("bit")
                        .HasColumnName("is_group");

                    b.Property<int>("Left")
                        .HasColumnType("int")
                        .HasColumnName("left");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("name");

                    b.Property<string>("Note")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("note");

                    b.Property<string>("Relationship")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)")
                        .HasColumnName("relationship");

                    b.Property<int>("Top")
                        .HasColumnType("int")
                        .HasColumnName("top");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.Property<int>("Width")
                        .HasColumnType("int")
                        .HasColumnName("width");

                    b.HasKey("Id")
                        .HasName("pk_family_tree_nodes");

                    b.HasIndex("FamilyTreeId")
                        .HasDatabaseName("ix_family_tree_nodes_family_tree_id");

                    b.ToTable("family_tree_nodes");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTreeNodeHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("FamilyTreeHistoryId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("family_tree_history_id");

                    b.Property<string>("FamilyTreeNodeId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("family_tree_node_id");

                    b.Property<int>("Height")
                        .HasColumnType("int")
                        .HasColumnName("height");

                    b.Property<bool>("IsAlive")
                        .HasColumnType("bit")
                        .HasColumnName("is_alive");

                    b.Property<bool>("IsGroup")
                        .HasColumnType("bit")
                        .HasColumnName("is_group");

                    b.Property<int>("Left")
                        .HasColumnType("int")
                        .HasColumnName("left");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("name");

                    b.Property<string>("Note")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("note");

                    b.Property<string>("Relationship")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)")
                        .HasColumnName("relationship");

                    b.Property<int>("Top")
                        .HasColumnType("int")
                        .HasColumnName("top");

                    b.Property<int>("Width")
                        .HasColumnType("int")
                        .HasColumnName("width");

                    b.HasKey("Id")
                        .HasName("pk_family_tree_node_histories");

                    b.HasIndex("FamilyTreeHistoryId")
                        .HasDatabaseName("ix_family_tree_node_histories_family_tree_history_id");

                    b.ToTable("family_tree_node_histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FiveForceFramework", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("AbilityToNegotiateWithSalesPartnersComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ability_to_negotiate_with_sales_partners_comment");

                    b.Property<int?>("AbilityToNegotiateWithSalesPartnersScore")
                        .HasColumnType("int")
                        .HasColumnName("ability_to_negotiate_with_sales_partners_score");

                    b.Property<string>("AbilityToNegotiateWithSuppliersComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ability_to_negotiate_with_suppliers_comment");

                    b.Property<int?>("AbilityToNegotiateWithSuppliersScore")
                        .HasColumnType("int")
                        .HasColumnName("ability_to_negotiate_with_suppliers_score");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<string>("CompetitorComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("competitor_comment");

                    b.Property<int?>("CompetitorScore")
                        .HasColumnType("int")
                        .HasColumnName("competitor_score");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("Ideal")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ideal");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Issue")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("issue");

                    b.Property<string>("NewComerComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("new_comer_comment");

                    b.Property<int?>("NewComerScore")
                        .HasColumnType("int")
                        .HasColumnName("new_comer_score");

                    b.Property<string>("SubstituteArticleComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("substitute_article_comment");

                    b.Property<int?>("SubstituteArticleScore")
                        .HasColumnType("int")
                        .HasColumnName("substitute_article_score");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_five_force_frameworks");

                    b.HasIndex("BusinessUnderstandingId")
                        .IsUnique()
                        .HasDatabaseName("ix_five_force_frameworks_business_understanding_id");

                    b.ToTable("five_force_frameworks");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FiveForceFrameworkHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("AbilityToNegotiateWithSalesPartnersComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ability_to_negotiate_with_sales_partners_comment");

                    b.Property<int?>("AbilityToNegotiateWithSalesPartnersScore")
                        .HasColumnType("int")
                        .HasColumnName("ability_to_negotiate_with_sales_partners_score");

                    b.Property<string>("AbilityToNegotiateWithSuppliersComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ability_to_negotiate_with_suppliers_comment");

                    b.Property<int?>("AbilityToNegotiateWithSuppliersScore")
                        .HasColumnType("int")
                        .HasColumnName("ability_to_negotiate_with_suppliers_score");

                    b.Property<string>("CompetitorComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("competitor_comment");

                    b.Property<int?>("CompetitorScore")
                        .HasColumnType("int")
                        .HasColumnName("competitor_score");

                    b.Property<string>("Ideal")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ideal");

                    b.Property<string>("Issue")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("issue");

                    b.Property<string>("NewComerComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("new_comer_comment");

                    b.Property<int?>("NewComerScore")
                        .HasColumnType("int")
                        .HasColumnName("new_comer_score");

                    b.Property<string>("OriginalId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("original_id");

                    b.Property<string>("SubstituteArticleComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("substitute_article_comment");

                    b.Property<int?>("SubstituteArticleScore")
                        .HasColumnType("int")
                        .HasColumnName("substitute_article_score");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.HasKey("Id")
                        .HasName("pk_five_force_framework_histories");

                    b.HasIndex("OriginalId");

                    b.ToTable("five_force_framework_histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FiveStepFrameWork", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<string>("CostReductionComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("cost_reduction_comment");

                    b.Property<int?>("CostReductionRating")
                        .HasColumnType("int")
                        .HasColumnName("cost_reduction_rating");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("HumanResourcesAndEvaluationAndDevelopmentComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("human_resources_and_evaluation_and_development_comment");

                    b.Property<int?>("HumanResourcesAndEvaluationAndDevelopmentRating")
                        .HasColumnType("int")
                        .HasColumnName("human_resources_and_evaluation_and_development_rating");

                    b.Property<string>("ICTAndBPRComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ict_and_bpr_comment");

                    b.Property<int?>("ICTAndBPRRating")
                        .HasColumnType("int")
                        .HasColumnName("ict_and_bpr_rating");

                    b.Property<string>("Ideal")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ideal");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Issue")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("issue");

                    b.Property<string>("ManagementComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("management_comment");

                    b.Property<int?>("ManagementRating")
                        .HasColumnType("int")
                        .HasColumnName("management_rating");

                    b.Property<string>("MarketingThinkingComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("marketing_thinking_comment");

                    b.Property<int?>("MarketingThinkingRating")
                        .HasColumnType("int")
                        .HasColumnName("marketing_thinking_rating");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_five_step_frame_works");

                    b.HasIndex("BusinessUnderstandingId")
                        .IsUnique()
                        .HasDatabaseName("ix_five_step_frame_works_business_understanding_id");

                    b.ToTable("five_step_frame_works");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FiveStepFrameWorkHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CostReductionComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("cost_reduction_comment");

                    b.Property<int?>("CostReductionRating")
                        .HasColumnType("int")
                        .HasColumnName("cost_reduction_rating");

                    b.Property<string>("HumanResourcesAndEvaluationAndDevelopmentComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("human_resources_and_evaluation_and_development_comment");

                    b.Property<int?>("HumanResourcesAndEvaluationAndDevelopmentRating")
                        .HasColumnType("int")
                        .HasColumnName("human_resources_and_evaluation_and_development_rating");

                    b.Property<string>("ICTAndBPRComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ict_and_bpr_comment");

                    b.Property<int?>("ICTAndBPRRating")
                        .HasColumnType("int")
                        .HasColumnName("ict_and_bpr_rating");

                    b.Property<string>("Ideal")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ideal");

                    b.Property<string>("Issue")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("issue");

                    b.Property<string>("ManagementComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("management_comment");

                    b.Property<int?>("ManagementRating")
                        .HasColumnType("int")
                        .HasColumnName("management_rating");

                    b.Property<string>("MarketingThinkingComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("marketing_thinking_comment");

                    b.Property<int?>("MarketingThinkingRating")
                        .HasColumnType("int")
                        .HasColumnName("marketing_thinking_rating");

                    b.Property<string>("OriginalId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("original_id");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.HasKey("Id")
                        .HasName("pk_five_step_frame_work_histories");

                    b.HasIndex("OriginalId");

                    b.ToTable("five_step_frame_work_histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssues", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<DateTimeOffset?>("CompletedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("completed_date_time");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("CurrentSituation")
                        .HasMaxLength(10000)
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("current_situation");

                    b.Property<DateTimeOffset>("ExpiredAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("expired_at");

                    b.Property<string>("Ideal")
                        .HasMaxLength(10000)
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ideal");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Issue")
                        .HasMaxLength(10000)
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("issue");

                    b.Property<int?>("Order")
                        .HasMaxLength(1)
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("RegistrantName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_name");

                    b.Property<string>("StaffId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<int?>("Status")
                        .HasMaxLength(1)
                        .HasColumnType("int")
                        .HasColumnName("status");

                    b.Property<string>("Title")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("title");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_hypothetical_discussion_of_issues");

                    b.HasIndex("BusinessUnderstandingId")
                        .HasDatabaseName("ix_hypothetical_discussion_of_issues_business_understanding_id");

                    b.ToTable("hypothetical_discussion_of_issues");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesComment", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<bool>("ContainCustomerReaction")
                        .HasColumnType("bit")
                        .HasColumnName("contain_customer_reaction");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool?>("IsPersonOfPower")
                        .HasColumnType("bit")
                        .HasColumnName("is_person_of_power");

                    b.Property<string>("MentionTargetTeamIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_team_ids");

                    b.Property<string>("MentionTargetUserIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_user_ids");

                    b.Property<string>("Person")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("person");

                    b.Property<string>("Purpose")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("Internal")
                        .HasColumnName("purpose");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("Registrant")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_hypothetical_discussion_of_issues_comments");

                    b.HasIndex("ThreadId");

                    b.ToTable("hypothetical_discussion_of_issues_comments");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesCommentFile", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CommentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("comment_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)")
                        .HasColumnName("file_name");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_hypothetical_discussion_of_issues_comment_files");

                    b.HasIndex("CommentId");

                    b.ToTable("hypothetical_discussion_of_issues_comment_files");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesCommentReaction", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CommentId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("comment_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<int>("ReactionType")
                        .HasColumnType("int")
                        .HasColumnName("reaction_type");

                    b.Property<string>("StaffId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_hypothetical_discussion_of_issues_comment_reactions");

                    b.HasIndex("CommentId");

                    b.ToTable("hypothetical_discussion_of_issues_comment_reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesThread", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<bool>("ContainCustomerReaction")
                        .HasColumnType("bit")
                        .HasColumnName("contain_customer_reaction");

                    b.Property<DateTimeOffset?>("CorrespondenceDate")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("correspondence_date");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<string>("HypotheticalDiscussionOfIssuesId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("hypothetical_discussion_of_issues_id");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool?>("IsPersonOfPower")
                        .HasColumnType("bit")
                        .HasColumnName("is_person_of_power");

                    b.Property<string>("MentionTargetTeamIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_team_ids");

                    b.Property<string>("MentionTargetUserIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_user_ids");

                    b.Property<string>("MentionTargetsHtml")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_targets_html");

                    b.Property<string>("Person")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("person");

                    b.Property<string>("Purpose")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("Internal")
                        .HasColumnName("purpose");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("Registrant")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("title");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_hypothetical_discussion_of_issues_threads");

                    b.HasIndex("HypotheticalDiscussionOfIssuesId")
                        .HasDatabaseName("ix_hypothetical_discussion_of_issues_threads_hypothetical_discussion_of_issues_id");

                    b.ToTable("hypothetical_discussion_of_issues_threads");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesThreadFile", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)")
                        .HasColumnName("file_name");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_hypothetical_discussion_of_issues_thread_files");

                    b.HasIndex("ThreadId");

                    b.ToTable("hypothetical_discussion_of_issues_thread_files");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesThreadReaction", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<int>("ReactionType")
                        .HasColumnType("int")
                        .HasColumnName("reaction_type");

                    b.Property<string>("StaffId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_hypothetical_discussion_of_issues_thread_reactions");

                    b.HasIndex("ThreadId");

                    b.ToTable("hypothetical_discussion_of_issues_thread_reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.IssueTypeMaster", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("name");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_issue_type_masters");

                    b.ToTable("issue_type_masters");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.Management", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<string>("CentripetalForceComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("centripetal_force_comment");

                    b.Property<int?>("CentripetalForceRating")
                        .HasColumnType("int")
                        .HasColumnName("centripetal_force_rating");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("ExperienceComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("experience_comment");

                    b.Property<int?>("ExperienceRating")
                        .HasColumnType("int")
                        .HasColumnName("experience_rating");

                    b.Property<string>("ExpertiseComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("expertise_comment");

                    b.Property<int?>("ExpertiseRating")
                        .HasColumnType("int")
                        .HasColumnName("expertise_rating");

                    b.Property<string>("Ideal")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ideal");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Issue")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("issue");

                    b.Property<string>("MediumToLongTermVisionComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("medium_to_long_term_vision_comment");

                    b.Property<int?>("MediumToLongTermVisionRating")
                        .HasColumnType("int")
                        .HasColumnName("medium_to_long_term_vision_rating");

                    b.Property<string>("SuccessorComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("successor_comment");

                    b.Property<int?>("SuccessorRating")
                        .HasColumnType("int")
                        .HasColumnName("successor_rating");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_managements");

                    b.HasIndex("BusinessUnderstandingId")
                        .IsUnique()
                        .HasDatabaseName("ix_managements_business_understanding_id");

                    b.ToTable("managements");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ManagementHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CentripetalForceComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("centripetal_force_comment");

                    b.Property<int?>("CentripetalForceRating")
                        .HasColumnType("int")
                        .HasColumnName("centripetal_force_rating");

                    b.Property<string>("ExperienceComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("experience_comment");

                    b.Property<int?>("ExperienceRating")
                        .HasColumnType("int")
                        .HasColumnName("experience_rating");

                    b.Property<string>("ExpertiseComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("expertise_comment");

                    b.Property<int?>("ExpertiseRating")
                        .HasColumnType("int")
                        .HasColumnName("expertise_rating");

                    b.Property<string>("Ideal")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ideal");

                    b.Property<string>("Issue")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("issue");

                    b.Property<string>("MediumToLongTermVisionComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("medium_to_long_term_vision_comment");

                    b.Property<int?>("MediumToLongTermVisionRating")
                        .HasColumnType("int")
                        .HasColumnName("medium_to_long_term_vision_rating");

                    b.Property<string>("OriginalId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("original_id");

                    b.Property<string>("SuccessorComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("successor_comment");

                    b.Property<int?>("SuccessorRating")
                        .HasColumnType("int")
                        .HasColumnName("successor_rating");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.HasKey("Id")
                        .HasName("pk_management_histories");

                    b.HasIndex("OriginalId");

                    b.ToTable("management_histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ManagementPlan", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<int?>("BusinessPlanRating")
                        .HasColumnType("int")
                        .HasColumnName("business_plan_rating");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("Ideal")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ideal");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Issue")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("issue");

                    b.Property<string>("ManagementPlanCurrentComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("management_plan_current_comment");

                    b.Property<string>("ManagementPlanOverview")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("management_plan_overview");

                    b.Property<DateTimeOffset?>("PlanningDate")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("planning_date");

                    b.Property<string>("StatusOfAchievementOfManagementPlan")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("status_of_achievement_of_management_plan");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_management_plans");

                    b.HasIndex("BusinessUnderstandingId")
                        .IsUnique()
                        .HasDatabaseName("ix_management_plans_business_understanding_id");

                    b.ToTable("management_plans");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ManagementPlanHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<int?>("BusinessPlanRating")
                        .HasColumnType("int")
                        .HasColumnName("business_plan_rating");

                    b.Property<string>("Ideal")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ideal");

                    b.Property<string>("Issue")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("issue");

                    b.Property<string>("ManagementPlanCurrentComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("management_plan_current_comment");

                    b.Property<string>("ManagementPlanOverview")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("management_plan_overview");

                    b.Property<string>("OriginalId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("original_id");

                    b.Property<DateTimeOffset?>("PlanningDate")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("planning_date");

                    b.Property<string>("StatusOfAchievementOfManagementPlan")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("status_of_achievement_of_management_plan");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.HasKey("Id")
                        .HasName("pk_management_plan_histories");

                    b.HasIndex("OriginalId");

                    b.ToTable("management_plan_histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstanding", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<DateTimeOffset?>("CompletedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("completed_date_time");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .HasMaxLength(10000)
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<DateTimeOffset>("ExpiredAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("expired_at");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Note")
                        .HasMaxLength(10000)
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("note");

                    b.Property<int?>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("RegistrantName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_name");

                    b.Property<string>("StaffId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<int?>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status");

                    b.Property<string>("TargetPerson")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("target_person");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("title");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_our_policy_understandings");

                    b.HasIndex("BusinessUnderstandingId")
                        .HasDatabaseName("ix_our_policy_understandings_business_understanding_id");

                    b.ToTable("our_policy_understandings");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingComment", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<bool>("ContainCustomerReaction")
                        .HasColumnType("bit")
                        .HasColumnName("contain_customer_reaction");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool?>("IsPersonOfPower")
                        .HasColumnType("bit")
                        .HasColumnName("is_person_of_power");

                    b.Property<string>("MentionTargetTeamIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_team_ids");

                    b.Property<string>("MentionTargetUserIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_user_ids");

                    b.Property<string>("Person")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("person");

                    b.Property<string>("Purpose")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("Internal")
                        .HasColumnName("purpose");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("Registrant")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_our_policy_understanding_comments");

                    b.HasIndex("ThreadId");

                    b.ToTable("our_policy_understanding_comments");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingCommentFile", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CommentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("comment_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)")
                        .HasColumnName("file_name");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_our_policy_understanding_comment_files");

                    b.HasIndex("CommentId");

                    b.ToTable("our_policy_understanding_comment_files");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingCommentReaction", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CommentId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("comment_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<int>("ReactionType")
                        .HasColumnType("int")
                        .HasColumnName("reaction_type");

                    b.Property<string>("StaffId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_our_policy_understanding_comment_reactions");

                    b.HasIndex("CommentId");

                    b.ToTable("our_policy_understanding_comment_reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingThread", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<bool>("ContainCustomerReaction")
                        .HasColumnType("bit")
                        .HasColumnName("contain_customer_reaction");

                    b.Property<DateTimeOffset?>("CorrespondenceDate")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("correspondence_date");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool?>("IsPersonOfPower")
                        .HasColumnType("bit")
                        .HasColumnName("is_person_of_power");

                    b.Property<string>("MentionTargetTeamIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_team_ids");

                    b.Property<string>("MentionTargetUserIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_user_ids");

                    b.Property<string>("MentionTargetsHtml")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_targets_html");

                    b.Property<string>("OurPolicyUnderstandingId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("our_policy_understanding_id");

                    b.Property<string>("Person")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("person");

                    b.Property<string>("Purpose")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("Internal")
                        .HasColumnName("purpose");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("Registrant")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("title");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_our_policy_understanding_threads");

                    b.HasIndex("OurPolicyUnderstandingId")
                        .HasDatabaseName("ix_our_policy_understanding_threads_our_policy_understanding_id");

                    b.ToTable("our_policy_understanding_threads");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingThreadFile", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)")
                        .HasColumnName("file_name");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_our_policy_understanding_thread_files");

                    b.HasIndex("ThreadId");

                    b.ToTable("our_policy_understanding_thread_files");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingThreadReaction", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<int>("ReactionType")
                        .HasColumnType("int")
                        .HasColumnName("reaction_type");

                    b.Property<string>("StaffId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_our_policy_understanding_thread_reactions");

                    b.HasIndex("ThreadId");

                    b.ToTable("our_policy_understanding_thread_reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.RelationLevel", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<bool?>("AlreadyIdentifiedRealAuthority")
                        .HasColumnType("bit")
                        .HasColumnName("already_identified_real_authority");

                    b.Property<bool?>("AlumniDispatch")
                        .HasColumnType("bit")
                        .HasColumnName("alumni_dispatch");

                    b.Property<double?>("AuthorityLevel")
                        .HasColumnType("float")
                        .HasColumnName("authority_level");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<int?>("CollateralApproach")
                        .HasColumnType("int")
                        .HasColumnName("collateral_approach");

                    b.Property<int?>("ConceptOfBanksNumber")
                        .HasColumnType("int")
                        .HasColumnName("concept_of_banks_number");

                    b.Property<int?>("ConceptOfBusinessGrowth")
                        .HasColumnType("int")
                        .HasColumnName("concept_of_business_growth");

                    b.Property<int?>("ConceptOfConsulting")
                        .HasColumnType("int")
                        .HasColumnName("concept_of_consulting");

                    b.Property<int?>("ConceptOfESGAndSDGs")
                        .HasColumnType("int")
                        .HasColumnName("concept_of_esg_and_sd_gs");

                    b.Property<bool?>("ConceptOfLeaseUseMainUse")
                        .HasColumnType("bit")
                        .HasColumnName("concept_of_lease_use_main_use");

                    b.Property<bool?>("ConceptOfLeaseUseNoUse")
                        .HasColumnType("bit")
                        .HasColumnName("concept_of_lease_use_no_use");

                    b.Property<bool?>("ConceptOfLeaseUseOtherUse")
                        .HasColumnType("bit")
                        .HasColumnName("concept_of_lease_use_other_use");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("DisclosureComments")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("disclosure_comments");

                    b.Property<double?>("DisclosureLevel")
                        .HasColumnType("float")
                        .HasColumnName("disclosure_level");

                    b.Property<int?>("FinancialsAlreadyShared")
                        .HasColumnType("int")
                        .HasColumnName("financials_already_shared");

                    b.Property<int?>("FundraisingConcept")
                        .HasColumnType("int")
                        .HasColumnName("fundraising_concept");

                    b.Property<bool?>("HasAppraisalDetails")
                        .HasColumnType("bit")
                        .HasColumnName("has_appraisal_details");

                    b.Property<bool?>("HasFinancialStatements")
                        .HasColumnType("bit")
                        .HasColumnName("has_financial_statements");

                    b.Property<bool?>("HasTrialBalanceAndCashManagement")
                        .HasColumnType("bit")
                        .HasColumnName("has_trial_balance_and_cash_management");

                    b.Property<string>("InterviewComments")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("interview_comments");

                    b.Property<DateTimeOffset?>("InterviewDate")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("interview_date");

                    b.Property<string>("InterviewDetail")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("interview_detail");

                    b.Property<string>("Interviewee")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("interviewee");

                    b.Property<string>("Interviewer")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("interviewer");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<int?>("ManagementInformation")
                        .HasColumnType("int")
                        .HasColumnName("management_information");

                    b.Property<int?>("OwnerDisclosure")
                        .HasColumnType("int")
                        .HasColumnName("owner_disclosure");

                    b.Property<int?>("Policy")
                        .HasColumnType("int")
                        .HasColumnName("policy");

                    b.Property<int?>("QualityOfInterviews")
                        .HasColumnType("int")
                        .HasColumnName("quality_of_interviews");

                    b.Property<string>("RelationLevelComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("relation_level_comment");

                    b.Property<double?>("RelationalLevel")
                        .HasColumnType("float")
                        .HasColumnName("relational_level");

                    b.Property<string>("RelationshipComments")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("relationship_comments");

                    b.Property<int?>("UnderstandingOfImprovement")
                        .HasColumnType("int")
                        .HasColumnName("understanding_of_improvement");

                    b.Property<int?>("UnderstandingOfOurPolicy")
                        .HasColumnType("int")
                        .HasColumnName("understanding_of_our_policy");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.Property<bool?>("VisitorsInTheLastOneYearAdministrator")
                        .HasColumnType("bit")
                        .HasColumnName("visitors_in_the_last_one_year_administrator");

                    b.Property<bool?>("VisitorsInTheLastOneYearOfficer")
                        .HasColumnType("bit")
                        .HasColumnName("visitors_in_the_last_one_year_officer");

                    b.Property<bool?>("VisitorsInTheLastOneYearStaff")
                        .HasColumnType("bit")
                        .HasColumnName("visitors_in_the_last_one_year_staff");

                    b.HasKey("Id")
                        .HasName("pk_relation_levels");

                    b.HasIndex("BusinessUnderstandingId")
                        .IsUnique()
                        .HasDatabaseName("ix_relation_levels_business_understanding_id");

                    b.ToTable("relation_levels");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.RelationLevelHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<bool?>("AlreadyIdentifiedRealAuthority")
                        .HasColumnType("bit")
                        .HasColumnName("already_identified_real_authority");

                    b.Property<bool?>("AlumniDispatch")
                        .HasColumnType("bit")
                        .HasColumnName("alumni_dispatch");

                    b.Property<double?>("AuthorityLevel")
                        .HasColumnType("float")
                        .HasColumnName("authority_level");

                    b.Property<int?>("CollateralApproach")
                        .HasColumnType("int")
                        .HasColumnName("collateral_approach");

                    b.Property<int?>("ConceptOfBanksNumber")
                        .HasColumnType("int")
                        .HasColumnName("concept_of_banks_number");

                    b.Property<int?>("ConceptOfBusinessGrowth")
                        .HasColumnType("int")
                        .HasColumnName("concept_of_business_growth");

                    b.Property<int?>("ConceptOfConsulting")
                        .HasColumnType("int")
                        .HasColumnName("concept_of_consulting");

                    b.Property<int?>("ConceptOfESGAndSDGs")
                        .HasColumnType("int")
                        .HasColumnName("concept_of_esg_and_sd_gs");

                    b.Property<bool?>("ConceptOfLeaseUseMainUse")
                        .HasColumnType("bit")
                        .HasColumnName("concept_of_lease_use_main_use");

                    b.Property<bool?>("ConceptOfLeaseUseNoUse")
                        .HasColumnType("bit")
                        .HasColumnName("concept_of_lease_use_no_use");

                    b.Property<bool?>("ConceptOfLeaseUseOtherUse")
                        .HasColumnType("bit")
                        .HasColumnName("concept_of_lease_use_other_use");

                    b.Property<string>("DisclosureComments")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("disclosure_comments");

                    b.Property<double?>("DisclosureLevel")
                        .HasColumnType("float")
                        .HasColumnName("disclosure_level");

                    b.Property<int?>("FinancialsAlreadyShared")
                        .HasColumnType("int")
                        .HasColumnName("financials_already_shared");

                    b.Property<int?>("FundraisingConcept")
                        .HasColumnType("int")
                        .HasColumnName("fundraising_concept");

                    b.Property<bool?>("HasAppraisalDetails")
                        .HasColumnType("bit")
                        .HasColumnName("has_appraisal_details");

                    b.Property<bool?>("HasFinancialStatements")
                        .HasColumnType("bit")
                        .HasColumnName("has_financial_statements");

                    b.Property<bool?>("HasTrialBalanceAndCashManagement")
                        .HasColumnType("bit")
                        .HasColumnName("has_trial_balance_and_cash_management");

                    b.Property<string>("InterviewComments")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("interview_comments");

                    b.Property<DateTimeOffset?>("InterviewDate")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("interview_date");

                    b.Property<string>("InterviewDetail")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("interview_detail");

                    b.Property<string>("Interviewee")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("interviewee");

                    b.Property<string>("Interviewer")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("interviewer");

                    b.Property<int?>("ManagementInformation")
                        .HasColumnType("int")
                        .HasColumnName("management_information");

                    b.Property<string>("OriginalId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("original_id");

                    b.Property<int?>("OwnerDisclosure")
                        .HasColumnType("int")
                        .HasColumnName("owner_disclosure");

                    b.Property<int?>("Policy")
                        .HasColumnType("int")
                        .HasColumnName("policy");

                    b.Property<int?>("QualityOfInterviews")
                        .HasColumnType("int")
                        .HasColumnName("quality_of_interviews");

                    b.Property<string>("RelationLevelComment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("relation_level_comment");

                    b.Property<string>("RelationLevelId")
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("relation_level_id");

                    b.Property<double?>("RelationalLevel")
                        .HasColumnType("float")
                        .HasColumnName("relational_level");

                    b.Property<string>("RelationshipComments")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("relationship_comments");

                    b.Property<int?>("UnderstandingOfImprovement")
                        .HasColumnType("int")
                        .HasColumnName("understanding_of_improvement");

                    b.Property<int?>("UnderstandingOfOurPolicy")
                        .HasColumnType("int")
                        .HasColumnName("understanding_of_our_policy");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<bool?>("VisitorsInTheLastOneYearAdministrator")
                        .HasColumnType("bit")
                        .HasColumnName("visitors_in_the_last_one_year_administrator");

                    b.Property<bool?>("VisitorsInTheLastOneYearOfficer")
                        .HasColumnType("bit")
                        .HasColumnName("visitors_in_the_last_one_year_officer");

                    b.Property<bool?>("VisitorsInTheLastOneYearStaff")
                        .HasColumnType("bit")
                        .HasColumnName("visitors_in_the_last_one_year_staff");

                    b.HasKey("Id")
                        .HasName("pk_relation_level_histories");

                    b.HasIndex("RelationLevelId")
                        .HasDatabaseName("ix_relation_level_histories_relation_level_id");

                    b.ToTable("relation_level_histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SWOTAnalysis", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Opportunities")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("opportunities");

                    b.Property<string>("Strengths")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("strengths");

                    b.Property<string>("Threats")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("threats");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.Property<string>("Weaknesses")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("weaknesses");

                    b.HasKey("Id")
                        .HasName("pk_swot_analyses");

                    b.HasIndex("BusinessUnderstandingId")
                        .IsUnique()
                        .HasDatabaseName("ix_swot_analyses_business_understanding_id");

                    b.ToTable("swot_analyses");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SWOTAnalysisHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("Opportunities")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("opportunities");

                    b.Property<string>("OriginalId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("original_id");

                    b.Property<string>("Strengths")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("strengths");

                    b.Property<string>("Threats")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("threats");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Weaknesses")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("weaknesses");

                    b.HasKey("Id")
                        .HasName("pk_swot_analyses_histories");

                    b.HasIndex("OriginalId");

                    b.ToTable("swot_analyses_histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinance", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<DateTimeOffset?>("CompletedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("completed_date_time");

                    b.Property<string>("Content")
                        .HasMaxLength(10000)
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("content");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("ExpiredAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("expired_at");

                    b.Property<int?>("FinancialsAlreadyShared")
                        .HasColumnType("int")
                        .HasColumnName("financials_already_shared");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("Note")
                        .HasMaxLength(10000)
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("note");

                    b.Property<int?>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("RegistrantName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_name");

                    b.Property<string>("StaffId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<int?>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status");

                    b.Property<string>("TargetPerson")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("target_person");

                    b.Property<string>("Title")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("title");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_sharing_of_finances");

                    b.HasIndex("BusinessUnderstandingId")
                        .HasDatabaseName("ix_sharing_of_finances_business_understanding_id");

                    b.ToTable("sharing_of_finances");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceComment", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<bool>("ContainCustomerReaction")
                        .HasColumnType("bit")
                        .HasColumnName("contain_customer_reaction");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool?>("IsPersonOfPower")
                        .HasColumnType("bit")
                        .HasColumnName("is_person_of_power");

                    b.Property<string>("MentionTargetTeamIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_team_ids");

                    b.Property<string>("MentionTargetUserIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_user_ids");

                    b.Property<string>("Person")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("person");

                    b.Property<string>("Purpose")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("Internal")
                        .HasColumnName("purpose");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("Registrant")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_sharing_of_finance_comments");

                    b.HasIndex("ThreadId");

                    b.ToTable("sharing_of_finance_comments");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceCommentFile", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CommentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("comment_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)")
                        .HasColumnName("file_name");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_sharing_of_finance_comment_files");

                    b.HasIndex("CommentId");

                    b.ToTable("sharing_of_finance_comment_files");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceCommentReaction", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CommentId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("comment_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<int>("ReactionType")
                        .HasColumnType("int")
                        .HasColumnName("reaction_type");

                    b.Property<string>("StaffId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_sharing_of_finance_comment_reactions");

                    b.HasIndex("CommentId");

                    b.ToTable("sharing_of_finance_comment_reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceThread", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<bool>("ContainCustomerReaction")
                        .HasColumnType("bit")
                        .HasColumnName("contain_customer_reaction");

                    b.Property<DateTimeOffset?>("CorrespondenceDate")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("correspondence_date");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool?>("IsPersonOfPower")
                        .HasColumnType("bit")
                        .HasColumnName("is_person_of_power");

                    b.Property<string>("MentionTargetTeamIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_team_ids");

                    b.Property<string>("MentionTargetUserIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_user_ids");

                    b.Property<string>("MentionTargetsHtml")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_targets_html");

                    b.Property<string>("Person")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("person");

                    b.Property<string>("Purpose")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("Internal")
                        .HasColumnName("purpose");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("Registrant")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("SharingOfFinanceId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("sharing_of_finance_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("title");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_sharing_of_finance_threads");

                    b.HasIndex("SharingOfFinanceId")
                        .HasDatabaseName("ix_sharing_of_finance_threads_sharing_of_finance_id");

                    b.ToTable("sharing_of_finance_threads");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceThreadFile", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)")
                        .HasColumnName("file_name");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_sharing_of_finance_thread_files");

                    b.HasIndex("ThreadId");

                    b.ToTable("sharing_of_finance_thread_files");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceThreadReaction", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<int>("ReactionType")
                        .HasColumnType("int")
                        .HasColumnName("reaction_type");

                    b.Property<string>("StaffId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_sharing_of_finance_thread_reactions");

                    b.HasIndex("ThreadId");

                    b.ToTable("sharing_of_finance_thread_reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ThreeCAnalysis", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<string>("Company")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("company");

                    b.Property<string>("Competitor")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("competitor");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("Customer")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("customer");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_three_c_analyses");

                    b.HasIndex("BusinessUnderstandingId")
                        .IsUnique()
                        .HasDatabaseName("ix_three_c_analyses_business_understanding_id");

                    b.ToTable("three_c_analyses");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ThreeCAnalysisHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("Company")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("company");

                    b.Property<string>("Competitor")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("competitor");

                    b.Property<string>("Customer")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("customer");

                    b.Property<string>("OriginalId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("original_id");

                    b.Property<string>("ThreeCAnalysisId")
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("three_c_analysis_id");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.HasKey("Id")
                        .HasName("pk_three_c_analysis_histories");

                    b.HasIndex("ThreeCAnalysisId")
                        .HasDatabaseName("ix_three_c_analysis_histories_three_c_analysis_id");

                    b.ToTable("three_c_analysis_histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDo", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("BusinessUnderstandingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("business_understanding_id");

                    b.Property<DateTimeOffset?>("CompletedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("completed_date_time");

                    b.Property<string>("Content")
                        .HasMaxLength(10000)
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("content");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("ExpiredAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("expired_at");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<int?>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("RegistrantName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_name");

                    b.Property<string>("StaffId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<int?>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status");

                    b.Property<string>("Title")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("title");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset?>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_to_dos");

                    b.HasIndex("BusinessUnderstandingId")
                        .HasDatabaseName("ix_to_dos_business_understanding_id");

                    b.ToTable("to_dos");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDoComment", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<bool>("ContainCustomerReaction")
                        .HasColumnType("bit")
                        .HasColumnName("contain_customer_reaction");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool?>("IsPersonOfPower")
                        .HasColumnType("bit")
                        .HasColumnName("is_person_of_power");

                    b.Property<string>("MentionTargetTeamIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_team_ids");

                    b.Property<string>("MentionTargetUserIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_user_ids");

                    b.Property<string>("Person")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("person");

                    b.Property<string>("Purpose")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("Internal")
                        .HasColumnName("purpose");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("Registrant")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_to_do_comments");

                    b.HasIndex("ThreadId");

                    b.ToTable("to_do_comments");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDoCommentFile", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CommentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("comment_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)")
                        .HasColumnName("file_name");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_to_do_comment_files");

                    b.HasIndex("CommentId");

                    b.ToTable("to_do_comment_files");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDoCommentReaction", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<string>("CommentId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("comment_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<int>("ReactionType")
                        .HasColumnType("int")
                        .HasColumnName("reaction_type");

                    b.Property<string>("StaffId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_to_do_comment_reactions");

                    b.HasIndex("CommentId");

                    b.ToTable("to_do_comment_reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDoThread", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<bool>("ContainCustomerReaction")
                        .HasColumnType("bit")
                        .HasColumnName("contain_customer_reaction");

                    b.Property<DateTimeOffset?>("CorrespondenceDate")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("correspondence_date");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<bool?>("IsPersonOfPower")
                        .HasColumnType("bit")
                        .HasColumnName("is_person_of_power");

                    b.Property<string>("MentionTargetTeamIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_team_ids");

                    b.Property<string>("MentionTargetUserIds")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_target_user_ids");

                    b.Property<string>("MentionTargetsHtml")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mention_targets_html");

                    b.Property<string>("Person")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("person");

                    b.Property<string>("Purpose")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("Internal")
                        .HasColumnName("purpose");

                    b.Property<DateTimeOffset>("RegisteredDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("registered_date_time");

                    b.Property<string>("Registrant")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant");

                    b.Property<string>("RegistrantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("registrant_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("title");

                    b.Property<string>("ToDoId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("to_do_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_to_do_threads");

                    b.HasIndex("ToDoId")
                        .HasDatabaseName("ix_to_do_threads_to_do_id");

                    b.ToTable("to_do_threads");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDoThreadFile", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)")
                        .HasColumnName("file_name");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("UpdaterId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_id");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("updater_name");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_to_do_thread_files");

                    b.HasIndex("ThreadId");

                    b.ToTable("to_do_thread_files");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDoThreadReaction", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted");

                    b.Property<int>("ReactionType")
                        .HasColumnType("int")
                        .HasColumnName("reaction_type");

                    b.Property<string>("StaffId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_id");

                    b.Property<string>("StaffName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("staff_name");

                    b.Property<string>("ThreadId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("nvarchar(26)")
                        .HasColumnName("thread_id");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_at");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_by");

                    b.Property<DateTimeOffset>("UpdatedDateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("updated_date_time");

                    b.Property<string>("UpdatedProgram")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("updated_program");

                    b.Property<string>("Version")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasDefaultValue("0")
                        .HasColumnName("version");

                    b.HasKey("Id")
                        .HasName("pk_to_do_thread_reactions");

                    b.HasIndex("ThreadId");

                    b.ToTable("to_do_thread_reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessPartner", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithMany("BusinessPartners")
                        .HasForeignKey("BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_business_partners_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessPartnerIndustry", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessPartner", null)
                        .WithOne("BusinessPartnerIndustry")
                        .HasForeignKey("BusinessCustomerUnderstandingService.Domain.Entities.BusinessPartnerIndustry", "BusinessPartnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_business_partner_industries_business_partners_business_partner_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessPartnershipAndCreativeAccountingDetail", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithOne("BusinessPartnershipAndCreativeAccountingDetail")
                        .HasForeignKey("BusinessCustomerUnderstandingService.Domain.Entities.BusinessPartnershipAndCreativeAccountingDetail", "BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_business_partnership_and_creative_accounting_details_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessCustomer", "BusinessCustomer")
                        .WithOne("BusinessUnderstanding")
                        .HasForeignKey("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", "BusinessCustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_business_understandings_business_customers_business_customer_id");

                    b.Navigation("BusinessCustomer");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingDiscussion", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingThread", null)
                        .WithMany("Discussions")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_business_understanding_comment_business_understanding_thread_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingDiscussionFile", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingDiscussion", null)
                        .WithMany("Files")
                        .HasForeignKey("DiscussionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_business_understanding_discussion_business_understanding_discussion_file_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingDiscussionReaction", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingDiscussion", null)
                        .WithMany("Reactions")
                        .HasForeignKey("CommentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_business_understanding_discussion_reactions_business_understanding_discussions_business_understanding_discussion_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingFile", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithMany("BusinessUnderstandingFile")
                        .HasForeignKey("BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_business_understanding_files_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingHistory", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithMany("Histories")
                        .HasForeignKey("OriginalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_business_understanding_histories_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingMaterializedView", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithOne("BusinessUnderstandingMaterializedView")
                        .HasForeignKey("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingMaterializedView", "BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_business_understanding_materialized_views_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingThread", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithMany("Threads")
                        .HasForeignKey("BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_business_understanding_threads_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingThreadFile", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingThread", null)
                        .WithMany("Files")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_business_understanding_thread_business_understanding_thread_file_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingThreadReaction", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingThread", null)
                        .WithMany("Reactions")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_business_understanding_thread_reactions_business_understanding_threads_business_understanding_thread_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistribution", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithOne("CommercialDistribution")
                        .HasForeignKey("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistribution", "BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_commercial_distributions_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionEdge", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistribution", null)
                        .WithMany("Edges")
                        .HasForeignKey("CommercialDistributionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_commercial_distribution_edges_commercial_distributions_commercial_distribution_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionEdgeHistory", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionHistory", null)
                        .WithMany("CommercialDistributionEdgeHistories")
                        .HasForeignKey("CommercialDistributionHistoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_commercial_distribution_edge_histories_commercial_distribution_histories_commercial_distribution_history_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionHistory", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistribution", null)
                        .WithMany("Histories")
                        .HasForeignKey("OriginalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_commercial_distribution_histories_commercial_distributions_commercial_distribution_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionNode", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistribution", null)
                        .WithMany("Nodes")
                        .HasForeignKey("CommercialDistributionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_commercial_distribution_nodes_commercial_distributions_commercial_distribution_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionNodeHistory", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionHistory", null)
                        .WithMany("CommercialDistributionNodeHistories")
                        .HasForeignKey("CommercialDistributionHistoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_commercial_distribution_node_histories_commercial_distribution_histories_commercial_distribution_history_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionTemplateEdge", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionTemplate", null)
                        .WithMany("Edges")
                        .HasForeignKey("TemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_commercial_distribution_template_edges_commercial_distribution_templates_commercial_distribution_template_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionTemplateNode", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionTemplate", null)
                        .WithMany("Nodes")
                        .HasForeignKey("TemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_commercial_distribution_template_nodes_commercial_distribution_templates_commercial_distribution_template_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstanding", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithMany("CustomerIdeasUnderstanding")
                        .HasForeignKey("BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_customer_ideas_understandings_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingComment", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingThread", null)
                        .WithMany("Comments")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_customer_ideas_understanding_comment_customer_ideas_understanding_thread_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingCommentFile", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingComment", null)
                        .WithMany("Files")
                        .HasForeignKey("CommentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_customer_ideas_understanding_comment_customer_ideas_understanding_comment_file_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingCommentReaction", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingComment", null)
                        .WithMany("Reactions")
                        .HasForeignKey("CommentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_customer_ideas_understanding_comment_reactions_customer_ideas_understanding_comments_customer_ideas_understanding_comment_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingThread", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstanding", null)
                        .WithMany("Threads")
                        .HasForeignKey("CustomerIdeasUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_customer_ideas_understanding_threads_customer_ideas_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingThreadFile", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingThread", null)
                        .WithMany("Files")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_customer_ideas_understanding_thread_customer_ideas_understanding_thread_file_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingThreadReaction", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingThread", null)
                        .WithMany("Reactions")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_customer_ideas_understanding_thread_reactions_customer_ideas_understanding_threads_customer_ideas_understanding_thread_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerLink", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessCustomer", null)
                        .WithMany("Links")
                        .HasForeignKey("BusinessCustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_customer_links_business_customers_business_customer_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerReaction", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssues", "HypotheticalDiscussionOfIssues")
                        .WithOne("CustomerReaction")
                        .HasForeignKey("BusinessCustomerUnderstandingService.Domain.Entities.CustomerReaction", "HypotheticalDiscussionOfIssuesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_customer_reactions_hypothetical_discussion_of_issues_hypothetical_discussion_of_issues_id");

                    b.Navigation("HypotheticalDiscussionOfIssues");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ESGAndSDGs", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithOne("ESGAndSDGs")
                        .HasForeignKey("BusinessCustomerUnderstandingService.Domain.Entities.ESGAndSDGs", "BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_esg_and_sd_gs_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ESGAndSDGsHistory", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.ESGAndSDGs", null)
                        .WithMany("Histories")
                        .HasForeignKey("OriginalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_esg_and_sd_gs_histories_esg_and_sd_gs_esg_and_sd_gs_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ExternalEnvironment", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithOne("ExternalEnvironment")
                        .HasForeignKey("BusinessCustomerUnderstandingService.Domain.Entities.ExternalEnvironment", "BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_external_environments_business_understandings_business_understanding_id");

                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.ExternalEnvironmentMaster", "ExternalEnvironmentMaster")
                        .WithMany()
                        .HasForeignKey("ExternalEnvironmentMasterId")
                        .HasConstraintName("fk_external_environments_external_environment_master_external_environment_master_id");

                    b.Navigation("ExternalEnvironmentMaster");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ExternalEnvironmentHistory", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.ExternalEnvironmentMaster", "ExternalEnvironmentMaster")
                        .WithMany()
                        .HasForeignKey("ExternalEnvironmentMasterId")
                        .HasConstraintName("fk_external_environment_histories_external_environment_master_external_environment_master_id");

                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.ExternalEnvironment", null)
                        .WithMany("Histories")
                        .HasForeignKey("OriginalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_external_environment_histories_external_environments_external_environment_id");

                    b.Navigation("ExternalEnvironmentMaster");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTree", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithOne("FamilyTree")
                        .HasForeignKey("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTree", "BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_family_trees_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTreeEdge", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTree", null)
                        .WithMany("FamilyTreeEdges")
                        .HasForeignKey("FamilyTreeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_family_tree_edges_family_trees_family_tree_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTreeEdgeHistory", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTreeHistory", null)
                        .WithMany("FamilyTreeEdgeHistories")
                        .HasForeignKey("FamilyTreeHistoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_family_tree_edge_histories_family_tree_histories_family_tree_history_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTreeFile", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithMany("FamilyTreeFile")
                        .HasForeignKey("BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_family_tree_files_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTreeHistory", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTree", null)
                        .WithMany("Histories")
                        .HasForeignKey("OriginalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_family_tree_histories_family_trees_family_tree_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTreeNode", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTree", null)
                        .WithMany("FamilyTreeNodes")
                        .HasForeignKey("FamilyTreeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_family_tree_nodes_family_trees_family_tree_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTreeNodeHistory", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTreeHistory", null)
                        .WithMany("FamilyTreeNodeHistories")
                        .HasForeignKey("FamilyTreeHistoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_family_tree_node_histories_family_tree_histories_family_tree_history_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FiveForceFramework", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithOne("FiveForceFramework")
                        .HasForeignKey("BusinessCustomerUnderstandingService.Domain.Entities.FiveForceFramework", "BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_five_force_frameworks_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FiveForceFrameworkHistory", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.FiveForceFramework", null)
                        .WithMany("Histories")
                        .HasForeignKey("OriginalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_five_force_framework_histories_five_force_frameworks_five_force_framework_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FiveStepFrameWork", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithOne("FiveStepFrameWork")
                        .HasForeignKey("BusinessCustomerUnderstandingService.Domain.Entities.FiveStepFrameWork", "BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_five_step_frame_works_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FiveStepFrameWorkHistory", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.FiveStepFrameWork", null)
                        .WithMany("Histories")
                        .HasForeignKey("OriginalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_five_step_frame_work_histories_five_step_frame_works_five_step_frame_work_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssues", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithMany("HypotheticalDiscussionOfIssues")
                        .HasForeignKey("BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_hypothetical_discussion_of_issues_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesComment", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesThread", null)
                        .WithMany("Comments")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_hdoi_comment_hdoi_thread_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesCommentFile", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesComment", null)
                        .WithMany("Files")
                        .HasForeignKey("CommentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_hdoi_comment_hdoi_file_comment_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesCommentReaction", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesComment", null)
                        .WithMany("Reactions")
                        .HasForeignKey("CommentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_hypothetical_discussion_of_issues_comment_reactions_hypothetical_discussion_of_issues_comments_hypothetical_discussion_of_is~");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesThread", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssues", null)
                        .WithMany("Threads")
                        .HasForeignKey("HypotheticalDiscussionOfIssuesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_hdoi_threads_hdoi_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesThreadFile", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesThread", null)
                        .WithMany("Files")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_hdoi_thread_hdoi_file_thread_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesThreadReaction", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesThread", null)
                        .WithMany("Reactions")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_hypothetical_discussion_of_issues_thread_reactions_hypothetical_discussion_of_issues_threads_hypothetical_discussion_of_issu~");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.Management", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithOne("Management")
                        .HasForeignKey("BusinessCustomerUnderstandingService.Domain.Entities.Management", "BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_managements_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ManagementHistory", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.Management", null)
                        .WithMany("Histories")
                        .HasForeignKey("OriginalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_management_histories_managements_management_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ManagementPlan", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithOne("ManagementPlan")
                        .HasForeignKey("BusinessCustomerUnderstandingService.Domain.Entities.ManagementPlan", "BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_management_plans_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ManagementPlanHistory", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.ManagementPlan", null)
                        .WithMany("Histories")
                        .HasForeignKey("OriginalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_management_plan_histories_management_plans_management_plan_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstanding", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithMany("OurPolicyUnderstanding")
                        .HasForeignKey("BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_our_policy_understandings_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingComment", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingThread", null)
                        .WithMany("Comments")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_our_policy_understanding_comment_our_policy_understanding_thread_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingCommentFile", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingComment", null)
                        .WithMany("Files")
                        .HasForeignKey("CommentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_our_policy_understanding_comment_our_policy_understanding_comment_file_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingCommentReaction", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingComment", null)
                        .WithMany("Reactions")
                        .HasForeignKey("CommentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_our_policy_understanding_comment_reactions_our_policy_understanding_comments_our_policy_understanding_comment_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingThread", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstanding", null)
                        .WithMany("Threads")
                        .HasForeignKey("OurPolicyUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_our_policy_understanding_threads_our_policy_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingThreadFile", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingThread", null)
                        .WithMany("Files")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_our_policy_understanding_thread_our_policy_understanding_thread_file_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingThreadReaction", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingThread", null)
                        .WithMany("Reactions")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_our_policy_understanding_thread_reactions_our_policy_understanding_threads_our_policy_understanding_thread_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.RelationLevel", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", "BusinessUnderstanding")
                        .WithOne("RelationLevel")
                        .HasForeignKey("BusinessCustomerUnderstandingService.Domain.Entities.RelationLevel", "BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_relation_levels_business_understandings_business_understanding_id");

                    b.Navigation("BusinessUnderstanding");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.RelationLevelHistory", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.RelationLevel", null)
                        .WithMany("Histories")
                        .HasForeignKey("RelationLevelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_relation_level_histories_relation_levels_relation_level_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SWOTAnalysis", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithOne("SWOTAnalysis")
                        .HasForeignKey("BusinessCustomerUnderstandingService.Domain.Entities.SWOTAnalysis", "BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_swot_analyses_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SWOTAnalysisHistory", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.SWOTAnalysis", null)
                        .WithMany("Histories")
                        .HasForeignKey("OriginalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_swot_analyses_histories_swot_analyses_swot_analysis_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinance", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithMany("SharingOfFinance")
                        .HasForeignKey("BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_sharing_of_finances_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceComment", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceThread", null)
                        .WithMany("Comments")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_sharing_of_finance_comment_sharing_of_finance_thread_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceCommentFile", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceComment", null)
                        .WithMany("Files")
                        .HasForeignKey("CommentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_sharing_of_finance_comment_sharing_of_finance_comment_file_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceCommentReaction", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceComment", null)
                        .WithMany("Reactions")
                        .HasForeignKey("CommentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_sharing_of_finance_comment_reactions_sharing_of_finance_comments_sharing_of_finance_comment_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceThread", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinance", null)
                        .WithMany("Threads")
                        .HasForeignKey("SharingOfFinanceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_sharing_of_finance_threads_sharing_of_finance_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceThreadFile", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceThread", null)
                        .WithMany("Files")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_sharing_of_finance_thread_sharing_of_finance_thread_file_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceThreadReaction", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceThread", null)
                        .WithMany("Reactions")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_sharing_of_finance_thread_reactions_sharing_of_finance_threads_sharing_of_finance_thread_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ThreeCAnalysis", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithOne("ThreeCAnalysis")
                        .HasForeignKey("BusinessCustomerUnderstandingService.Domain.Entities.ThreeCAnalysis", "BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_three_c_analyses_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ThreeCAnalysisHistory", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.ThreeCAnalysis", null)
                        .WithMany("Histories")
                        .HasForeignKey("ThreeCAnalysisId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_three_c_analysis_histories_three_c_analyses_three_c_analysis_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDo", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", null)
                        .WithMany("ToDo")
                        .HasForeignKey("BusinessUnderstandingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_to_dos_business_understandings_business_understanding_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDoComment", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.ToDoThread", null)
                        .WithMany("Comments")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_todo_comment_todo_thread_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDoCommentFile", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.ToDoComment", null)
                        .WithMany("Files")
                        .HasForeignKey("CommentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_todo_comment_todo_comment_file_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDoCommentReaction", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.ToDoComment", null)
                        .WithMany("Reactions")
                        .HasForeignKey("CommentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_to_do_comment_reactions_to_do_comments_to_do_comment_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDoThread", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.ToDo", null)
                        .WithMany("Threads")
                        .HasForeignKey("ToDoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_todo_threads_todo_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDoThreadFile", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.ToDoThread", null)
                        .WithMany("Files")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_todo_thread_todo_thread_file_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDoThreadReaction", b =>
                {
                    b.HasOne("BusinessCustomerUnderstandingService.Domain.Entities.ToDoThread", null)
                        .WithMany("Reactions")
                        .HasForeignKey("ThreadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_to_do_thread_reactions_to_do_threads_to_do_thread_id");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessCustomer", b =>
                {
                    b.Navigation("BusinessUnderstanding");

                    b.Navigation("Links");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessPartner", b =>
                {
                    b.Navigation("BusinessPartnerIndustry")
                        .IsRequired();
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstanding", b =>
                {
                    b.Navigation("BusinessPartners");

                    b.Navigation("BusinessPartnershipAndCreativeAccountingDetail");

                    b.Navigation("BusinessUnderstandingFile");

                    b.Navigation("BusinessUnderstandingMaterializedView");

                    b.Navigation("CommercialDistribution");

                    b.Navigation("CustomerIdeasUnderstanding");

                    b.Navigation("ESGAndSDGs");

                    b.Navigation("ExternalEnvironment");

                    b.Navigation("FamilyTree");

                    b.Navigation("FamilyTreeFile");

                    b.Navigation("FiveForceFramework");

                    b.Navigation("FiveStepFrameWork");

                    b.Navigation("Histories");

                    b.Navigation("HypotheticalDiscussionOfIssues");

                    b.Navigation("Management");

                    b.Navigation("ManagementPlan");

                    b.Navigation("OurPolicyUnderstanding");

                    b.Navigation("RelationLevel");

                    b.Navigation("SWOTAnalysis");

                    b.Navigation("SharingOfFinance");

                    b.Navigation("Threads");

                    b.Navigation("ThreeCAnalysis");

                    b.Navigation("ToDo");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingDiscussion", b =>
                {
                    b.Navigation("Files");

                    b.Navigation("Reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.BusinessUnderstandingThread", b =>
                {
                    b.Navigation("Discussions");

                    b.Navigation("Files");

                    b.Navigation("Reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistribution", b =>
                {
                    b.Navigation("Edges");

                    b.Navigation("Histories");

                    b.Navigation("Nodes");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionHistory", b =>
                {
                    b.Navigation("CommercialDistributionEdgeHistories");

                    b.Navigation("CommercialDistributionNodeHistories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CommercialDistributionTemplate", b =>
                {
                    b.Navigation("Edges");

                    b.Navigation("Nodes");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstanding", b =>
                {
                    b.Navigation("Threads");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingComment", b =>
                {
                    b.Navigation("Files");

                    b.Navigation("Reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.CustomerIdeasUnderstandingThread", b =>
                {
                    b.Navigation("Comments");

                    b.Navigation("Files");

                    b.Navigation("Reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ESGAndSDGs", b =>
                {
                    b.Navigation("Histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ExternalEnvironment", b =>
                {
                    b.Navigation("Histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTree", b =>
                {
                    b.Navigation("FamilyTreeEdges");

                    b.Navigation("FamilyTreeNodes");

                    b.Navigation("Histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FamilyTreeHistory", b =>
                {
                    b.Navigation("FamilyTreeEdgeHistories");

                    b.Navigation("FamilyTreeNodeHistories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FiveForceFramework", b =>
                {
                    b.Navigation("Histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.FiveStepFrameWork", b =>
                {
                    b.Navigation("Histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssues", b =>
                {
                    b.Navigation("CustomerReaction");

                    b.Navigation("Threads");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesComment", b =>
                {
                    b.Navigation("Files");

                    b.Navigation("Reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.HypotheticalDiscussionOfIssuesThread", b =>
                {
                    b.Navigation("Comments");

                    b.Navigation("Files");

                    b.Navigation("Reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.Management", b =>
                {
                    b.Navigation("Histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ManagementPlan", b =>
                {
                    b.Navigation("Histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstanding", b =>
                {
                    b.Navigation("Threads");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingComment", b =>
                {
                    b.Navigation("Files");

                    b.Navigation("Reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.OurPolicyUnderstandingThread", b =>
                {
                    b.Navigation("Comments");

                    b.Navigation("Files");

                    b.Navigation("Reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.RelationLevel", b =>
                {
                    b.Navigation("Histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SWOTAnalysis", b =>
                {
                    b.Navigation("Histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinance", b =>
                {
                    b.Navigation("Threads");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceComment", b =>
                {
                    b.Navigation("Files");

                    b.Navigation("Reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.SharingOfFinanceThread", b =>
                {
                    b.Navigation("Comments");

                    b.Navigation("Files");

                    b.Navigation("Reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ThreeCAnalysis", b =>
                {
                    b.Navigation("Histories");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDo", b =>
                {
                    b.Navigation("Threads");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDoComment", b =>
                {
                    b.Navigation("Files");

                    b.Navigation("Reactions");
                });

            modelBuilder.Entity("BusinessCustomerUnderstandingService.Domain.Entities.ToDoThread", b =>
                {
                    b.Navigation("Comments");

                    b.Navigation("Files");

                    b.Navigation("Reactions");
                });
#pragma warning restore 612, 618
        }
    }
}
