using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;
using Shared.Domain;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessCustomer.FindBusinessCustomer;

public class FindBusinessCustomerHandler : IRequestHandler<FindBusinessCustomerQuery, Result<List<FindBusinessCustomerResult>>>
{
    private readonly IRepository<Domain.Entities.BusinessCustomer> _repository;

    public FindBusinessCustomerHandler(IRepositoryFactory repositoryFactory)
    {
        if (repositoryFactory is null) throw new ArgumentNullException(nameof(repositoryFactory));
        _repository = repositoryFactory.GetRepository<Domain.Entities.BusinessCustomer>();
    }

    public async Task<Result<List<FindBusinessCustomerResult>>> Handle(FindBusinessCustomerQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 検索を行います。
        var spec = new FindBusinessCustomerSpecification(request);
        var result = await _repository.FindAsync(spec)
            .ConfigureAwait(false);

        return result.Map(f => f.Select(v => new FindBusinessCustomerResult(
            v.Id,
            v.CustomerIdentificationId,
            v.Version
            )).ToList());
    }
}
