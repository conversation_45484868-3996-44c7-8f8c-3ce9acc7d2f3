using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Domain.Entities;
using BusinessCustomerUnderstandingService.Externals.BusinessCustomerUnderstanding;
using BusinessCustomerUnderstandingService.Services.Domain;
using MediatR;
using Nut.Results;
using Shared.Collections;
using Shared.Domain;
using Shared.Messaging;
using Shared.Results.Errors;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistribution.UpdateCommercialDistribution;

public class UpdateCommercialDistributionHandler : IRequestHandler<UpdateCommercialDistributionCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageSender<UpdateBusinessUnderstandingUpdatedDateTime> _businessUnderstandingUpdateMessageSender;
    private readonly IQuillDeltaImageCompressService _quillDeltaImageCompressService;

    public UpdateCommercialDistributionHandler(
        IUnitOfWork unitOfWork,
        IMessageSender<UpdateBusinessUnderstandingUpdatedDateTime> businessUnderstandingUpdateMessageSender,
        IQuillDeltaImageCompressService quillDeltaImageCompressService
        )
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _businessUnderstandingUpdateMessageSender = businessUnderstandingUpdateMessageSender ?? throw new ArgumentNullException(nameof(businessUnderstandingUpdateMessageSender));
        _quillDeltaImageCompressService = quillDeltaImageCompressService ?? throw new ArgumentNullException(nameof(quillDeltaImageCompressService));
    }

    public async Task<Result<string>> Handle(UpdateCommercialDistributionCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CommercialDistribution, string>();
        // 既存データを取得します。
        var getResult = await repository.SingleAsync(
            new FindByIdSpecification<Domain.Entities.CommercialDistribution, string>(request.Id)
            .Include(c => c.Nodes)
            .Include(c => c.Edges));

        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        // 履歴の登録
        var currentData = getResult.Get();
        if (currentData.Histories is null)
        {
            currentData.Histories = new List<CommercialDistributionHistory>();
        }
        currentData.Histories.Add(new CommercialDistributionHistory
        {
            Id = Ulid.NewUlid().ToString(),
            CanvasColor = currentData.CanvasColor,
            UpdatedDateTime = currentData.UpdatedDateTime ?? currentData.CreatedAt,
            UpdaterId = currentData.UpdaterId,
            UpdaterName = currentData.UpdaterName,
            OriginalId = currentData.Id,
            CommercialDistributionNodeHistories = GetCurrentCommercialDistributionNodes(currentData),
            CommercialDistributionEdgeHistories = GetCurrentCommercialDistributionEdges(currentData)
        });

        currentData.CanvasColor = request.CanvasColor;
        currentData.UpdatedDateTime = request.UpdatedDateTime;
        currentData.UpdaterId = request.UpdaterId;
        currentData.UpdaterName = request.UpdaterName;
        // バージョン番号は、同時実行制御を有効にするために引数で受け取ったデータの値で必ず上書きします。
        currentData.Version = request.Version;

        // Nodeの更新
        var updateCommercialDistributionNodeResult =
            await UpdateCommercialDistributionNodesAsync(request.Nodes, currentData.Nodes);
        if (updateCommercialDistributionNodeResult.IsError) return updateCommercialDistributionNodeResult.PreserveErrorAs<string>();

        // Edgeの更新
        var updateCommercialDistributionEdgeResult =
            await UpdateCommercialDistributionEdgesAsync(request.Edges, currentData.Edges);
        if (updateCommercialDistributionEdgeResult.IsError) return updateCommercialDistributionEdgeResult.PreserveErrorAs<string>();

        // 事業性理解最終更新日の更新
        var updateBusinessUnderstandingResult = await _businessUnderstandingUpdateMessageSender.SendAsync(new UpdateBusinessUnderstandingUpdatedDateTime()
        {
            BusinessUnderstandingId = currentData.BusinessUnderstandingId,
            UpdaterId = request.UpdaterId,
            UpdaterName = request.UpdaterName,
        });
        if (updateBusinessUnderstandingResult.IsError) return updateBusinessUnderstandingResult.PreserveErrorAs<string>();

        return await repository
            .UpdateAsync(currentData) // データを更新として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }

    private List<CommercialDistributionNodeHistory> GetCurrentCommercialDistributionNodes(Domain.Entities.CommercialDistribution currentData)
    {
        var nodeHistories = new List<CommercialDistributionNodeHistory>();
        foreach (var currentNode in currentData.Nodes)
        {
            var nodeHistory = new CommercialDistributionNodeHistory();
            nodeHistory.Id = Ulid.NewUlid().ToString();
            nodeHistory.CommercialDistributionNodeId = currentNode.Id;
            nodeHistory.NodeType = currentNode.NodeType;
            nodeHistory.Left = currentNode.Left;
            nodeHistory.Top = currentNode.Top;
            nodeHistory.Width = currentNode.Width;
            nodeHistory.Height = currentNode.Height;
            nodeHistory.Title = currentNode.Title;
            nodeHistory.Text = currentNode.Text;
            nodeHistory.GroupingId = currentNode.GroupingId;
            nodeHistory.Color = currentNode.Color;
            nodeHistory.CustomerName = currentNode.CustomerName;
            nodeHistory.PersonType = currentNode.PersonType;
            nodeHistory.BranchNumber = currentNode.BranchNumber;
            nodeHistory.CifNumber = currentNode.CifNumber;
            nodeHistory.Merchandise = currentNode.Merchandise;
            nodeHistory.Material = currentNode.Material;
            nodeHistory.Amount = currentNode.Amount;
            nodeHistory.Industry = currentNode.Industry;
            nodeHistory.Area = currentNode.Area;
            nodeHistory.IsInternationalBusinessPartners = currentNode.IsInternationalBusinessPartners;
            nodeHistory.CountryCode = currentNode.CountryCode;
            nodeHistory.CityName = currentNode.CityName;
            nodeHistory.Note = currentNode.Note;
            nodeHistories.Add(nodeHistory);
        }
        return nodeHistories;
    }

    private List<CommercialDistributionEdgeHistory> GetCurrentCommercialDistributionEdges(Domain.Entities.CommercialDistribution currentData)
    {
        var edgeHistories = new List<CommercialDistributionEdgeHistory>();
        foreach (var currentEdge in currentData.Edges)
        {
            var edgeHistory = new CommercialDistributionEdgeHistory();
            edgeHistory.Id = Ulid.NewUlid().ToString();
            edgeHistory.CommercialDistributionEdgeId = currentEdge.Id;
            edgeHistory.SourceNode = currentEdge.SourceNode;
            edgeHistory.Source = currentEdge.Source;
            edgeHistory.TargetNode = currentEdge.TargetNode;
            edgeHistory.Target = currentEdge.Target;
            edgeHistories.Add(edgeHistory);
        }
        return edgeHistories;
    }

    private async Task<Result> UpdateCommercialDistributionNodesAsync(
    List<UpdateCommercialDistributionNodesCommand> requestItems,
    List<Domain.Entities.CommercialDistributionNode> currentItems)
    {
        // Idが設定されていて、Updatedがtrueのものは更新されている
        foreach (var requestDetail in requestItems.Where(r => r.Id is not null && r.Updated))
        {
            var item = currentItems.Where(i => i.Id == requestDetail.Id).FirstOrDefault();

            // 存在しない場合は、すでに更新されているとみなして同時更新エラーにする
            if (item is null) return Result.Error(new ChangeConflictException());

            item.NodeType = requestDetail.NodeType;
            item.Left = requestDetail.Left;
            item.Top = requestDetail.Top;
            item.Width = requestDetail.Width;
            item.Height = requestDetail.Height;
            item.Title = requestDetail.Title;

            var compressedText = requestDetail.Text;
            if (!string.IsNullOrEmpty(requestDetail.Text))
            {
                var compressTextResult = _quillDeltaImageCompressService.ConvertToImageCompressedDelta(requestDetail.Text);
                if (compressTextResult.IsError) return compressTextResult.PreserveErrorAs();
                compressedText = compressTextResult.Get();
            }
            item.Text = compressedText;

            item.GroupingId = requestDetail.GroupingId;
            item.CustomerName = requestDetail.CustomerName;
            item.Color = requestDetail.Color;
            item.PersonType = requestDetail.PersonType;
            item.BranchNumber = requestDetail.BranchNumber;
            item.CifNumber = requestDetail.CifNumber;
            item.Merchandise = requestDetail.Merchandise;
            item.Material = requestDetail.Material;
            item.Amount = requestDetail.Amount;
            item.Industry = requestDetail.Industry;
            item.Area = requestDetail.Area;
            item.IsInternationalBusinessPartners = requestDetail.IsInternationalBusinessPartners;
            item.CountryCode = requestDetail.CountryCode;
            item.CityName = requestDetail.CityName;

            var compressedNote = requestDetail.Note;
            if (!string.IsNullOrEmpty(requestDetail.Note))
            {
                var compressNoteResult = _quillDeltaImageCompressService.ConvertToImageCompressedDelta(requestDetail.Note);
                if (compressNoteResult.IsError) return compressNoteResult.PreserveErrorAs();
                compressedNote = compressNoteResult.Get();
            }
            item.Note = compressedNote;
        }

        // リクエストで渡ってきていないIDは削除されたとみなす
        var deleteItems = currentItems.Except(requestItems, ci => ci.Id, ri => ri.Id).ToList();
        if (deleteItems.Any())
        {
            var repo = _unitOfWork.GetRepository<Domain.Entities.CommercialDistributionNode>();
            foreach (var deleteItem in deleteItems)
            {
                // リポジトリを利用して削除とマーク
                await repo.DeleteAsync(deleteItem);
                currentItems.Remove(deleteItem);
            }
        }

        // Updatedがfalseのものは追加されている
        var newItems = requestItems
            .Where(r => !r.Updated)
            .Select(r => new Domain.Entities.CommercialDistributionNode()
            {
                Id = r.Id,
                NodeType = r.NodeType,
                Left = r.Left,
                Top = r.Top,
                Width = r.Width,
                Height = r.Height,
                Title = r.Title,
                Text = r.Text,
                GroupingId = r.GroupingId,
                Color = r.Color,
                CustomerName = r.CustomerName,
                PersonType = r.PersonType,
                BranchNumber = r.BranchNumber,
                CifNumber = r.CifNumber,
                Merchandise = r.Merchandise,
                Material = r.Material,
                Amount = r.Amount,
                Industry = r.Industry,
                Area = r.Area,
                IsInternationalBusinessPartners = r.IsInternationalBusinessPartners,
                CountryCode = r.CountryCode,
                CityName = r.CityName,
                Note = r.Note
            }).ToList();

        // Text,Noteの画像を圧縮して上書き
        foreach (var item in newItems)
        {
            var compressedText = item.Text;
            if (!string.IsNullOrEmpty(item.Text))
            {
                var compressTextResult = _quillDeltaImageCompressService.ConvertToImageCompressedDelta(item.Text);
                if (compressTextResult.IsError) return compressTextResult.PreserveErrorAs();
                compressedText = compressTextResult.Get();
            }
            var compressedNote = item.Note;
            if (!string.IsNullOrEmpty(item.Note))
            {
                var compressNoteResult = _quillDeltaImageCompressService.ConvertToImageCompressedDelta(item.Note);
                if (compressNoteResult.IsError) return compressNoteResult.PreserveErrorAs();
                compressedNote = compressNoteResult.Get();
            }
            item.Text = compressedText;
            item.Note = compressedNote;
        }

        currentItems.AddRange(newItems);

        return Result.Ok();
    }

    private async Task<Result> UpdateCommercialDistributionEdgesAsync(
   List<UpdateCommercialDistributionEdgesCommand> requestItems,
   List<Domain.Entities.CommercialDistributionEdge> currentItems)
    {
        // Idが設定されていて、Updatedがtrueのものは更新されている
        foreach (var requestDetail in requestItems.Where(r => r.Id is not null && r.Updated))
        {
            var item = currentItems.Where(i => i.Id == requestDetail.Id).FirstOrDefault();

            // 存在しない場合は、すでに更新されているとみなして同時更新エラーにする
            if (item is null) return Result.Error(new ChangeConflictException());

            item.SourceNode = requestDetail.SourceNode;
            item.Source = requestDetail.Source;
            item.TargetNode = requestDetail.TargetNode;
            item.Target = requestDetail.Target;
        }

        // リクエストで渡ってきていないIDは削除されたとみなす
        var deleteItems = currentItems.Except(requestItems, ci => ci.Id, ri => ri.Id).ToList();
        if (deleteItems.Any())
        {
            var repo = _unitOfWork.GetRepository<Domain.Entities.CommercialDistributionEdge>();
            foreach (var deleteItem in deleteItems)
            {
                // リポジトリを利用して削除とマーク
                await repo.DeleteAsync(deleteItem);
                currentItems.Remove(deleteItem);
            }
        }

        // Idが設定されておらず、Updatedがfalseのものは追加されている
        var newItems = requestItems
            .Where(r => r.Id is null && !r.Updated)
            .Select(r => new Domain.Entities.CommercialDistributionEdge()
            {
                Id = Ulid.NewUlid().ToString(),
                SourceNode = r.SourceNode,
                Source = r.Source,
                TargetNode = r.TargetNode,
                Target = r.Target,
            });
        currentItems.AddRange(newItems);

        return Result.Ok();
    }
}
