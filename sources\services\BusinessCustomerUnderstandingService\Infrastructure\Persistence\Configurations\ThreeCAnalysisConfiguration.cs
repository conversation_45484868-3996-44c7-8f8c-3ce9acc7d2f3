using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class ThreeCAnalysisConfiguration : IEntityTypeConfiguration<ThreeCAnalysis>
{
    public void Configure(EntityTypeBuilder<ThreeCAnalysis> builder)
    {
        builder.HasOne<BusinessUnderstanding>()
            .WithOne(l => l.ThreeCAnalysis)
            .HasForeignKey<ThreeCAnalysis>(j => j.BusinessUnderstandingId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .HasMaxLength(26);

        builder.Property(u => u.Customer)
            .HasMaxLength(1000);

        builder.Property(u => u.Company)
            .HasMaxLength(1000);

        builder.Property(u => u.Competitor)
            .HasMaxLength(1000);
    }
}
