using MediatR;
using Nut.MediatR.ServiceLike;
using Nut.Results;
using Shared.UseCase.AsServiceFilters;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.FindCommunicationPlanByStaffId;

[AsService("/business-customer-understanding/find-communicationplan-by-staffid", typeof(StripResultFilter))]
[WithDefaultBehaviors]
public record FindCommunicationPlanByStaffIdQuery : IRequest<Result<IEnumerable<FindCommunicationPlanByStaffIdResult>>>
{
    public string StaffId { get; init; } = default!;
}
