using FluentValidation;

namespace CustomerProposalService.UseCases.CaseDiscussionReplyReaction.UpdateCaseDiscussionReplyReaction;

public class UpdateCaseDiscussionReplyReactionValidator : AbstractValidator<UpdateCaseDiscussionReplyReactionCommand>
{
    public UpdateCaseDiscussionReplyReactionValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.ReactionType).IsInEnum();
        RuleFor(v => v.UpdatedDateTime).NotEmpty();
        RuleFor(v => v.Version).NotEmpty();
    }
}
