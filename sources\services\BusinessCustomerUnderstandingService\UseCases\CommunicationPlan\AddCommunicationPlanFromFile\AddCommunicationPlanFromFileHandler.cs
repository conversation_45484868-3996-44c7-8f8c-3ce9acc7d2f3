using System.Globalization;
using System.Text;
using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Infrastructure.Storage;
using CsvHelper.Configuration;
using MediatR;
using Nut.Results;
using Shared.ObjectStorage;
using Shared.Results.Errors;
using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.AddCommunicationPlanFromFile;

public class AddCommunicationPlanFromFileHandler : IRequestHandler<AddCommunicationPlanFromFileCommand, Result>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IBatchAddCommunicationPlanStorageClientProvider _objectStorageClientProvider;


    // 取り込みファイル名
    private readonly string _batchAddCommunicationPlanFileName = "batchAddCommunicationPlan.csv";

    public AddCommunicationPlanFromFileHandler(IUnitOfWork unitOfWork,
        IBatchAddCommunicationPlanStorageClientProvider objectStorageClientProvider)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
    }

    public async Task<Result> Handle(AddCommunicationPlanFromFileCommand request, CancellationToken cancellationToken)
    {
        return Result.Ok();
    }

    /// <summary>
    /// CSVファイルの読み取りに関する設定を取得
    /// </summary>
    /// <returns></returns>
    private CsvConfiguration GetCsvConfiguration()
    {
        return new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true,
            Delimiter = ",",
            IgnoreBlankLines = true,
            Encoding = Encoding.UTF8,
            AllowComments = false,
            DetectColumnCountChanges = true,
            TrimOptions = TrimOptions.Trim,
        };
    }

    /// <summary>
    /// Blobからファイルを取得
    /// </summary>
    private async Task<Result<Stream>> GetFileFromBlob(string folderName)
    {
        // エンコード設定
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

        // ObjectStorageのクライアントを作成します。
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        // 保存されているオブジェクトの一覧を取得
        var objects = await storageClient.GetObjectInformationsAsync(folderName).ConfigureAwait(false);
        if (objects.IsError) return objects.PreserveErrorAs<Stream>();

        // 指定されたファイルを取得
        var target = objects.Get().Where(e => e.Name == $"{folderName}/{_batchAddCommunicationPlanFileName}").FirstOrDefault();
        if (target is null) return Result.Error<Stream>(new DataNotFoundException());

        return await storageClient.GetAsync(target.Name);
    }

    /// <summary>
    /// ログファイルをBlobに作成
    /// </summary>
    private async void CreateLogFile(StringBuilder log, string folderName, string type)
    {
        // ObjectStorageのクライアントを作成。
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        var encoding = Encoding.UTF8;
        var stream = new MemoryStream(encoding.GetBytes(log.ToString()));

        await storageClient.PostAsync($"{folderName}/{type}_log.txt", stream).ConfigureAwait(false);
    }
}
