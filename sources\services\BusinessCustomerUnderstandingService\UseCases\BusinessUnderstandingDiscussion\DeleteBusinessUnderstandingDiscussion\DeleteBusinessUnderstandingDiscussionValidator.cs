using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussion.DeleteBusinessUnderstandingDiscussion;

public class DeleteBusinessUnderstandingDiscussionValidator : AbstractValidator<DeleteBusinessUnderstandingDiscussionCommand>
{
    public DeleteBusinessUnderstandingDiscussionValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.Version).NotEmpty();
    }
}
