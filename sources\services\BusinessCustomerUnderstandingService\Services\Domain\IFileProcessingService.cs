using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.Services.Domain;

public interface IFileProcessingService
{
    Task<Result<string>> UpdateFiles<TData, TFileType>(TData currentData, string containerName, string folderName, IEnumerable<IFormFile>? uploadFiles, IEnumerable<string>? filesToRemove)
    where TData : IFileProcessable<TFileType>
    where TFileType : class, new();
}
