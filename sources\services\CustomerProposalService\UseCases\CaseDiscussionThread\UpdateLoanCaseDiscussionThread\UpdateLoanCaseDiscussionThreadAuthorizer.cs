using Nut.MediatR;
using Shared.Services;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.UpdateLoanCaseDiscussionThread;
public class UpdateLoanCaseDiscussionThreadAuthorizer : IAuthorizer<UpdateLoanCaseDiscussionThreadCommand>
{
    private readonly ICurrentUserService _currentUserService;

    public UpdateLoanCaseDiscussionThreadAuthorizer(ICurrentUserService currentUserService)
    {
        _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
    }

    public async Task<AuthorizationResult> AuthorizeAsync(UpdateLoanCaseDiscussionThreadCommand request, CancellationToken cancellationToken)
    {
        var currentUser = await _currentUserService.GetAsync().ConfigureAwait(false);

        if (currentUser.UserId == request.RegistrantId)
        {
            return AuthorizationResult.Success();
        }

        return AuthorizationResult.Failed("不正なリクエストです。");
    }
}
