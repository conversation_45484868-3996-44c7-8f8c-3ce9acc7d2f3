using BusinessCustomerUnderstandingService.Domain.Enums;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThreadReaction.AddBUThreadReaction;

[WithDefaultBehaviors]
public record AddBUThreadReactionCommand(
    string ThreadId,
    string StaffId,
    string StaffName,
    ReactionType ReactionType,
    DateTimeOffset UpdatedDateTime
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
