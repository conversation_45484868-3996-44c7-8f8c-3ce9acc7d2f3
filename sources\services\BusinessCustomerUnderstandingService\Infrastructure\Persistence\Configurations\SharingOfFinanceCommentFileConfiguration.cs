using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;

public class SharingOfFinanceCommentFileConfiguration : IEntityTypeConfiguration<SharingOfFinanceCommentFile>
{
    public void Configure(EntityTypeBuilder<SharingOfFinanceCommentFile> builder)
    {
        builder.HasOne<SharingOfFinanceComment>()
            .WithMany(l => l.Files)
            .HasForeignKey(l => l.CommentId)
            .HasConstraintName("fk_sharing_of_finance_comment_sharing_of_finance_comment_file_id")
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.FileName)
            .IsRequired()
            .HasMaxLength(300);
    }
}
