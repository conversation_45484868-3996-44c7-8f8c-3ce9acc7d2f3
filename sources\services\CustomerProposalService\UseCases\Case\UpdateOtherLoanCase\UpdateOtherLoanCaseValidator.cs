using FluentValidation;

namespace CustomerProposalService.UseCases.Case.UpdateOtherLoanCase;

public class UpdateOtherLoanCaseValidator : AbstractValidator<UpdateOtherLoanCaseCommand>
{
    public UpdateOtherLoanCaseValidator()
    {
        // Case Entity
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(e => e.CaseName).NotEmpty().MaximumLength(50);
        RuleFor(e => e.CaseStatus).NotEmpty().IsInEnum().NotEqual(Domain.Enums.CaseStatus.Undefined);
        RuleFor(e => e.CaseOutline).CaseOutline();
        RuleFor(e => e.StaffId).NotEmpty().MaximumLength(50);
        RuleFor(e => e.StaffName).NotEmpty().MaximumLength(50);
        RuleFor(v => v.Version).NotEmpty();
        // OtherLoanCase Entity なし
    }
}
