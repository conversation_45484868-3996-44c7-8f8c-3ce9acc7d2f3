using BusinessCustomerUnderstandingService.Domain.Enums;
using Nut.Results;
using Shared.Application;

namespace BusinessCustomerUnderstandingService.Services.Queries;

public interface IFindCustomerIdentificationIdsBySearchConditionQueryService
{
    Task<Result<List<Guid>>> Handle(FindCustomerIdentificationIdsBySearchConditionQuery request);
}

public record FindCustomerIdentificationIdsBySearchConditionQuery(
    IEnumerable<string>? UserIds,
    IEnumerable<BusinessUnderstandingApproachType>? ApproachTypes,
    IEnumerable<string>? TransactionPolicies,
    IEnumerable<Guid>? CustomerIdentificationIds,
    int CountLimit,
    IList<Sort> Sort
    );
