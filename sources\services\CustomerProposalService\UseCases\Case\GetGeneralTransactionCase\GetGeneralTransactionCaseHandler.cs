using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.GetGeneralTransactionCase;

public class GetGeneralTransactionCaseHandler : IRequestHandler<GetGeneralTransactionCaseQuery, Result<GetGeneralTransactionCaseResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetGeneralTransactionCaseHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetGeneralTransactionCaseResult>> Handle(GetGeneralTransactionCaseQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.GeneralTransactionCase, string>();
        return repository.SingleAsync(new GetGeneralTransactionCaseSpecification(request))
            .Map(v =>
                new GetGeneralTransactionCaseResult
                (
                    v.Id,
                    v.CustomerIdentificationId,
                    v.CaseCategory,
                    v.CaseName,
                    v.CaseStatus,
                    v.CaseOutline,
                    v.ExpiredAt,
                    v.StaffId,
                    v.StaffName,
                    v.RegisteredAt,
                    v.CaseUpdatedAt,
                    v.CaseUpdateInformation.LastUpdatedAt,
                    v.GeneralTransactionTypeId,
                    v.CaseFiles,
                    v.CaseLinks,
                    v.Version
                    ));
    }
}
