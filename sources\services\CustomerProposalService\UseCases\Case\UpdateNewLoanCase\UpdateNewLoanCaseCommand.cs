using CustomerProposalService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.UpdateNewLoanCase;

[WithDefaultBehaviors]
public record UpdateNewLoanCaseCommand(
    // Case Entity
    string Id,
    string CaseName,
    CaseStatus CaseStatus,
    string? CaseOutline,
    DateTimeOffset? ExpiredAt,
    string StaffId,
    string StaffName,
    IEnumerable<IFormFile>? UploadFiles,
    IEnumerable<string>? CaseLinks,
    // NewLoanCase Entity
    SubjectType? SubjectType,
    decimal? Amount,
    int? Period,
    decimal? InterestRate,
    string? InterestRateCustom,
    UseOfFundsType? UseOfFundsType,
    string? UseOfFundsCustom,
    RepaymentMethodType? RepaymentMethodType,
    string? RepaymentMethodCustom,
    bool? CollateralOrGuarantee,
    string? CollateralOrGuaranteeCustom,
    CancelTypeOfLoan? CancelTypeOfLoan,
    string? CancelReason,
    bool? PreConsultationStandardTarget,
    bool? RelatedEsg,
    bool IsEarthquakeRelated,
    TrafficSource? TrafficSource,
    string Version
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
