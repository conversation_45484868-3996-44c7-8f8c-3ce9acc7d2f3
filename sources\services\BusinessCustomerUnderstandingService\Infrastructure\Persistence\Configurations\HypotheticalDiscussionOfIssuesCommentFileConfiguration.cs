using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;

public class HypotheticalDiscussionOfIssuesCommentFileConfiguration : IEntityTypeConfiguration<HypotheticalDiscussionOfIssuesCommentFile>
{
    public void Configure(EntityTypeBuilder<HypotheticalDiscussionOfIssuesCommentFile> builder)
    {
        builder.HasOne<HypotheticalDiscussionOfIssuesComment>()
            .WithMany(l => l.Files)
            .HasForeignKey(j => j.CommentId)
            .HasConstraintName("fk_hdoi_comment_hdoi_file_comment_id")
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMax<PERSON>ength(26);

        builder.Property(u => u.FileName)
            .IsRequired()
            .HasMaxLength(300);
    }
}
