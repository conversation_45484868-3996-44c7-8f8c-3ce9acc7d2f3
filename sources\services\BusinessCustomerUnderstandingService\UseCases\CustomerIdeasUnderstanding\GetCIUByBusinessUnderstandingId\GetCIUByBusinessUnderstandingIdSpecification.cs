using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CustomerIdeasUnderstanding.GetCIUByBusinessUnderstandingId;

public class GetCIUByBusinessUnderstandingIdSpecification : BaseSpecification<Domain.Entities.CustomerIdeasUnderstanding>
{
    public GetCIUByBusinessUnderstandingIdSpecification(string businessUnderstandingId)
    {
        Query
            .Where(x => x.BusinessUnderstandingId == businessUnderstandingId)
            .Include(x => x.Threads, x => x.ThenInclude(x => x.Comments).ThenInclude(x => x.Files))
            .Include(x => x.Threads, x => x.ThenInclude(x => x.Comments).ThenInclude(x => x.Reactions))
            .Include(x => x.Threads, x => x.ThenInclude(x => x.Files))
            .Include(x => x.Threads, x => x.ThenInclude(x => x.Reactions))
            .OrderBy(x => x.Order)
            .AsNoTracking();
    }
}
