using MediatR;
using Nut.Results;
using Shared.Application;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindCustomerIdentificationIdsByBusinessUnderstandingIds;

public class FindCustomerIdentificationIdsByBusinessUnderstandingIdsHandler : IRequestHandler<FindCustomerIdentificationIdsByBusinessUnderstandingIdsQuery, Result<List<FindCustomerIdentificationIdsByBusinessUnderstandingIdsResult>>>
{
    private readonly IFindCustomerIdentificationIdsByBusinessUnderstandingIdsQueryService _queryService;

    public FindCustomerIdentificationIdsByBusinessUnderstandingIdsHandler(IFindCustomerIdentificationIdsByBusinessUnderstandingIdsQueryService queryService)
    {
        _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
    }

    public async Task<Result<List<FindCustomerIdentificationIdsByBusinessUnderstandingIdsResult>>> Handle(FindCustomerIdentificationIdsByBusinessUnderstandingIdsQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var result = await _queryService.Handle(request);
        return result.Map(x => x.Select(v => new FindCustomerIdentificationIdsByBusinessUnderstandingIdsResult(
            v.BusinessUnderstandingId,
            v.BusinessCustomerId,
            v.CustomerIdentificationId)).ToList());
    }
}
