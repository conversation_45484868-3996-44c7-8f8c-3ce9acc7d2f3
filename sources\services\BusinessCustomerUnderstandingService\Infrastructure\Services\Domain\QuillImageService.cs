using BusinessCustomerUnderstandingService.Configurations;
using BusinessCustomerUnderstandingService.Infrastructure.Storage;
using Nut.Results;
using Shared.AzureBlob.QuillContents;

namespace BusinessCustomerUnderstandingService.Services.Domain;

public class QuillImageService : IQuillImageService
{
    private readonly IStorageClientProvider _objectStorageClientProvider;
    private readonly ServiceSettings _serviceSettings;
    private readonly IQuillContentsUtility _quillContentsUtility;
    private readonly string _baseUrl;

    public QuillImageService(
        IStorageClientProvider objectStorageClientProvider,
        ServiceSettings serviceSettings,
        IQuillContentsUtility quillContentsUtility)
    {
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
        _serviceSettings = serviceSettings ?? throw new ArgumentNullException(nameof(serviceSettings));
        _quillContentsUtility = quillContentsUtility ?? throw new ArgumentNullException(nameof(quillContentsUtility));
        _baseUrl = _serviceSettings.Blob.AttachedContentsBaseUrl ?? string.Empty;
    }

    public async Task<Result<string>> ArrangeContentByUrlWhenSaveAsync(string containerName, string imageAddressPrefix, string delta, string id)
    {
        return await _quillContentsUtility.ArrangeDeltaAndUploadContentsAsync(_objectStorageClientProvider, _baseUrl, containerName, GetImageAddress(imageAddressPrefix, id), delta);
    }

    public async Task<Result<string>> GetArrangedDeltaWhenPublish(string delta)
    {
        return await _quillContentsUtility.GetArrangedDeltaWhenPublish(delta);
    }

    public async Task<Result<string>> DeleteAllContentsAsync(string containerName, string imageAddressPrefix, string id)
    {
        return await _quillContentsUtility.DeleteAllContentsAsync(_objectStorageClientProvider, containerName, GetImageAddress(imageAddressPrefix, id));
    }

    private static string GetImageAddress(string imageAddressPrefix, string id)
    {
        return $"{imageAddressPrefix}/{id}/description";
    }
}

