using Microsoft.EntityFrameworkCore;
using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBasicInfoOfBusinessUnderstanding;

public class GetBasicInfoOfBusinessUnderstandingSpecification : BaseSpecification<Domain.Entities.BusinessUnderstanding>
{
    public GetBasicInfoOfBusinessUnderstandingSpecification(string id)
    {
        Query
            .Where(x => x.Id == id)
            .Include(x => x.BusinessCustomer)
            .AsNoTracking();
    }
}
