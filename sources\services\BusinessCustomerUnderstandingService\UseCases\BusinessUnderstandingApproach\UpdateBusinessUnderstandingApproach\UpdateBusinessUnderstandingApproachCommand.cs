using BusinessCustomerUnderstandingService.Domain.Enums;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.UpdateBusinessUnderstandingApproach;

[WithDefaultBehaviors]
public record UpdateBusinessUnderstandingApproachCommand(
    string BusinessUnderstandingId,
    string Id,
    BusinessUnderstandingApproachType ApproachType,
    string? Comment,
    string UpdaterId,
    string UpdaterName,
    DateTimeOffset UpdatedDateTime,
    string BusinessUnderstandingVersion,
    string Version
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
