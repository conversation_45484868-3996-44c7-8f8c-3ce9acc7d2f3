using CustomerFixedInformationService.Domain;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Shared.Domain;
using CustomerFixedInformationService.UseCases.CustomerStaffUpdateProgress.FindCustomerStaffUpdateProgress;
using FluentAssertions;
using Moq;
using Nut.Results;
using Xunit;


namespace CustomerFixedInformationService.Tests.UseCases.CustomerStaffUpdateProgress.FindCustomerStaffUpdateProgress;

public class FindCustomerStaffUpdateProgressHandlerTest
{
    private readonly Mock<IRepository<Domain.Entities.CustomerStaffUpdateProgress>> _repositoryMock = new();
    private readonly Mock<IUnitOfWork> _unitOfWorkMock = new();

    private FindCustomerStaffUpdateProgressHandler CreateHandler()
    {
        _unitOfWorkMock.Setup(f => f.GetRepository<Domain.Entities.CustomerStaffUpdateProgress>())
            .Returns(_repositoryMock.Object);
        return new FindCustomerStaffUpdateProgressHandler(_unitOfWorkMock.Object);
    }

    [Fact]
    public async Task Handle_SuccessCases()
    {
        var entities = new List<Domain.Entities.CustomerStaffUpdateProgress>
        {
            new Domain.Entities.CustomerStaffUpdateProgress
            {
                Id = "id1",
                IsCompleted = true,
                ResultMessage = "done",
                LatestRunDateTimeFrom = DateTimeOffset.UtcNow.AddDays(-1),
                LatestRunDateTimeTo = DateTimeOffset.UtcNow,
                Version = "2"
            }
        };
        _repositoryMock.Setup(r => r.FindAsync(It.IsAny<Shared.Spec.ISpecification<Domain.Entities.CustomerStaffUpdateProgress>>()))
            .ReturnsAsync(Result.Ok(entities));
        var handler = CreateHandler();
        var query = new FindCustomerStaffUpdateProgressQuery { IsCompleted = true };
        var result = await handler.Handle(query, CancellationToken.None);
        result.IsOk.Should().BeTrue();
        result.Get().Should().HaveCount(1);
        var mapped = result.Get()[0];
        mapped.Id.Should().Be("id1");
        mapped.IsCompleted.Should().BeTrue();
        mapped.ResultMessage.Should().Be("done");
        mapped.Version.Should().Be("2");

        _repositoryMock.Reset();
        _repositoryMock.Setup(r => r.FindAsync(It.IsAny<Shared.Spec.ISpecification<Domain.Entities.CustomerStaffUpdateProgress>>()))
            .ReturnsAsync(Result.Ok(new List<Domain.Entities.CustomerStaffUpdateProgress>()));
        handler = CreateHandler();
        query = new FindCustomerStaffUpdateProgressQuery();
        result = await handler.Handle(query, CancellationToken.None);
        result.IsOk.Should().BeTrue();
        result.Get().Should().BeEmpty();
    }

    [Fact]
    public async Task Handle_FailureCases()
    {
        _repositoryMock.Setup(r => r.FindAsync(It.IsAny<Shared.Spec.ISpecification<Domain.Entities.CustomerStaffUpdateProgress>>()))
            .ReturnsAsync(Result.Error<List<Domain.Entities.CustomerStaffUpdateProgress>>("db error"));
        var handler = CreateHandler();
        var query = new FindCustomerStaffUpdateProgressQuery { IsCompleted = false };
        var result = await handler.Handle(query, CancellationToken.None);
        result.IsError.Should().BeTrue();

        _repositoryMock.Reset();
        _repositoryMock.Setup(r => r.FindAsync(It.IsAny<Shared.Spec.ISpecification<Domain.Entities.CustomerStaffUpdateProgress>>()))
            .ThrowsAsync(new Exception("db exception"));
        handler = CreateHandler();
        query = new FindCustomerStaffUpdateProgressQuery();
        Func<Task> act = async () => await handler.Handle(query, CancellationToken.None);
        await act.Should().ThrowAsync<Exception>();
    }

    [Fact]
    public async Task Handle_ExceptionCases()
    {
        var handler = CreateHandler();
        await Assert.ThrowsAsync<ArgumentNullException>(() => handler.Handle(null!, CancellationToken.None));
    }
}
