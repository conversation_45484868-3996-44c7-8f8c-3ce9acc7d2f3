using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class OurPolicyUnderstandingCommentFileConfiguration : IEntityTypeConfiguration<OurPolicyUnderstandingCommentFile>
{
    public void Configure(EntityTypeBuilder<OurPolicyUnderstandingCommentFile> builder)
    {
        builder.HasOne<OurPolicyUnderstandingComment>()
            .WithMany(l => l.Files)
            .HasForeignKey(l => l.CommentId)
            .HasConstraintName("fk_our_policy_understanding_comment_our_policy_understanding_comment_file_id")
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.FileName)
            .IsRequired()
            .HasMaxLength(300);
    }
}
