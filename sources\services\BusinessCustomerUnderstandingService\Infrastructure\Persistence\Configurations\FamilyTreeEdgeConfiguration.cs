using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class FamilyTreeEdgeConfiguration : IEntityTypeConfiguration<FamilyTreeEdge>
{
    public void Configure(EntityTypeBuilder<FamilyTreeEdge> builder)
    {
        builder.HasOne<FamilyTree>()
            .WithMany(c => c.FamilyTreeEdges)
            .HasForeignKey(e => e.FamilyTreeId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(e => e.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(e => e.SourceNode)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(e => e.Source)
            .IsRequired()
            .HasMaxLength(32);

        builder.Property(e => e.TargetNode)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(e => e.Target)
            .IsRequired()
            .HasMaxLength(32);
    }
}
