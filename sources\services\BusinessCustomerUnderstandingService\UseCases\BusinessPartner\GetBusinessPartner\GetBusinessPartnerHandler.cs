using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Services.Queries;
using MediatR;
using Nut.Results;
using Shared.Domain;
using Shared.Messaging;
using SharedKernel.ExternalApi.MessageContract.CustomerIdentifying;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartner.GetBusinessPartner;

public class GetBusinessPartnerHandler : IRequestHandler<GetBusinessPartnerQuery, Result<GetBusinessPartnerResult>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IGetBPAndCAByCustomerIdentificationIdQueryService _getBPAndCPQueryService;
    private readonly IMessageSender<FindCustomerByIdsQuery, IEnumerable<FindCustomerByIdsResult>> _findCustomerByIdsSender;


    public GetBusinessPartnerHandler(
        IUnitOfWork unitOfWork,
        IGetBPAndCAByCustomerIdentificationIdQueryService queryService,
        IMessageSender<FindCustomerByIdsQuery, IEnumerable<FindCustomerByIdsResult>> findCustomerByIdsSender
        )
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _getBPAndCPQueryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
        _findCustomerByIdsSender = findCustomerByIdsSender ?? throw new ArgumentNullException(nameof(findCustomerByIdsSender));
    }

    public async Task<Result<GetBusinessPartnerResult>> Handle(GetBusinessPartnerQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessPartner>();
        var spec = new FindByIdSpecification<Domain.Entities.BusinessPartner, string>(request.Id)
            .Include(x => x.BusinessPartnerIndustry)
            .AsNoTracking();

        var singleAsyncBusinessPartnerResult = await repository.SingleAsync(spec).ConfigureAwait(false);
        if (singleAsyncBusinessPartnerResult.IsError) return singleAsyncBusinessPartnerResult.PreserveErrorAs<GetBusinessPartnerResult>();
        var businessPartner = singleAsyncBusinessPartnerResult.Get();

        var getBusinessPartnerResult = new GetBusinessPartnerResult(
                Id: businessPartner.Id,
                BusinessUnderstandingId: businessPartner.BusinessUnderstandingId,
                BusinessPartnerCustomerIdentificationId: businessPartner.BusinessPartnerCustomerIdentificationId,
                BusinessPartnerCustomerName: businessPartner.BusinessPartnerCustomerName,
                BusinessPartnerIndustry:
                    new(
                        Id: businessPartner.BusinessPartnerIndustry.Id,
                        BusinessPartnerId: businessPartner.BusinessPartnerIndustry.BusinessPartnerId,
                        SubIndustryCode: businessPartner.BusinessPartnerIndustry.SubIndustryCode,
                        DetailIndustryCode: businessPartner.BusinessPartnerIndustry.DetailIndustryCode,
                        IndustryCode: businessPartner.BusinessPartnerIndustry.IndustryCode
                    ),
                HasBusinessPartnershipWithOurCompany: null,
                HasCreativeAccountingIncident: null,
                Note: businessPartner.Note,
                Version: businessPartner.Version
        );

        // 顧客識別IDが登録されている場合、提携・粉飾情報と顧客名を取得
        if (getBusinessPartnerResult.BusinessPartnerCustomerIdentificationId is not null)
        {
            // 提携・粉飾情報を取得
            var getBPAndCAResult = await _getBPAndCPQueryService.Handle(new List<Guid>() { getBusinessPartnerResult.BusinessPartnerCustomerIdentificationId.Value }).ConfigureAwait(false);
            if (getBPAndCAResult.IsError) return getBPAndCAResult.PreserveErrorAs<GetBusinessPartnerResult>();
            var businessPartnershipAndCreativeAccounting = getBPAndCAResult.Get().FirstOrDefault();

            // 顧客名を取得
            var getCustomersResult = await _findCustomerByIdsSender.SendAsync(new() { Ids = new List<Guid>() { getBusinessPartnerResult.BusinessPartnerCustomerIdentificationId.Value } }).ConfigureAwait(false);
            if (getCustomersResult.IsError) return getCustomersResult.PreserveErrorAs<GetBusinessPartnerResult>();
            var customer = getCustomersResult.Get().FirstOrDefault();

            // 取得結果をgetBusinessPartnerResultに付加する
            getBusinessPartnerResult = getBusinessPartnerResult with
            {
                BusinessPartnerCustomerName = string.IsNullOrEmpty(customer?.Name) ? null : customer.Name,
                HasBusinessPartnershipWithOurCompany = businessPartnershipAndCreativeAccounting?.HasBusinessPartnershipWithOurCompany,
                HasCreativeAccountingIncident = businessPartnershipAndCreativeAccounting?.HasCreativeAccountingIncident,
            };
        }

        return Result.Ok(getBusinessPartnerResult);
    }
}
