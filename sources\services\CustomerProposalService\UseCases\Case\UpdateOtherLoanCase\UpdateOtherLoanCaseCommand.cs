using CustomerProposalService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.UpdateOtherLoanCase;

[WithDefaultBehaviors]
public record UpdateOtherLoanCaseCommand(
    // Case Entity
    string Id,
    string CaseName,
    CaseStatus CaseStatus,
    string? CaseOutline,
    DateTimeOffset? ExpiredAt,
    string StaffId,
    string StaffName,
    IEnumerable<IFormFile>? UploadFiles,
    IEnumerable<string>? CaseLinks,
    // OtherLoanCase Entity
    bool? PreConsultationStandardTarget,
    bool IsEarthquakeRelated,
    string Version
    ) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
