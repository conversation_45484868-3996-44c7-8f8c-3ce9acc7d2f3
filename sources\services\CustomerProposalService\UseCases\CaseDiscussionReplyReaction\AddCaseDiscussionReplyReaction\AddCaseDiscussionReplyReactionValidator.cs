using FluentValidation;

namespace CustomerProposalService.UseCases.CaseDiscussionReplyReaction.AddCaseDiscussionReplyReaction;

public class AddCaseDiscussionReplyReactionValidator : AbstractValidator<AddCaseDiscussionReplyReactionCommand>
{
    public AddCaseDiscussionReplyReactionValidator()
    {
        RuleFor(v => v.CaseDiscussionReplyId).NotEmpty();
        RuleFor(v => v.UpdaterId).NotEmpty();
        RuleFor(v => v.UpdaterName).NotEmpty();
        RuleFor(v => v.ReactionType).IsInEnum();
        RuleFor(v => v.UpdatedDateTime).NotEmpty();
    }
}
