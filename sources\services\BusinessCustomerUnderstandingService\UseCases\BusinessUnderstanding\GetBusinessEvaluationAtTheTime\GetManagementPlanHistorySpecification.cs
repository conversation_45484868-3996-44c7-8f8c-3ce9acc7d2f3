using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessEvaluationAtTheTime;

public class GetManagementPlanHistorySpecification : BaseSpecification<Domain.Entities.ManagementPlanHistory>
{
    public GetManagementPlanHistorySpecification(string originalId, DateTimeOffset targetDateTime)
    {
        Query.Where(x => x.OriginalId == originalId && x.UpdatedDateTime == targetDateTime).AsNoTracking();
    }
}
