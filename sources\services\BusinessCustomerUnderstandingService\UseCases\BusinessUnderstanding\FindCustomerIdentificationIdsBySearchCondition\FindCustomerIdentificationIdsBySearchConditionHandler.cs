using BusinessCustomerUnderstandingService.Services.Queries;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindCustomerIdentificationIdsBySearchCondition;

public class FindCustomerIdentificationIdsBySearchConditionHandler : IRequestHandler<FindCustomerIdentificationIdsBySearchConditionQuery, Result<List<Guid>>>
{
    private readonly IFindCustomerIdentificationIdsBySearchConditionQueryService _queryService;

    public FindCustomerIdentificationIdsBySearchConditionHandler(IFindCustomerIdentificationIdsBySearchConditionQueryService queryService)
    {
        if (queryService is null) throw new ArgumentNullException(nameof(queryService));
        _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
    }

    public async Task<Result<List<Guid>>> Handle(FindCustomerIdentificationIdsBySearchConditionQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var query = new Services.Queries.FindCustomerIdentificationIdsBySearchConditionQuery(
            request.UserIds,
            request.ApproachTypes,
            request.TransactionPolicies,
            request.CustomerIdentificationIds,
            request.CountLmit,
            request.Sort);

        return await _queryService.Handle(query);
    }
}
