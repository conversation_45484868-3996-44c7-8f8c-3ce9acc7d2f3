using CustomerProposalService.Domain;
using CustomerProposalService.Domain.Enums;
using CustomerProposalService.Infrastructure.Storage;
using Microsoft.AspNetCore.Http;
using Nut.Results;
using BusinessMatching = SharedKernel.ExternalApi.Domain.MatchingRecommendationCandidate;
using MAndAMatching = SharedKernel.ExternalApi.Domain.MAndAMatchingCaseManagement;
using Status = CustomerProposalService.Domain.Enums.CaseStatus;
using BusinessMatchingStatus = SharedKernel.ExternalApi.Domain.MatchingRecommendationCandidate.CaseStatus;
using MAndAMatchingStatus = SharedKernel.ExternalApi.Domain.MAndAMatchingCaseManagement.CaseStatus;
using CommonCaseStatus = SharedKernel.ExternalApi.Domain.CaseWorkflowOptimization.CaseStatus;

namespace CustomerProposalService.UseCases.Case;

public class CaseUtility : ICaseUtility
{
    private readonly ICaseStorageClientProvider _objectStorageClientProvider;

    public CaseUtility(
        ICaseStorageClientProvider objectStorageClientProvider)
    {
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
    }

    public async Task<Result<string>> AddFiles(IEnumerable<IFormFile>? files, string id)
    {
        if (files?.Any() == true)
        {
            var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);
            foreach (var file in files.Select(file => file))
            {
                var stream = file.OpenReadStream();
                var result = await storageClient.PostAsync($"{id}/{file.FileName}", stream).ConfigureAwait(false);
                if (result.IsError) return result.PreserveErrorAs<string>();
            }
        }
        return Result.Ok("");
    }

    public async Task<Result<string>> ReplaceFiles(IUnitOfWork unitOfWork, IEnumerable<IFormFile>? uploadFiles, Domain.Entities.Case currentData)
    {
        if (uploadFiles?.Any() == true)
        {
            // ファイルをアップロード
            var uploadResult = await UpdateFiles(unitOfWork, uploadFiles, currentData);
            return uploadResult;
        }
        else
        {
            // DBおよびBlobからファイルを全て削除
            var deleteResult = await DeleteAllFiles(unitOfWork, currentData);
            return deleteResult;
        }
    }

    public virtual async Task<Result<string>> UpdateFiles(IUnitOfWork unitOfWork, IEnumerable<IFormFile> uploadFiles, Domain.Entities.Case currentData)
    {
        var repository = unitOfWork.GetRepository<Domain.Entities.CaseFile>();

        // Blobとの接続を確立
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        // リクエストされたファイルに存在しないファイルをblobとテーブルから削除
        if (currentData.CaseFiles?.Any() == true)
        {
            foreach (var currentFile in currentData.CaseFiles)
            {
                var selectedFileName = uploadFiles.FirstOrDefault(x => x.FileName == currentFile.FileName);
                if (selectedFileName != null) continue;

                // DB削除
                var deleteResult = await repository.DeleteAsync(currentFile).ConfigureAwait(false);
                if (deleteResult.IsError) return deleteResult.PreserveErrorAs<string>();

                // Blobから削除
                var deleteFileResult = await storageClient.DeleteAsync($"{currentData.Id}/{currentFile.FileName}").ConfigureAwait(false);
                if (deleteFileResult.IsError) return deleteFileResult.PreserveErrorAs<string>();
            }
        }

        foreach (var uploadFile in uploadFiles)
        {
            // 中身のないファイル(Length = 0)：既に登録されているファイルなので何もしない
            if (uploadFile.Length == 0) continue;

            // テーブルに存在しないファイル：新規追加（テーブルに登録)
            var currentFile = currentData.CaseFiles?.FirstOrDefault(x => x.FileName == uploadFile.FileName);
            if (currentFile == null)
            {
                var fileEntity = new Domain.Entities.CaseFile()
                {
                    Id = Ulid.NewUlid().ToString(),
                    FileName = uploadFile.FileName,
                    CaseId = currentData.Id,
                };

                // DB登録
                var addResult = await repository.AddAsync(fileEntity).ConfigureAwait(false);
                if (addResult.IsError) return addResult.PreserveErrorAs<string>();
            }

            // Blobに登録(上書き対象のファイルも含む)
            var stream = uploadFile.OpenReadStream();
            var addFileResult = await storageClient.PostAsync($"{currentData.Id}/{uploadFile.FileName}", stream).ConfigureAwait(false);
            if (addFileResult.IsError) return addFileResult.PreserveErrorAs<string>();
        }

        return Result.Ok("");
    }

    public virtual async Task<Result<string>> DeleteAllFiles(IUnitOfWork unitOfWork, Domain.Entities.Case currentData)
    {
        if (currentData.CaseFiles?.Any() != true) return Result.Ok("");

        var repository = unitOfWork.GetRepository<Domain.Entities.CaseFile>();

        // Blobとの接続を確立
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        foreach (var file in currentData.CaseFiles)
        {
            // DB削除
            var deleteResult = await repository.DeleteAsync(file).ConfigureAwait(false);
            if (deleteResult.IsError) return deleteResult.PreserveErrorAs<string>();

            // Blobから削除
            var deleteFileResult = await storageClient.DeleteAsync($"{currentData.Id}/{file.FileName}").ConfigureAwait(false);
            if (deleteFileResult.IsError) return deleteFileResult.PreserveErrorAs<string>();
        }

        return Result.Ok("");
    }

    public async Task<Result<string>> ReplaceLinks(IUnitOfWork unitOfWork, IEnumerable<Domain.Entities.CaseLink> caseLinks, Domain.Entities.Case currentData)
    {
        if (caseLinks?.Any() == true)
        {
            // リンクを登録
            var uploadResult = await UpdateLinks(unitOfWork, caseLinks, currentData);
            return uploadResult;
        }
        else
        {
            // リンクを全て削除
            var deleteResult = await DeleteAllLinks(unitOfWork, currentData);
            return deleteResult;
        }
    }
    /// <summary>
    /// リンクを登録
    /// </summary>
    public virtual async Task<Result<string>> UpdateLinks(IUnitOfWork unitOfWork, IEnumerable<Domain.Entities.CaseLink> caseLinks, Domain.Entities.Case currentData)
    {
        var repository = unitOfWork.GetRepository<Domain.Entities.CaseLink>();

        // リクエストされたリンクに存在しないリンクを削除
        if (currentData.CaseLinks?.Any() == true)
        {
            foreach (var currentLink in currentData.CaseLinks)
            {
                var selectedLink = caseLinks.FirstOrDefault(x => x.Id == currentLink.Id);
                if (selectedLink != null) continue;

                // DB削除
                var deleteResult = await repository.DeleteAsync(currentLink).ConfigureAwait(false);
                if (deleteResult.IsError) return deleteResult.PreserveErrorAs<string>();
            }
        }

        foreach (var requestLink in caseLinks)
        {
            // テーブルに存在しないリンク：新規追加
            var currentLink = currentData.CaseLinks?.FirstOrDefault(x => x.Id == requestLink.Id);
            if (currentLink == null)
            {
                var linkEntity = new Domain.Entities.CaseLink()
                {
                    Id = Ulid.NewUlid().ToString(),
                    Title = requestLink.Title,
                    Url = requestLink.Url,
                    CaseId = currentData.Id,
                };

                // DB登録
                var addResult = await repository.AddAsync(linkEntity).ConfigureAwait(false);
                if (addResult.IsError) return addResult.PreserveErrorAs<string>();
            }
            // テーブルに存在するリンクで、タイトルまたはURLが異なるもの：更新
            else if (currentLink.Title != requestLink.Title || currentLink.Url != requestLink.Url)
            {
                currentLink.Title = requestLink.Title;
                currentLink.Url = requestLink.Url;
                currentLink.Version = requestLink.Version;

                // DB更新
                var updateResult = await repository.UpdateAsync(currentLink).ConfigureAwait(false);
                if (updateResult.IsError) return updateResult.PreserveErrorAs<string>();
            }
        }

        return Result.Ok("");
    }

    /// <summary>
    /// リンクを全件削除
    /// </summary>
    public virtual async Task<Result<string>> DeleteAllLinks(IUnitOfWork unitOfWork, Domain.Entities.Case currentData)
    {
        if (currentData.CaseLinks?.Any() != true) return Result.Ok("");

        var repository = unitOfWork.GetRepository<Domain.Entities.CaseLink>();

        foreach (var link in currentData.CaseLinks)
        {
            // DB削除
            var deleteResult = await repository.DeleteAsync(link).ConfigureAwait(false);
            if (deleteResult.IsError) return deleteResult.PreserveErrorAs<string>();
        }

        return Result.Ok("");
    }

    IEnumerable<BusinessMatchingStatus> ICaseUtility.ConvertCaseStatusToMatchingStatus(IEnumerable<string>? statuses)
    {
        var list = new List<BusinessMatchingStatus>();
        if (statuses is null) return list;

        foreach (var status in statuses)
        {
            Status statusValue;
            if (!Enum.TryParse(status, out statusValue))
            {
                list.Add(BusinessMatchingStatus.Undefined);
                continue;
            }

            if (statusValue == Status.BeforeProposal)
                list.Add(BusinessMatchingStatus.BeforeProposal);
            else if (statusValue == Status.UnderProposal)
                list.Add(BusinessMatchingStatus.Proposing);
            else if (statusValue == Status.Finished)
                list.Add(BusinessMatchingStatus.Completed);
            else if (statusValue == Status.CancelBeforeProposal)
                list.Add(BusinessMatchingStatus.WithdrawBeforeProposal);
            else if (statusValue == Status.CancelAfterProposal)
                list.Add(BusinessMatchingStatus.LostCotractAfterProposal);
            else
                list.Add(BusinessMatchingStatus.Undefined);
        }
        return list.Distinct();
    }

    IEnumerable<MAndAMatchingStatus> ICaseUtility.ConvertCaseStatusToMAndAMatchingStatus(IEnumerable<string>? statuses)
    {
        var list = new List<MAndAMatchingStatus>();
        if (statuses is null) return list;

        foreach (var status in statuses)
        {
            if (status == "NULL")
            {
                list.Add(MAndAMatchingStatus.NotEntered);
                continue;
            }
            Status statusValue;
            if (!Enum.TryParse(status, out statusValue))
            {
                // 「コンサル中」はM&Aマッチングでは該当なし
                list.Add(MAndAMatchingStatus.Undefined);
                continue;
            }

            if (statusValue == Status.BeforeProposal) // 提案前
                list.Add(MAndAMatchingStatus.BeforeProposal);
            else if (statusValue == Status.UnderProposal) // 提案中
                list.Add(MAndAMatchingStatus.Proposing);
            else if (statusValue == Status.Accepted) // 応諾済
                list.Add(MAndAMatchingStatus.Acceptance);
            else if (statusValue == Status.Finished) // 完了
                list.Add(MAndAMatchingStatus.Completed);
            else if (statusValue == Status.CancelBeforeProposal) // 提案前取下
                list.Add(MAndAMatchingStatus.WithdrawBeforeProposal);
            else if (statusValue == Status.CancelAfterProposal) // 提案後失注
                list.Add(MAndAMatchingStatus.LostCotractAfterProposal);
            else if (statusValue == Status.CancelWithOurCircumstance) // 謝絶
                list.Add(MAndAMatchingStatus.CancelProposal);
        }

        return list.Distinct();
    }

    IEnumerable<BusinessMatching.BuySellType> ICaseUtility.ConvertCaseCategoryToMatchingBuySellType(IEnumerable<CaseCategory>? caseCategories)
    {
        var list = new List<BusinessMatching.BuySellType>();
        if (caseCategories is null) return list;

        if (caseCategories.Contains(CaseCategory.BusinessMatchingBuy))
            list.Add(BusinessMatching.BuySellType.Buy);
        if (caseCategories.Contains(CaseCategory.BusinessMatchingSell))
            list.Add(BusinessMatching.BuySellType.Sell);

        if (!list.Any())
            list.Add(BusinessMatching.BuySellType.Undefined);

        return list;
    }

    IEnumerable<MAndAMatching.BuySellType> ICaseUtility.ConvertCaseCategoryToMAndAMatchingBuySellType(IEnumerable<CaseCategory>? caseCategories)
    {
        var list = new List<MAndAMatching.BuySellType>();
        if (caseCategories is null) return list;

        if (caseCategories.Contains(CaseCategory.MAndAMatchingBuy))
            list.Add(MAndAMatching.BuySellType.Buy);
        if (caseCategories.Contains(CaseCategory.MAndAMatchingSell))
            list.Add(MAndAMatching.BuySellType.Sell);

        if (!list.Any())
            list.Add(MAndAMatching.BuySellType.Undefined);

        return list;
    }

    IEnumerable<CommonCaseStatus> ICaseUtility.ConvertCaseStatusToCommonCaseStatus(IEnumerable<string>? statuses)
    {
        if (statuses is null) return Enumerable.Empty<CommonCaseStatus>();

        return statuses.Select(status =>
        {
            // FIXME: 全ての案件を共通化したら、コンサル中のステータスを案件ステータスのEnumに含める
            if (status == "UnderConsulting") return CommonCaseStatus.Consulting;
            if (status == "NULL" || !Enum.TryParse(status, out Status statusValue)) return CommonCaseStatus.Undefined;
            return statusValue switch
            {
                Status.BeforeProposal => CommonCaseStatus.BeforeProposal,
                Status.UnderProposal => CommonCaseStatus.UnderProposal,
                Status.Accepted => CommonCaseStatus.Accepted,
                Status.Finished => CommonCaseStatus.Finished,
                Status.CancelBeforeProposal => CommonCaseStatus.CancelBeforeProposal,
                Status.CancelAfterProposal => CommonCaseStatus.CancelAfterProposal,
                Status.CancelWithOurCircumstance => CommonCaseStatus.CancelWithOurCircumstance,
                _ => CommonCaseStatus.Undefined
            };
        });
    }

    CommonCaseStatus ICaseUtility.GetStatusOfCommonCase(string commonCaseStatus)
    {
        if(!Enum.TryParse<CommonCaseStatus>(commonCaseStatus, out var statusValue))
        {
            return CommonCaseStatus.Undefined;
        }
        return statusValue;
    }
}
