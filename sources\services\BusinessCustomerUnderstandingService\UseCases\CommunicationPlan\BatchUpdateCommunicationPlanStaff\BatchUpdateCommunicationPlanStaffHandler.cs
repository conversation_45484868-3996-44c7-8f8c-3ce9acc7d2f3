using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.BatchUpdateCommunicationPlanStaff;

public class BatchUpdateCommunicationPlanStaffHandler : IRequestHandler<BatchUpdateCommunicationPlanStaffCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;

    public BatchUpdateCommunicationPlanStaffHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<string>> Handle(BatchUpdateCommunicationPlanStaffCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 法人IDを取得
        var bizCustomerRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessCustomer>();
        var getBizCustomerResult = await bizCustomerRepository.FindAsync(new FindBusinessCustomersByCustomerIdentificationIdsSpecification(request.CustomerIdentificationIdList)).ConfigureAwait(false);
        if (getBizCustomerResult.IsError) return getBizCustomerResult.PreserveErrorAs<string>();
        var bizCustomerList = getBizCustomerResult.Get();
        var bizCustomerIdList = bizCustomerList.Select(x => x.Id).ToList();

        // 法人IDから事業性理解IDを取得
        var bizUnderstandingRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding>();
        var getBizUnderstandingResult = await bizUnderstandingRepository.FindAsync(new FindBusinessUnderstandingsByBusinessCustomersSpecification(bizCustomerIdList)).ConfigureAwait(false);
        if (getBizUnderstandingResult.IsError) return getBizUnderstandingResult.PreserveErrorAs<string>();
        var bizUnderstandingList = getBizUnderstandingResult.Get();
        var bizUnderstandingIdList = bizUnderstandingList.Select(x=>x.Id).ToList();

        // 当社の方針の担当者を更新
        var ourPolicyRepository = _unitOfWork.GetRepository<Domain.Entities.OurPolicyUnderstanding>();
        var getOurPolicyResult = await ourPolicyRepository.FindAsync(new FindOurPolicyUnderstandingSpecification(request.CurrentStaffId, bizUnderstandingIdList));
        if (getOurPolicyResult.IsError) return getOurPolicyResult.PreserveErrorAs<string>();
        var ourPolicyList = getOurPolicyResult.Get();
        foreach (var ourpolicy in ourPolicyList)
        {
            ourpolicy.StaffId = request.ChangeStaffId;
            ourpolicy.StaffName = request.ChangeStaffName;
            await ourPolicyRepository.UpdateAsync(ourpolicy).ConfigureAwait(false);
        }

        // お客さまの考え方の担当者を更新
        var customerIdeasRepository = _unitOfWork.GetRepository<Domain.Entities.CustomerIdeasUnderstanding>();
        var getCustomerIdeasResult = await customerIdeasRepository.FindAsync(new FindCustomerIdeasUnderstandingSpecification(request.CurrentStaffId, bizUnderstandingIdList));
        if (getCustomerIdeasResult.IsError) return getCustomerIdeasResult.PreserveErrorAs<string>();
        var customerIdeasList = getCustomerIdeasResult.Get();
        foreach (var customerIdeas in customerIdeasList)
        {
            customerIdeas.StaffId = request.ChangeStaffId;
            customerIdeas.StaffName = request.ChangeStaffName;
            await customerIdeasRepository.UpdateAsync(customerIdeas).ConfigureAwait(false);
        }

        // 財務の共有の担当者を更新
        var sharingOfFinanceRepository = _unitOfWork.GetRepository<Domain.Entities.SharingOfFinance>();
        var getSharingOfFinanceResult = await sharingOfFinanceRepository.FindAsync(new FindSharingOfFinanceSpecification(request.CurrentStaffId, bizUnderstandingIdList));
        if (getSharingOfFinanceResult.IsError) return getSharingOfFinanceResult.PreserveErrorAs<string>();
        var sharingOfFinanceList = getSharingOfFinanceResult.Get();
        foreach (var sharingOfFinance in sharingOfFinanceList)
        {
            sharingOfFinance.StaffId = request.ChangeStaffId;
            sharingOfFinance.StaffName = request.ChangeStaffName;
            await sharingOfFinanceRepository.UpdateAsync(sharingOfFinance).ConfigureAwait(false);
        }

        // 課題の仮説協議の担当者を更新
        var hypotheticalDiscussionRepository = _unitOfWork.GetRepository<Domain.Entities.HypotheticalDiscussionOfIssues>();
        var getHypotheticalDiscussionResult = await hypotheticalDiscussionRepository.FindAsync(new FindHypotheticalDiscussionOfIssuesSpecification(request.CurrentStaffId, bizUnderstandingIdList));
        if (getHypotheticalDiscussionResult.IsError) return getHypotheticalDiscussionResult.PreserveErrorAs<string>();
        var hypotheticalDiscussionList = getHypotheticalDiscussionResult.Get();
        foreach (var hypotheticalDiscussion in hypotheticalDiscussionList)
        {
            hypotheticalDiscussion.StaffId = request.ChangeStaffId;
            hypotheticalDiscussion.StaffName = request.ChangeStaffName;
            await hypotheticalDiscussionRepository.UpdateAsync(hypotheticalDiscussion).ConfigureAwait(false);
        }

        // ToDoの担当者を更新
        var toDoRepository = _unitOfWork.GetRepository<Domain.Entities.ToDo>();
        var getToDoResult = await toDoRepository.FindAsync(new FindToDoSpecification(request.CurrentStaffId, bizUnderstandingIdList));
        if (getToDoResult.IsError) return getToDoResult.PreserveErrorAs<string>();
        var toDoList = getToDoResult.Get();
        foreach (var toDo in toDoList)
        {
            toDo.StaffId = request.ChangeStaffId;
            toDo.StaffName = request.ChangeStaffName;
            await toDoRepository.UpdateAsync(toDo).ConfigureAwait(false);
        }

        // 保存
        var result = await _unitOfWork.SaveEntitiesAsync().ConfigureAwait(false);
        if (result.IsError) return result.PreserveErrorAs<string>();

        return await Task.FromResult(Result.Ok(""));
    }
}
