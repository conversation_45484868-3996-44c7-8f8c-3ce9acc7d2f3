using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;

public class CustomerLinkConfiguration : IEntityTypeConfiguration<Domain.Entities.CustomerLink>
{
    public void Configure(EntityTypeBuilder<Domain.Entities.CustomerLink> builder)
    {
        builder.HasOne<BusinessCustomer>()
            .WithMany(a => a.Links)
            .HasForeignKey(b => b.BusinessCustomerId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(entity => entity.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(entity => entity.DisplayName)
            .HasMaxLength(100);

        builder.Property(entity => entity.Url)
            .IsRequired()
            .HasMaxLength(1000);
    }
}
