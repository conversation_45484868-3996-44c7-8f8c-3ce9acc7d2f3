using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class BusinessPartnerIndustryConfiguration : IEntityTypeConfiguration<BusinessPartnerIndustry>
{
    public void Configure(EntityTypeBuilder<BusinessPartnerIndustry> builder)
    {
        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.BusinessPartnerId)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.SubIndustryCode)
            .IsRequired()
            .HasMaxLength(20);
        builder.Property(u => u.DetailIndustryCode)
            .IsRequired()
            .HasMaxLength(20);
        builder.Property(u => u.IndustryCode)
            .IsRequired()
            .HasMaxLength(20);

        builder.HasOne<BusinessPartner>()
            .WithOne(u => u.BusinessPartnerIndustry)
            .HasForeignKey<BusinessPartnerIndustry>(u => u.BusinessPartnerId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}