namespace CustomerProposalService.UseCases.CaseDiscussionReply.GetCaseDiscussionReply;

public record GetCaseDiscussionReplyResult(
    string Id,
    DateTimeOffset RegisteredAt,
    string RegistrantId,
    string RegistrantName,
    IEnumerable<string>? MentionTargetUserIds,
    IEnumerable<string>? MentionTargetTeamIds,
    string Description,
    IEnumerable<Domain.Entities.CaseDiscussionReplyFile>? Files,
    string Version
);
