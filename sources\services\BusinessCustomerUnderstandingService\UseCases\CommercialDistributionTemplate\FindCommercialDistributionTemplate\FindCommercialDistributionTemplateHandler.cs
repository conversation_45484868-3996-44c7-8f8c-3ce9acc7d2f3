using MediatR;
using Nut.Results;
using Shared.Application;
using Shared.Domain;
using BusinessCustomerUnderstandingService.Domain;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistributionTemplate.FindCommercialDistributionTemplate;

public class FindCommercialDistributionTemplateHandler : IRequestHandler<FindCommercialDistributionTemplateQuery, Result<PaginatedResult<FindCommercialDistributionTemplateResult>>>
{
    private readonly IRepository<Domain.Entities.CommercialDistributionTemplate> _repository;

    public FindCommercialDistributionTemplateHandler(IRepositoryFactory repositoryFactory)
    {
        if (repositoryFactory is null) throw new ArgumentNullException(nameof(repositoryFactory));
        _repository = repositoryFactory.GetRepository<Domain.Entities.CommercialDistributionTemplate>();
    }

    public Task<Result<PaginatedResult<FindCommercialDistributionTemplateResult>>> Handle(FindCommercialDistributionTemplateQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));
        var spec = new FindCommercialDistributionTemplateSpecification(request);
        var result = _repository.FindWithPaginationAsync(
            spec,
            request.ExtractSafePagenationOption(sort: new List<Sort>()
            {
                // デフォルトのソート条件
                new Sort() { Target = nameof(Domain.Entities.CommercialDistributionTemplate.Id) },
            }));

        return result.Map(pr =>
            pr.Map(v => new FindCommercialDistributionTemplateResult(
                    v.Id,
                    v.TemplateName,
                    v.CanvasColor,
                    v.Nodes,
                    v.Edges,
                    v.Version
            )));
    }
}
