using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;
using Shared.Application;
using Shared.Linq;
using Shared.Messaging;
using SharedKernel.ExternalApi.MessageContract.CustomerIdentifying;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartner.FindBusinessPartnerContractee;

public class FindBusinessPartnerContracteeHandler : IRequestHandler<FindBusinessPartnerContracteeQuery, Result<PaginatedResult<FindBusinessPartnerContracteeResult>>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageSender<FindCustomerByIdsQuery, IEnumerable<FindCustomerByIdsResult>> _findCustomerByIdsSender;

    public FindBusinessPartnerContracteeHandler(
        IUnitOfWork unitOfWork,
        IMessageSender<FindCustomerByIdsQuery, IEnumerable<FindCustomerByIdsResult>> findCustomerByIdsSender
        )
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _findCustomerByIdsSender = findCustomerByIdsSender ?? throw new ArgumentNullException(nameof(findCustomerByIdsSender));
    }

    public async Task<Result<PaginatedResult<FindBusinessPartnerContracteeResult>>> Handle(FindBusinessPartnerContracteeQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 事業性理解に紐づくビジネスパートナーを全件取得
        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding>();
        var spec = new FindBusinessPartnerContracteeSpecification(request.customerIdentificationId);

        var getBusinessUnderstandingsResult = await repository.FindAsync(spec).ConfigureAwait(false);
        if (getBusinessUnderstandingsResult.IsError) return getBusinessUnderstandingsResult.PreserveErrorAs<PaginatedResult<FindBusinessPartnerContracteeResult>>();
        var targetCustomerIdentificationIds = getBusinessUnderstandingsResult.Get().Select(x => x.BusinessCustomer.CustomerIdentificationId).ToList();

        if (!targetCustomerIdentificationIds.Any())
        {
            // 紐づく顧客が1件も存在しない場合は空を返す
            return new PaginatedResult<FindBusinessPartnerContracteeResult>(Enumerable.Empty<FindBusinessPartnerContracteeResult>(), 0);
        }

        // 顧客検索
        var getCustomersResult = await _findCustomerByIdsSender.SendAsync(new() { Ids = targetCustomerIdentificationIds });
        if (getCustomersResult.IsError) return getCustomersResult.PreserveErrorAs<PaginatedResult<FindBusinessPartnerContracteeResult>>();
        var customers = getCustomersResult.Get()
            .Select(x =>
                new FindBusinessPartnerContracteeResult(
                    CustomerIdentificationId: x.Id,
                    BranchNumber: x.BranchNumber,
                    CifNumber: x.CifNumber,
                    Name: string.IsNullOrEmpty(x.Name) ? null : x.Name,
                    StaffId: x.StaffId,
                    StaffName: x.StaffName
                )
            );

        var option = request.ExtractSafePagenationOption();
        return await customers.SortBy(option.Sort).ToPaginatedAsync(option.PageIndex, option.PageSize); ;
    }
}
