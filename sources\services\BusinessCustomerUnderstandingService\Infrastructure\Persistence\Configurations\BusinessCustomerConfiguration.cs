using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class BusinessCustomerConfiguration : IEntityTypeConfiguration<BusinessCustomer>
{
    public void Configure(EntityTypeBuilder<BusinessCustomer> builder)
    {
        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.CustomerIdentificationId)
            .IsRequired()
            .HasMaxLength(36);

        // 一意キー制約
        builder.HasIndex(u => u.CustomerIdentificationId)
            .IsUnique();

        // 非クラスター化インデックス
        builder.HasIndex(u => u.CustomerIdentificationId)
            .IsUnique(false)
            .IsClustered(false);
    }
}
