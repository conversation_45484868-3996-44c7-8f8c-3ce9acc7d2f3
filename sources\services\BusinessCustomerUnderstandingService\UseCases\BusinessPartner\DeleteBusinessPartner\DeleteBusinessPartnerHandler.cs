using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;
using Shared.Domain;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartner.DeleteBusinessPartner;

public class DeleteBusinessPartnerHandler : IRequestHandler<DeleteBusinessPartnerCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;

    public DeleteBusinessPartnerHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<string>> Handle(DeleteBusinessPartnerCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessPartner, string>();

        // 既存データを取得します。
        var spec = new FindByIdSpecification<Domain.Entities.BusinessPartner, string>(request.Id)
            .Include(x => x.BusinessPartnerIndustry)
            .AsNoTracking();
        var getResult = await repository.SingleAsync(spec).ConfigureAwait(false);

        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentBusinessPartner = getResult.Get();
        currentBusinessPartner.Version = request.Version;

        return await repository
            .DeleteAsync(currentBusinessPartner) // データを更新として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentBusinessPartner.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
