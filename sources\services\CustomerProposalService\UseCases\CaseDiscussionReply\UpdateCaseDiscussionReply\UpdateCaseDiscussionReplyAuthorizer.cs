using Nut.MediatR;
using Shared.Services;

namespace CustomerProposalService.UseCases.CaseDiscussionReply.UpdateCaseDiscussionReply;
public class UpdateCaseDiscussionReplyAuthorizer : IAuthorizer<UpdateCaseDiscussionReplyCommand>
{
    private readonly ICurrentUserService _currentUserService;

    public UpdateCaseDiscussionReplyAuthorizer(ICurrentUserService currentUserService)
    {
        _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
    }

    public async Task<AuthorizationResult> AuthorizeAsync(UpdateCaseDiscussionReplyCommand request, CancellationToken cancellationToken)
    {
        var currentUser = await _currentUserService.GetAsync().ConfigureAwait(false);

        if (currentUser.UserId == request.RegistrantId)
        {
            return AuthorizationResult.Success();
        }

        return AuthorizationResult.Failed("不正なリクエストです。");
    }
}
