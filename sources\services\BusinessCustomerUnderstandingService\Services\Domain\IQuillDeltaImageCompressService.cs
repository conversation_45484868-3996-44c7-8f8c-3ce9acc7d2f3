using Nut.Results;

namespace BusinessCustomerUnderstandingService.Services.Domain;

public interface IQuillDeltaImageCompressService
{
    /// <summary>
    /// 画像サイズを削減したDeltaを返す
    /// </summary>
    /// <param name="delta">オリジナルのDelta</param>
    /// <param name="allowImageWidth">画像許容サイズ(幅)</param>
    /// <param name="allowImageHeight">画像許容サイズ(高さ)</param>
    /// <returns>Imageをサイズ削減後の情報に置き換えたDelta</returns>
    public Result<string> ConvertToImageCompressedDelta(string delta, uint allowImageWidth, uint allowImageHeight);

    /// <summary>
    /// 画像サイズを削減したDeltaを返す
    /// </summary>
    /// <param name="delta">オリジナルのDelta</param>
    /// <returns>Imageをサイズ削減後の情報に置き換えたDelta</returns>
    public Result<string> ConvertToImageCompressedDelta(string delta);
}