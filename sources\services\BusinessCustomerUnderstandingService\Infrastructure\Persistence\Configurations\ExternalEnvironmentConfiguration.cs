using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class ExternalEnvironmentConfiguration : IEntityTypeConfiguration<ExternalEnvironment>
{
    public void Configure(EntityTypeBuilder<ExternalEnvironment> builder)
    {
        builder.HasOne(e => e.ExternalEnvironmentMaster)
            .WithMany()
            .HasForeignKey(e => e.ExternalEnvironmentMasterId);
    }
}
