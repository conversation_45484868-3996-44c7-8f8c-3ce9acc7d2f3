using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Domain.Entities;
using MediatR;
using Nut.Results;
using Shared.Domain;
using Shared.Services;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.UpdateBusinessUnderstandingUpdatedDateTime;

public class UpdateBusinessUnderstandingUpdatedDateTimeHandler : IRequestHandler<UpdateBusinessUnderstandingUpdatedDateTimeCommand, Result>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDateTimeService;

    public UpdateBusinessUnderstandingUpdatedDateTimeHandler(
        IUnitOfWork unitOfWork,
        ICurrentDateTimeService currentDateTimeService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
    }

    public async Task<Result> Handle(UpdateBusinessUnderstandingUpdatedDateTimeCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding, string>();

        // 事業性理解取得
        var bizUnderstandingRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding, string>();
        var getBizUnderstandingResult = await bizUnderstandingRepository.SingleAsync(
            new FindByIdSpecification<Domain.Entities.BusinessUnderstanding, string>(request.BusinessUnderstandingId)
            .Include(c => c.BusinessCustomer)
            ).ConfigureAwait(false);
        if (getBizUnderstandingResult.IsError) return getBizUnderstandingResult.PreserveErrorAs();
        var bizUnderstanding = getBizUnderstandingResult.Get();

        // キューの作成
        var queue = new Domain.Entities.BusinessUnderstandingMaterializedViewQueue()
        {
            Id = Ulid.NewUlid().ToString(),
            BusinessUnderstandingId = bizUnderstanding.Id,
            CustomerIdentificationId = bizUnderstanding.BusinessCustomer.CustomerIdentificationId,
            NumberOfRetries = 0,
            UpdaterId = request.UpdaterId,
            UpdaterName = request.UpdaterName,
            UpdatedDateTime = _currentDateTimeService.NowDateTimeOffset()
        };
        var queueRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingMaterializedViewQueue>();
        await queueRepository.AddAsync(queue);

        return await queueRepository
            .AddAsync(queue) // データを更新として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok())
            .ConfigureAwait(false);
    }
}
