using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Infrastructure.Storage;
using BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussion.GetBusinessUnderstandingDiscussion;
using BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussionFile.GetBUDiscussionFileByDiscussionId;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussionFile.UploadBUDiscussionFile;

public class UploadBUDiscussionFileHandler : IRequestHandler<UploadBUDiscussionFileCommand, Result<Domain.Entities.BusinessUnderstandingDiscussion>>
{
    private readonly IBusinessUnderstandingStorageClientProvider _objectStorageClientProvider;
    private readonly IUnitOfWork _unitOfWork;

    private readonly string _folderName = "discussion";

    public UploadBUDiscussionFileHandler(
        IBusinessUnderstandingStorageClientProvider objectStorageClientProvider,
        IUnitOfWork unitOfWork
        )
    {
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<Domain.Entities.BusinessUnderstandingDiscussion>> Handle(UploadBUDiscussionFileCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var uploadFiles = (request.UploadFiles) ?? new List<Microsoft.AspNetCore.Http.IFormFile>();
        var selectedFilesNames = (request.SelectedFileNames) ?? new List<string>();

        // Blobとの接続を確立
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        // Blobに登録
        foreach (var file in uploadFiles)
        {
            var stream = file.OpenReadStream();
            var addResult = await storageClient.PostAsync($"{_folderName}/{request.DiscussionId}/{file.FileName}", stream).ConfigureAwait(false);
            if (addResult.IsError) return addResult.PreserveErrorAs<Domain.Entities.BusinessUnderstandingDiscussion>();
        }

        // DBから添付ファイル情報を取得
        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingDiscussionFile>();
        var spec = new GetBUDiscussionFileByDiscussionIdSpecification(request.DiscussionId);
        var result = await repository.FindAsync(spec);

        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (result.IsError) return result.PreserveErrorAs<Domain.Entities.BusinessUnderstandingDiscussion>();

        var currentFiles = result.Get();

        // テーブルに存在しないファイル情報を追加
        foreach (var fileName in selectedFilesNames)
        {
            var currentFile = currentFiles.FirstOrDefault(x => x.FileName == fileName);
            if (currentFile != null) continue;

            var newFile = new Domain.Entities.BusinessUnderstandingDiscussionFile()
            {
                Id = Ulid.NewUlid().ToString(),
                FileName = fileName,
                DiscussionId = request.DiscussionId,
                UpdatedDateTime = DateTimeOffset.Now,
                UpdaterId = request.UpdaterId,
                UpdaterName = request.UpdaterName,
            };

            var addResult = await repository.AddAsync(newFile).ConfigureAwait(false);
            if (addResult.IsError) return addResult.PreserveErrorAs<Domain.Entities.BusinessUnderstandingDiscussion>();
        }

        // 選択したファイルに存在しないファイルとファイル情報を削除
        foreach (var file in currentFiles)
        {
            var selectedFileName = selectedFilesNames.FirstOrDefault(x => x == file.FileName);
            if (selectedFileName != null) continue;

            var deleteResult = await repository.DeleteAsync(file).ConfigureAwait(false);
            if (deleteResult.IsError) return deleteResult.PreserveErrorAs<Domain.Entities.BusinessUnderstandingDiscussion>();

            var deleteFileResult = await storageClient.DeleteAsync($"{_folderName}/{request.DiscussionId}/{file.FileName}").ConfigureAwait(false);
            if (deleteFileResult.IsError) return deleteFileResult.PreserveErrorAs<Domain.Entities.BusinessUnderstandingDiscussion>();
        }

        // 保存
        var saveResult = await _unitOfWork.SaveEntitiesAsync().ConfigureAwait(false);
        if (saveResult.IsError) return saveResult.PreserveErrorAs<Domain.Entities.BusinessUnderstandingDiscussion>();

        // 社内協議を添付ファイル含めて取得
        var commentRepo = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingDiscussion>();
        var commentRepoSpec = new GetBusinessUnderstandingDiscussionSpecification(request.DiscussionId);
        var commentResult = await commentRepo.SingleAsync(commentRepoSpec).ConfigureAwait(false);

        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (commentResult.IsError) return commentResult.PreserveErrorAs<Domain.Entities.BusinessUnderstandingDiscussion>();

        return Result.Ok(commentResult.Get());
    }
}
