using BusinessCustomerUnderstandingService.Domain.Enums;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussionReaction.UpdateBUDiscussionReaction;

[WithDefaultBehaviors]
public record UpdateBUDiscussionReactionCommand(
    string Id,
    ReactionType ReactionType,
    DateTimeOffset UpdatedDateTime,
    string Version
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
