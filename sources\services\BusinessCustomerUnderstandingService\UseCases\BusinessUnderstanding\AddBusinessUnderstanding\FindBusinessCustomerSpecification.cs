using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.AddBusinessUnderstanding;

public class FindBusinessCustomerSpecification : BaseSpecification<Domain.Entities.BusinessCustomer>
{
    public FindBusinessCustomerSpecification(Guid customerIdentificationId)
    {
        Query
            .Where(x => x.CustomerIdentificationId == customerIdentificationId)
            .AsNoTracking();
    }
}
