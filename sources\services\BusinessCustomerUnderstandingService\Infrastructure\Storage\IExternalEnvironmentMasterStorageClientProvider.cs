using Azure.Storage.Blobs;
using Microsoft.Extensions.Azure;
using Shared.AzureBlob;
using Shared.ObjectStorage;

namespace BusinessCustomerUnderstandingService.Infrastructure.Storage;

/// <summary>
/// ファイルを利用するためのクライアントを提供するインターフェースです。
/// </summary>
public interface IExternalEnvironmentMasterStorageClientProvider : IObjectStorageClientProvider
{
}

/// <summary>
/// 外部環境マスタ用ストレージクライアントプロバイダーの実装です。
/// </summary>
public class ExternalEnvironmentMasterStorageClientProvider : BlobStorageClientProvider, IExternalEnvironmentMasterStorageClientProvider
{
    /// <summary>
    /// 使用するAzure Blobのクライアント名です。
    /// </summary>
    public const string AzureClientName = "ExternalEnvironmentMasterBlob";

    /// <summary>
    /// ファイルを利用するためのクライアントを初期化します。
    /// </summary>
    /// <param name="clientFactory">Blobクライアントファクトリー</param>
    public ExternalEnvironmentMasterStorageClientProvider(IAzureClientFactory<BlobServiceClient> clientFactory)
        : base(clientFactory, AzureClientName, "external-environment-master")
    {
    }
}
