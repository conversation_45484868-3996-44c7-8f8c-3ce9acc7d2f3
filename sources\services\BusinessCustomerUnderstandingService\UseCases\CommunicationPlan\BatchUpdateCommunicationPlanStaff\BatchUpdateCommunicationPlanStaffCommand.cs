using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.BatchUpdateCommunicationPlanStaff;

[WithDefaultBehaviors]
public record BatchUpdateCommunicationPlanStaffCommand(
    List<Guid> CustomerIdentificationIdList,
    string CurrentStaffId,
    string ChangeStaffId,
    string ChangeStaffName
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
