using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistributionTemplate.AddCommercialDistributionTemplate;

public class AddCommercialDistributionTemplateHandler : IRequestHandler<AddCommercialDistributionTemplateCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;

    public AddCommercialDistributionTemplateHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<string>> Handle(AddCommercialDistributionTemplateCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 追加する値を作成します。
        var newData = new Domain.Entities.CommercialDistributionTemplate()
        {
            // キーの値をUlidで生成します。
            Id = Ulid.NewUlid().ToString(),
            TemplateName = request.TemplateName,
            CanvasColor= request.CanvasColor,
            Nodes = request.Nodes,
            Edges = request.Edges,
        };

        var repository = _unitOfWork.GetRepository<Domain.Entities.CommercialDistributionTemplate>();
        return await repository
            .AddAsync(newData) // データを追加します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(newData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
