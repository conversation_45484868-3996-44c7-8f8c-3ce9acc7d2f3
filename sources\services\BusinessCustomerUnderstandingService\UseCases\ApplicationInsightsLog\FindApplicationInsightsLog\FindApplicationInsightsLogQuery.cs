using MediatR;
using Nut.Results;
using Shared.Application;

namespace BusinessCustomerUnderstandingService.UseCases.ApplicationInsightsLog.FindApplicationInsightsLog;

[WithDefaultBehaviors]
public record FindApplicationInsightsLogQuery : PageQuery, IRequest<Result<FindApplicationInsightsLogResult>>
{
    public string Environment { get; set; } = default!;
    public DateTimeOffset? FromDate { get; set; }
    public DateTimeOffset? ToDate { get; set; }
    public string? UserId { get; set; }
    public string? BranchNumber { get; set; }
}
