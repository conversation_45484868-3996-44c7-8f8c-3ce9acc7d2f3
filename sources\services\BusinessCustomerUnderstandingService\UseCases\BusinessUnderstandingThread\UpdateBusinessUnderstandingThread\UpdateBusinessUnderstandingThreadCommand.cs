using BusinessCustomerUnderstandingService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThread.UpdateBusinessUnderstandingThread;

[WithDefaultBehaviors]
public record UpdateBusinessUnderstandingThreadCommand(
    string Id,
    string Registrant,
    string RegistrantId,
    string Title,
    string Description,
    string BusinessUnderstandingId,
    string CustomerName,
     string? MentionTargetsHtml,
    IEnumerable<string>? MentionTargetUserIds,
    IEnumerable<string>? MentionTargetTeamIds,
    IEnumerable<string>? MentionTargetTeamMemberUserIds,
    DiscussionPurpose Purpose,
    string? Person,
    bool? IsPersonOfPower,
    DateTimeOffset? CorrespondenceDate,
    bool? IsCorporateDepositTheme,
    bool? IsFundSettlementTheme,
    string Version,
    IEnumerable<IFormFile>? UploadFiles,
    IEnumerable<string>? FilesToRemove
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
