using BusinessCustomerUnderstandingService.Domain.Enums;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussionReaction.AddBUDiscussionReaction;

[WithDefaultBehaviors]
public record AddBUDiscussionReactionCommand(
    string CommentId,
    string StaffId,
    string StaffName,
    ReactionType ReactionType,
    DateTimeOffset UpdatedDateTime
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
