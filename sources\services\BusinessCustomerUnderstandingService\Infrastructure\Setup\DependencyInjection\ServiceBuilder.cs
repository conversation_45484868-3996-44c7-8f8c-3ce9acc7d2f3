using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Shared.Services;

namespace BusinessCustomerUnderstandingService.Infrastructure.Setup.DependencyInjection;

public class ServiceBuilder : IServiceBuilder
{
    public ServiceBuilder(IServiceCollection service)
    {
        Service = service ?? throw new ArgumentNullException(nameof(service));
    }

    public IServiceCollection Service { get; }

    public IServiceBuilder AddCurrentUserService<T>()
        where T : class, ICurrentUserService
    {
        Service.TryAddTransient<ICurrentUserService, T>();
        return this;
    }

    public IServiceBuilder AddCurrentDateTimeService<T>()
        where T : class, ICurrentDateTimeService
    {
        Service.TryAddTransient<ICurrentDateTimeService, T>();
        return this;
    }
}
