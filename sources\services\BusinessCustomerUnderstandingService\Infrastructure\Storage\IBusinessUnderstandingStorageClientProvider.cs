using Azure.Storage.Blobs;
using Microsoft.Extensions.Azure;
    using Shared.AzureBlob;
    using Shared.ObjectStorage;

    namespace BusinessCustomerUnderstandingService.Infrastructure.Storage;

    /// <summary>
    /// ファイルを利用するためのクライアントを提供するインターフェースです。
    /// </summary>
    public interface IBusinessUnderstandingStorageClientProvider : IObjectStorageClientProvider
    {
    }

    /// <summary>
    /// 事業性理解用ストレージクライアントプロバイダーの実装です。
    /// </summary>
    public class BusinessUnderstandingStorageClientProvider : BlobStorageClientProvider, IBusinessUnderstandingStorageClientProvider
    {
        /// <summary>
        /// 使用するAzure Blobのクライアント名です。
        /// </summary>
        public const string AzureClientName = "BusinessUnderstandingBlob";

        /// <summary>
        /// ファイルを利用するためのクライアントを初期化します。
        /// </summary>
        /// <param name="clientFactory">Blobクライアントファクトリー</param>
        public BusinessUnderstandingStorageClientProvider(IAzureClientFactory<BlobServiceClient> clientFactory)
            : base(clientFactory, AzureClientName, "business-understanding")
        {
        }
    }
