using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartnershipAndCreativeAccountingDetail;

public class GetBPAndCADetailSpecification : BaseSpecification<Domain.Entities.BusinessPartnershipAndCreativeAccountingDetail>
{
    public GetBPAndCADetailSpecification(string? id, string? businessUnderstandingId)
    {
        Query
            .WhereIfNotEmpty(id, x => x.Id == id)
            .WhereIfNotEmpty(businessUnderstandingId, x => x.BusinessUnderstandingId == businessUnderstandingId)
            .AsNoTracking();

    }
}
