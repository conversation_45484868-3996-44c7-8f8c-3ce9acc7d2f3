using Microsoft.Extensions.DependencyInjection;
using Shared.Services;

namespace BusinessCustomerUnderstandingService.Infrastructure.Setup.DependencyInjection;

public interface IServiceBuilder
{
    IServiceCollection Service { get; }

    public IServiceBuilder AddCurrentUserService<T>()
        where T : class, ICurrentUserService;

    public IServiceBuilder AddCurrentDateTimeService<T>()
        where T : class, ICurrentDateTimeService;
}
