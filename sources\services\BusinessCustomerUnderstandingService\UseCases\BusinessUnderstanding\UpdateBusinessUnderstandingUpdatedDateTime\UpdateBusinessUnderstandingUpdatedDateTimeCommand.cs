using MediatR;
using Nut.MediatR.ServiceLike;
using Nut.Results;
using Shared.UseCase.AsServiceFilters;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.UpdateBusinessUnderstandingUpdatedDateTime;

[AsService("/business-customer-understanding/business-understanding/udpate-updated-datetime", typeof(StripResultFilter))]
[WithDefaultBehaviors]
public record UpdateBusinessUnderstandingUpdatedDateTimeCommand(
    string BusinessUnderstandingId,
    string UpdaterId,
    string UpdaterName
) : IRequest<Result> // エンティティのキーの型を設定します。
{
}
