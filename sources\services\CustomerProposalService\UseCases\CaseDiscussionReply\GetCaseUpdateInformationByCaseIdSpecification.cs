using Microsoft.EntityFrameworkCore;
using Shared.Spec;

namespace CustomerProposalService.UseCases.CaseDiscussionReply;

public class GetCaseUpdateInformationByCaseIdSpecification : BaseSpecification<Domain.Entities.CaseUpdateInformation>
{
    public GetCaseUpdateInformationByCaseIdSpecification(string caseId)
    {
        Query
            .Where(x => x.CaseId == caseId)
            .AsNoTracking();
    }
}
