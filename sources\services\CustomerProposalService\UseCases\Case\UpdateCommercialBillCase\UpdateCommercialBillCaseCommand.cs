using CustomerProposalService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.UpdateCommercialBillCase;

[WithDefaultBehaviors]
public record UpdateCommercialBillCaseCommand(
    // Case Entity
    string Id,
    string CaseName,
    CaseStatus CaseStatus,
    string? CaseOutline,
    DateTimeOffset? ExpiredAt,
    string StaffId,
    string StaffName,
    IEnumerable<IFormFile>? UploadFiles,
    IEnumerable<string>? CaseLinks,
    // CommercialBillCase Entity
    decimal? Amount,
    string? ApplicableBaseInterestRate,
    decimal? InterestRate,
    string? InterestRateCustom,
    decimal? BaseInterestRate,
    decimal? Spread,
    string? LoanPurposeCode,
    string? LoanPurposeCustom,
    bool RelatedEsg,
    bool PreConsultationStandard,
    bool IsEarthquakeRelated,
    string? CollateralType,
    string? CollateralOrGuaranteeCustom,
    string? GuaranteeType,
    CancelTypeOfLoan? CancelTypeOfLoan,
    string? CancelReason, TrafficSource? TrafficSource,
    IEnumerable<Domain.Entities.Guarantor>? Guarantors,
    string Version
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
