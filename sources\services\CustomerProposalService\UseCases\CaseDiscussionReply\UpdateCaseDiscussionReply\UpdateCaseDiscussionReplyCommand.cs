using CustomerProposalService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionReply.UpdateCaseDiscussionReply;

[WithDefaultBehaviors]
public record UpdateCaseDiscussionReplyCommand(
    string Id,
    string CaseId,
    Guid CustomerIdentificationId,
    string CustomerName,
    string CustomerStaffId,
    string RegistrantId,
    CaseDiscussionPurpose Purpose,
    string? Person,
    bool? IsPersonOfPower,
    string Description,
    IEnumerable<IFormFile>? UploadFiles,
    IEnumerable<string> FilesToRemove,
    IEnumerable<string>? MentionTargetUserIds,
    IEnumerable<string>? MentionTargetTeamIds,
    IEnumerable<string>? MentionTargetTeamMemberUserIds,
    string Version
) : IRequest<Result<string>>
{
}
