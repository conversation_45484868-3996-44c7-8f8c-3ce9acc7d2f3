using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;

public class BusinessUnderstandingThreadFileConfiguration : IEntityTypeConfiguration<BusinessUnderstandingThreadFile>
{
    public void Configure(EntityTypeBuilder<BusinessUnderstandingThreadFile> builder)
    {
        builder.HasOne<BusinessUnderstandingThread>()
            .WithMany(l => l.Files)
            .HasForeignKey(j => j.ThreadId)
            .HasConstraintName("fk_business_understanding_thread_business_understanding_thread_file_id")
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.FileName)
            .IsRequired()
            .HasMaxLength(300);
    }
}
