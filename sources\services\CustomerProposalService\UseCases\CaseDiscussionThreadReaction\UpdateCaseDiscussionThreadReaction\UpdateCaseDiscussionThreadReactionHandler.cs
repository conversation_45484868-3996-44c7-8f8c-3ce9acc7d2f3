using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionThreadReaction.UpdateCaseDiscussionThreadReaction;

public class UpdateCaseDiscussionThreadReactionHandler : IRequestHandler<UpdateCaseDiscussionThreadReactionCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;

    public UpdateCaseDiscussionThreadReactionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<string>> Handle(UpdateCaseDiscussionThreadReactionCommand request, CancellationToken cancellationToken)
    {
        if (request is null) throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CaseDiscussionThreadReaction, string>();
        var getResult = await repository.GetAsync(request.Id, true);
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();
        currentData.ReactionType = request.ReactionType;
        currentData.UpdatedDateTime = request.UpdatedDateTime;
        currentData.Version = request.Version;

        return await repository
            .UpdateAsync(currentData)
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync())
            .FlatMap(() => Result.Ok(currentData.Id))
            .ConfigureAwait(false);
    }
}
