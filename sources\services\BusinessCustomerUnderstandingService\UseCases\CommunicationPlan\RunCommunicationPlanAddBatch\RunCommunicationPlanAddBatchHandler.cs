using BusinessCustomerUnderstandingService.Configurations;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.RunCommunicationPlanAddBatch;

public class RunCommunicationPlanAddBatchHandler : IRequestHandler<RunCommunicationPlanAddBatchCommand, Result<string>>
{
    private readonly string _url;

    public RunCommunicationPlanAddBatchHandler(ServiceSettings settings)
    {
        _url = settings.ExternalEnvironmentUpdateBatch.BatchUrl;
    }

    public Task<Result<string>> Handle(RunCommunicationPlanAddBatchCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // Function起動。結果を待たない。
        _ = SendAsync(request.FolderName, request.UpdaterId, request.UpdaterId);

        return Task.FromResult(Result.Ok(""));
    }

    // API送信後に結果を待たないためのメソッド（投げっぱなし）
    private async Task SendAsync(string folderName, string updaterId, string updaterName)
    {
        // リクエスト作成
        var parameters = new Dictionary<string, string>()
        {
            { "folderName", folderName },
            { "updaterId", updaterId },
            { "updaterName", updaterName },
        };
        var httpPostRequest = new HttpRequestMessage(HttpMethod.Get, $"{_url}?{await new FormUrlEncodedContent(parameters).ReadAsStringAsync()}");

        // API実行
        using (var client = new HttpClient())
        {
            await client.SendAsync(httpPostRequest).ConfigureAwait(false);
        }
    }
}
