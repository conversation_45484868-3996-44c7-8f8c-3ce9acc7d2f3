using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Services.Queries;
using Microsoft.EntityFrameworkCore;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;

public class GetBusinessUnderstandingSummaryQueryService : IGetBusinessUnderstandingSummaryQueryService
{
    private readonly ApplicationDbContext _dbContext;

    public GetBusinessUnderstandingSummaryQueryService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    public async Task<Result<GetBusinessUnderstandingSummaryQueryResult>> Handle(Guid customerIdentificationId)
    {
        var businessCustomerId = (await _dbContext.BusinessCustomers.AsNoTracking().SingleOrDefaultAsync(x => x.CustomerIdentificationId == customerIdentificationId).ConfigureAwait(false))?.Id;
        if (businessCustomerId is null)
        {
            // 法人顧客存在がしない
            return new GetBusinessUnderstandingSummaryQueryResult() { BusinessCustomerId = null, BusinessUnderstandingId = null };
        }

        var businessUnderstandingId = (await _dbContext.BusinessUnderstandings.AsNoTracking().Select(x => new { x.BusinessCustomerId, x.Id }).SingleOrDefaultAsync(x => x.BusinessCustomerId == businessCustomerId).ConfigureAwait(false))?.Id;
        if (businessUnderstandingId is null)
        {
            // 事業性理解が存在しない
            return new GetBusinessUnderstandingSummaryQueryResult() { BusinessCustomerId = businessCustomerId, BusinessUnderstandingId = null };
        }

        // 事業性理解に紐づく対象のデータを取得
        var businessUnderstandingMaterializedView =
            await _dbContext.BusinessUnderstandingMaterializedViews
                .AsNoTracking()
                .Select(x => new { x.BusinessUnderstandingId, x.TransactionPolicy })
                .SingleOrDefaultAsync(x => x.BusinessUnderstandingId == businessUnderstandingId)
                .ConfigureAwait(false);
        var commercialDistribution =
            await _dbContext.CommercialDistributions
                .AsNoTracking()
                .Select(x => new { x.BusinessUnderstandingId, x.UpdatedDateTime })
                .SingleOrDefaultAsync(x => x.BusinessUnderstandingId == businessUnderstandingId)
                .ConfigureAwait(false);
        var relationLevel =
            await _dbContext.RelationLevels
                .AsNoTracking()
                .Select(x => new { x.BusinessUnderstandingId, x.FinancialsAlreadyShared, x.InterviewDate })
                .SingleOrDefaultAsync(x => x.BusinessUnderstandingId == businessUnderstandingId)
                .ConfigureAwait(false);
        var managementPlan =
            await _dbContext.ManagementPlans
                .AsNoTracking()
                .Select(x => new { x.BusinessUnderstandingId, x.BusinessPlanRating })
                .SingleOrDefaultAsync(x => x.BusinessUnderstandingId == businessUnderstandingId)
                .ConfigureAwait(false);

        return new GetBusinessUnderstandingSummaryQueryResult()
        {
            BusinessCustomerId = businessCustomerId,
            BusinessUnderstandingId = businessUnderstandingId,
            TransactionPolicy = businessUnderstandingMaterializedView?.TransactionPolicy,
            FinancialsAlreadySharedType = relationLevel?.FinancialsAlreadyShared,
            FinanceSharedAt = relationLevel?.InterviewDate,
            BusinessPlanRatingType = managementPlan?.BusinessPlanRating,
            CommercialDistributionUpdatedAt = commercialDistribution?.UpdatedDateTime,
        };
    }
}