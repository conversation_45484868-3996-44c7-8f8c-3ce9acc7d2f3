using System.ComponentModel.DataAnnotations;
using BusinessCustomerUnderstandingService.Domain.Entities;
using BusinessCustomerUnderstandingService.Domain.Enums;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Services.Queries;
using BusinessCustomerUnderstandingService.UseCases.UserBusinessCustomerIdentificationSummary.FindUserBusinessCustomerIdentificationSummary;
using Microsoft.EntityFrameworkCore;
using Nut.Results;
using Shared.Domain;
using Shared.Messaging;
using SharedKernel.ExternalApi.MessageContract.CustomerIdentifying;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;
internal class FindUserBusinessCustomerIdentificationSummaryQueryService : IFindUserBusinessCustomerIdentificationSummaryQueryService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMessageSender<FindCustomerByIdsQuery, IEnumerable<FindCustomerByIdsResult>> _findCustomerByIdsSender;

    public FindUserBusinessCustomerIdentificationSummaryQueryService(
        ApplicationDbContext dbContext,
        IMessageSender<FindCustomerByIdsQuery, IEnumerable<FindCustomerByIdsResult>> findCustomerByIdsSender)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        _findCustomerByIdsSender = findCustomerByIdsSender ?? throw new ArgumentNullException(nameof(findCustomerByIdsSender));
    }

    public async Task<Result<FindUserBusinessCustomerIdentificationSummaryResult>> Handle(
        FindUserBusinessCustomerIdentificationSummaryQuery request)
    {
        // 顧客担当者で絞り込んだ事業性理解IDを絞り込みに使用するため、取得する
        var businessUnderStandingIds = await _dbContext.BusinessUnderstandingMaterializedViews
            .AsNoTracking()
            .Where(x => request.CustomerIdentificationIds.Contains(x.CustomerIdentificationId))
            .Select(x => x.BusinessUnderstandingId)
            .ToListAsync();

        // コミュニケーションプラン検索
        var totalCount = await GetTotalCount(request, businessUnderStandingIds);
        var expireIn30DaysCount = await GetExpireIn30DaysCount(request, businessUnderStandingIds);
        var expiredCount = await GetExpiredCount(request, businessUnderStandingIds);
        var details = await GetDetails(request, businessUnderStandingIds);

        // 顧客検索
        var customerIdentificationIds = new HashSet<Guid>(details.Select(x => x.CustomerIdentificationId));
        var getCustomersResult = await GetCustomers(customerIdentificationIds);
        if (getCustomersResult.IsError) return getCustomersResult.PreserveErrorAs<FindUserBusinessCustomerIdentificationSummaryResult>();
        var customers = getCustomersResult.Get();

        // resultを作成
        var communicationPlanSummaryDetails = CreateCommunicationPlanSummaryDetails(details, customers, request.ItemMax);
        return new FindUserBusinessCustomerIdentificationSummaryResult(
            totalCount,
            expireIn30DaysCount,
            expiredCount,
            communicationPlanSummaryDetails);
    }

    /// <summary>
    /// 顧客情報を取得する
    /// </summary>
    /// <param name="customerIdentificationIds"></param>
    /// <returns></returns>
    private async Task<Result<Dictionary<Guid, FindCustomerByIdsResult>>> GetCustomers(IEnumerable<Guid> customerIdentificationIds)
    {
        var customers = new Dictionary<Guid, FindCustomerByIdsResult>();
        if (!customerIdentificationIds.Any()) return customers;

        foreach (var customerIdsChunk in customerIdentificationIds.Chunk(1000))
        {
            var getCustomersResult = await _findCustomerByIdsSender.SendAsync(
                new()
                {
                    Ids = customerIdsChunk,
                });
            if (getCustomersResult.IsError) return getCustomersResult.PreserveErrorAs<Dictionary<Guid, FindCustomerByIdsResult>>();

            foreach (var customer in getCustomersResult.Get())
            {
                customers[customer.Id] = customer;
            }
        }

        return customers;
    }

    /// <summary>
    /// 当社の方針・施策をご理解いただく 抽出条件
    /// タスク担当者で絞り込みを実施
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private IQueryable<OurPolicyUnderstanding> CreateOurPolicyQueryByStaff(
        FindUserBusinessCustomerIdentificationSummaryQuery request)
        => _dbContext.OurPolicyUnderstandings
            .AsNoTracking()
            .Where(x => x.StaffId == request.StaffId)
            .Where(x => x.Status != Status.Completed);

    /// <summary>
    /// 当社の方針・施策をご理解いただく 抽出条件
    /// 顧客担当者で絞り込みを実施
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private IQueryable<OurPolicyUnderstanding> CreateOurPolicyQueryByCustomerStaff(
        IEnumerable<string> businessUnderstndingIds)
        => _dbContext.OurPolicyUnderstandings
            .AsNoTracking()
            .Where(x => x.Status != Status.Completed)
            .Where(x => businessUnderstndingIds.Contains(x.BusinessUnderstandingId));

    /// <summary>
    /// お客様の考え方を理解する 抽出条件
    /// タスク担当者で絞り込みを実施
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private IQueryable<CustomerIdeasUnderstanding> CreateCustomerIdeasUnderstandingQueryByStaff(
        FindUserBusinessCustomerIdentificationSummaryQuery request)
        => _dbContext.CustomerIdeasUnderstandings
            .AsNoTracking()
            .Where(x => x.StaffId == request.StaffId)
            .Where(x => x.Status != Status.Completed);

    /// <summary>
    /// お客様の考え方を理解する 抽出条件
    /// 顧客担当者で絞り込みを実施
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private IQueryable<CustomerIdeasUnderstanding> CreateCustomerIdeasUnderstandingQueryByCustomerStaff(
        IEnumerable<string> businessUnderstndingIds)
        => _dbContext.CustomerIdeasUnderstandings
            .AsNoTracking()
            .Where(x => x.Status != Status.Completed)
            .Where(x => businessUnderstndingIds.Contains(x.BusinessUnderstandingId));

    /// <summary>
    /// 財務の共有 抽出条件
    /// タスク担当者で絞り込みを実施
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private IQueryable<SharingOfFinance> CreateSharingOfFinanceQueryByStaff(
        FindUserBusinessCustomerIdentificationSummaryQuery request)
        => _dbContext.SharingOfFinances
            .AsNoTracking()
            .Where(x => x.StaffId == request.StaffId)
            .Where(x => x.Status != Status.Completed);

    /// <summary>
    /// 財務の共有 抽出条件
    /// 顧客担当者で絞り込みを実施
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private IQueryable<SharingOfFinance> CreateSharingOfFinanceQueryByCustomerStaff(
        IEnumerable<string> businessUnderstndingIds)
        => _dbContext.SharingOfFinances
            .AsNoTracking()
            .Where(x => x.Status != Status.Completed)
            .Where(x => businessUnderstndingIds.Contains(x.BusinessUnderstandingId));

    /// <summary>
    /// 課題の仮説協議 抽出条件
    /// タスク担当者で絞り込みを実施
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private IQueryable<HypotheticalDiscussionOfIssues> CreateHypotheticalDiscussionOfIssueQueryByStaff(
        FindUserBusinessCustomerIdentificationSummaryQuery request)
        => _dbContext.HypotheticalDiscussionOfIssues
            .AsNoTracking()
            .Where(x => x.StaffId == request.StaffId)
            .Where(x => x.Status != Status.Completed);

    /// <summary>
    /// 課題の仮説協議 抽出条件
    /// 顧客担当者で絞り込みを実施
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private IQueryable<HypotheticalDiscussionOfIssues> CreateHypotheticalDiscussionOfIssueQueryByCustomerStaff(
        IEnumerable<string> businessUnderstndingIds)
        => _dbContext.HypotheticalDiscussionOfIssues
            .AsNoTracking()
            .Where(x => x.Status != Status.Completed)
            .Where(x => businessUnderstndingIds.Contains(x.BusinessUnderstandingId));


    /// <summary>
    /// ToDo 抽出条件
    /// タスク担当者で絞り込みを実施
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private IQueryable<ToDo> CreateToDoCaseQueryByStaff(
        FindUserBusinessCustomerIdentificationSummaryQuery request)
        => _dbContext.ToDo
            .AsNoTracking()
            .Where(x => x.StaffId == request.StaffId)
            .Where(x => x.Status != Status.Completed);

    /// <summary>
    /// ToDo 抽出条件
    /// 顧客担当者で絞り込みを実施
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private IQueryable<ToDo> CreateToDoCaseQueryByCustomerStaff(
        IEnumerable<string> businessUnderstndingIds)
        => _dbContext.ToDo
            .AsNoTracking()
            .Where(x => x.Status != Status.Completed)
            .Where(x => businessUnderstndingIds.Contains(x.BusinessUnderstandingId));

    /// <summary>
    /// TotalCount取得
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private async Task<int> GetTotalCount(
        FindUserBusinessCustomerIdentificationSummaryQuery request, IEnumerable<string> businessUnderstndingIds)
    {
        // コミュニティプランは検索条件を簡潔にするため、クエリをタスク担当者で検索するクエリと顧客担当者で検索するクエリで取得する
        // 当社の方針
        var ourPolicyQueryByStaff = CreateOurPolicyQueryByStaff(request)
            .Select(x => x.Id);
        var ourPolicyQueryByCustomerStaff = CreateOurPolicyQueryByCustomerStaff(businessUnderstndingIds)
            .Select(x => x.Id);
        var ourPolicyQuery = ourPolicyQueryByStaff.Union(ourPolicyQueryByCustomerStaff);
        // お客様の考え方を理解する
        var customerIdeasQueryByStaff = CreateCustomerIdeasUnderstandingQueryByStaff(request)
            .Select(x => x.Id);
        var customerIdeasQueryByCustomerStaff = CreateCustomerIdeasUnderstandingQueryByCustomerStaff(businessUnderstndingIds)
            .Select(x => x.Id);
        var customerIdeasQuery = customerIdeasQueryByStaff.Union(customerIdeasQueryByCustomerStaff);
        // 財務の共有
        var sharingOfFinanceQueryByStaff = CreateSharingOfFinanceQueryByStaff(request)
            .Select(x => x.Id);
        var sharingOfFinanceQueryByCustomerStaff = CreateSharingOfFinanceQueryByCustomerStaff(businessUnderstndingIds)
            .Select(x => x.Id);
        var sharingOfFinanceQuery = sharingOfFinanceQueryByStaff.Union(sharingOfFinanceQueryByCustomerStaff);
        // 課題の仮説協議
        var hypotheticalDiscussionOfIssueQueryByStaff = CreateHypotheticalDiscussionOfIssueQueryByStaff(request)
            .Select(x => x.Id);
        var hypotheticalDiscussionOfIssueQueryByCustomerStaff = CreateHypotheticalDiscussionOfIssueQueryByCustomerStaff(businessUnderstndingIds)
            .Select(x => x.Id);
        var hypotheticalDiscussionOfIssueQuery = hypotheticalDiscussionOfIssueQueryByStaff.Union(hypotheticalDiscussionOfIssueQueryByCustomerStaff);
        // ToDo
        var todoQueryByStaff = CreateToDoCaseQueryByStaff(request)
            .Select(x => x.Id);
        var todoQueryByCustomerStaff = CreateToDoCaseQueryByCustomerStaff(businessUnderstndingIds)
            .Select(x => x.Id);
        var todoQuery = todoQueryByStaff.Union(todoQueryByCustomerStaff);

        return await ourPolicyQuery
            .Concat(customerIdeasQuery)
            .Concat(sharingOfFinanceQuery)
            .Concat(hypotheticalDiscussionOfIssueQuery)
            .Concat(todoQuery)
            .CountAsync();
    }

    /// <summary>
    /// ExpireIn30DaysCount取得
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private async Task<int> GetExpireIn30DaysCount(
        FindUserBusinessCustomerIdentificationSummaryQuery request, IEnumerable<string> businessUnderstndingIds)
    {
        // 29日後の同一日を含むため翌日未満までを期限内とする
        var expired30Days = request.BaseDateJST.AddDays(30);

        // コミュニティプランは検索条件を簡潔にするため、クエリをタスク担当者で検索するクエリと顧客担当者で検索するクエリで取得する
        // 当社の方針
        var ourPolicyQueryByStaff = CreateOurPolicyQueryByStaff(request)
            .Where(x => request.BaseDateJST <= x.ExpiredAt && x.ExpiredAt < expired30Days)
            .Select(x => x.Id);
        var ourPolicyQueryByCustomerStaff = CreateOurPolicyQueryByCustomerStaff(businessUnderstndingIds)
            .Where(x => request.BaseDateJST <= x.ExpiredAt && x.ExpiredAt < expired30Days)
            .Select(x => x.Id);
        var ourPolicyQuery = ourPolicyQueryByStaff.Union(ourPolicyQueryByCustomerStaff);
        // お客様の考え方を理解する
        var customerIdeasQueryByStaff = CreateCustomerIdeasUnderstandingQueryByStaff(request)
            .Where(x => request.BaseDateJST <= x.ExpiredAt && x.ExpiredAt < expired30Days)
            .Select(x => x.Id);
        var customerIdeasQueryByCustomerStaff = CreateCustomerIdeasUnderstandingQueryByCustomerStaff(businessUnderstndingIds)
            .Where(x => request.BaseDateJST <= x.ExpiredAt && x.ExpiredAt < expired30Days)
            .Select(x => x.Id);
        var customerIdeasQuery = customerIdeasQueryByStaff.Union(customerIdeasQueryByCustomerStaff);
        // 財務の共有
        var sharingOfFinanceQueryByStaff = CreateSharingOfFinanceQueryByStaff(request)
            .Where(x => request.BaseDateJST <= x.ExpiredAt && x.ExpiredAt < expired30Days)
            .Select(x => x.Id);
        var sharingOfFinanceQueryByCustomerStaff = CreateSharingOfFinanceQueryByCustomerStaff(businessUnderstndingIds)
            .Where(x => request.BaseDateJST <= x.ExpiredAt && x.ExpiredAt < expired30Days)
            .Select(x => x.Id);
        var sharingOfFinanceQuery = sharingOfFinanceQueryByStaff.Union(sharingOfFinanceQueryByCustomerStaff);
        // 課題の仮説協議
        var hypotheticalDiscussionOfIssueQueryByStaff = CreateHypotheticalDiscussionOfIssueQueryByStaff(request)
            .Where(x => request.BaseDateJST <= x.ExpiredAt && x.ExpiredAt < expired30Days)
            .Select(x => x.Id);
        var hypotheticalDiscussionOfIssueQueryByCustomerStaff = CreateHypotheticalDiscussionOfIssueQueryByCustomerStaff(businessUnderstndingIds)
            .Where(x => request.BaseDateJST <= x.ExpiredAt && x.ExpiredAt < expired30Days)
            .Select(x => x.Id);
        var hypotheticalDiscussionOfIssueQuery = hypotheticalDiscussionOfIssueQueryByStaff.Union(hypotheticalDiscussionOfIssueQueryByCustomerStaff);
        // ToDo
        var todoQueryByStaff = CreateToDoCaseQueryByStaff(request)
            .Where(x => request.BaseDateJST <= x.ExpiredAt && x.ExpiredAt < expired30Days)
            .Select(x => x.Id);
        var todoQueryByCustomerStaff = CreateToDoCaseQueryByCustomerStaff(businessUnderstndingIds)
            .Where(x => request.BaseDateJST <= x.ExpiredAt && x.ExpiredAt < expired30Days)
            .Select(x => x.Id);
        var todoQuery = todoQueryByStaff.Union(todoQueryByCustomerStaff);

        return await ourPolicyQuery
            .Concat(customerIdeasQuery)
            .Concat(sharingOfFinanceQuery)
            .Concat(hypotheticalDiscussionOfIssueQuery)
            .Concat(todoQuery)
            .CountAsync();
    }

    /// <summary>
    /// ExpiredCount取得
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private async Task<int> GetExpiredCount(
        FindUserBusinessCustomerIdentificationSummaryQuery request, IEnumerable<string> businessUnderStandingIds)
    {
        // コミュニティプランは検索条件を簡潔にするため、クエリをタスク担当者で検索するクエリと顧客担当者で検索するクエリで取得する
        // 当社の方針
        var ourPolicyQueryByStaff = CreateOurPolicyQueryByStaff(request)
            .Where(x => x.ExpiredAt < request.BaseDateJST)
            .Select(x => x.Id);
        var ourPolicyQueryByCustomerStaff = CreateOurPolicyQueryByCustomerStaff(businessUnderStandingIds)
            .Where(x => x.ExpiredAt < request.BaseDateJST)
            .Select(x => x.Id);
        var ourPolicyQuery = ourPolicyQueryByStaff.Union(ourPolicyQueryByCustomerStaff);
        // お客様の考え方を理解する
        var customerIdeasQueryByStaff = CreateCustomerIdeasUnderstandingQueryByStaff(request)
            .Where(x => x.ExpiredAt < request.BaseDateJST)
            .Select(x => x.Id);
        var customerIdeasQueryByCustomerStaff = CreateCustomerIdeasUnderstandingQueryByCustomerStaff(businessUnderStandingIds)
            .Where(x => x.ExpiredAt < request.BaseDateJST)
            .Select(x => x.Id);
        var customerIdeasQuery = customerIdeasQueryByStaff.Union(customerIdeasQueryByCustomerStaff);
        // 財務の共有
        var sharingOfFinanceQueryByStaff = CreateSharingOfFinanceQueryByStaff(request)
            .Where(x => x.ExpiredAt < request.BaseDateJST)
            .Select(x => x.Id);
        var sharingOfFinanceQueryByCustomerStaff = CreateSharingOfFinanceQueryByCustomerStaff(businessUnderStandingIds)
            .Where(x => x.ExpiredAt < request.BaseDateJST)
            .Select(x => x.Id);
        var sharingOfFinanceQuery = sharingOfFinanceQueryByStaff.Union(sharingOfFinanceQueryByCustomerStaff);
        // 課題の仮説協議
        var hypotheticalDiscussionOfIssueQueryByStaff = CreateHypotheticalDiscussionOfIssueQueryByStaff(request)
            .Where(x => x.ExpiredAt < request.BaseDateJST)
            .Select(x => x.Id);
        var hypotheticalDiscussionOfIssueQueryByCustomerStaff = CreateHypotheticalDiscussionOfIssueQueryByCustomerStaff(businessUnderStandingIds)
            .Where(x => x.ExpiredAt < request.BaseDateJST)
            .Select(x => x.Id);
        var hypotheticalDiscussionOfIssueQuery = hypotheticalDiscussionOfIssueQueryByStaff.Union(hypotheticalDiscussionOfIssueQueryByCustomerStaff);
        // ToDo
        var todoQueryByStaff = CreateToDoCaseQueryByStaff(request)
            .Where(x => x.ExpiredAt < request.BaseDateJST)
            .Select(x => x.Id);
        var todoQueryByCustomerStaff = CreateToDoCaseQueryByCustomerStaff(businessUnderStandingIds)
            .Where(x => x.ExpiredAt < request.BaseDateJST)
            .Select(x => x.Id);
        var todoQuery = todoQueryByStaff.Union(todoQueryByCustomerStaff);

        return await ourPolicyQuery
            .Concat(customerIdeasQuery)
            .Concat(sharingOfFinanceQuery)
            .Concat(hypotheticalDiscussionOfIssueQuery)
            .Concat(todoQuery)
            .CountAsync();
    }

    /// <summary>
    /// データ取得
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private async Task<IEnumerable<CommunicationPlanResult>> GetDetails(
        FindUserBusinessCustomerIdentificationSummaryQuery request, IEnumerable<string> businessUnderStandingIds)
    {
        // コミュニティプランは検索条件を簡潔にするため、クエリをタスク担当者で検索するクエリと顧客担当者で検索するクエリで取得する
        // 当社の方針
        var ourPoliciesByStaff = CreateOurPolicyQueryByStaff(request)
            .Select(x => new
            {
                x.BusinessUnderstandingId,
                x.Id,
                CommunicationPlanType = CommunicationPlanType.OurPolicyUnderstanding,
                x.Title,
                x.ExpiredAt
            });
        var ourPoliciesByCustomerStaff = CreateOurPolicyQueryByCustomerStaff(businessUnderStandingIds)
            .Select(x => new
            {
                x.BusinessUnderstandingId,
                x.Id,
                CommunicationPlanType = CommunicationPlanType.OurPolicyUnderstanding,
                x.Title,
                x.ExpiredAt
            });
        var ourPolicies = ourPoliciesByStaff.Union(ourPoliciesByCustomerStaff);

        // お客様の考え方を理解する
        var customerIdeasByStaff = CreateCustomerIdeasUnderstandingQueryByStaff(request)
            .Select(x => new
            {
                x.BusinessUnderstandingId,
                x.Id,
                CommunicationPlanType = CommunicationPlanType.CustomerIdeasUnderstanding,
                x.Title,
                x.ExpiredAt
            });

        var customerIdeasByCustomerStaff = CreateCustomerIdeasUnderstandingQueryByCustomerStaff(businessUnderStandingIds)
            .Select(x => new
            {
                x.BusinessUnderstandingId,
                x.Id,
                CommunicationPlanType = CommunicationPlanType.CustomerIdeasUnderstanding,
                x.Title,
                x.ExpiredAt
            });

        var customerIdeas = customerIdeasByStaff.Union(customerIdeasByCustomerStaff);

        // 財務の共有
        var sharingOfFinanceByStaff = CreateSharingOfFinanceQueryByStaff(request)
            .Select(x => new
            {
                x.BusinessUnderstandingId,
                x.Id,
                CommunicationPlanType = CommunicationPlanType.SharingOfFinance,
                x.Title,
                x.ExpiredAt
            });

        var sharingOfFinanceByCustomerStaff = CreateSharingOfFinanceQueryByCustomerStaff(businessUnderStandingIds)
            .Select(x => new
            {
                x.BusinessUnderstandingId,
                x.Id,
                CommunicationPlanType = CommunicationPlanType.SharingOfFinance,
                x.Title,
                x.ExpiredAt
            });
        var sharingOfFinance = sharingOfFinanceByStaff.Union(sharingOfFinanceByCustomerStaff);

        // 課題の仮説協議
        var hypotheticalDiscussionOfIssueByStaff = CreateHypotheticalDiscussionOfIssueQueryByStaff(request)
            .Select(x => new
            {
                x.BusinessUnderstandingId,
                x.Id,
                CommunicationPlanType = CommunicationPlanType.HypotheticalDiscussionOfIssues,
                x.Title,
                x.ExpiredAt
            });

        var hypotheticalDiscussionOfIssueByCustomerStaff = CreateHypotheticalDiscussionOfIssueQueryByCustomerStaff(businessUnderStandingIds)
            .Select(x => new
            {
                x.BusinessUnderstandingId,
                x.Id,
                CommunicationPlanType = CommunicationPlanType.HypotheticalDiscussionOfIssues,
                x.Title,
                x.ExpiredAt
            });
        var hypotheticalDiscussionOfIssues = hypotheticalDiscussionOfIssueByStaff.Union(hypotheticalDiscussionOfIssueByCustomerStaff);

        // ToDo
        var todoByStaff = CreateToDoCaseQueryByStaff(request)
            .Select(x => new
            {
                x.BusinessUnderstandingId,
                x.Id,
                CommunicationPlanType = CommunicationPlanType.ToDo,
                x.Title,
                x.ExpiredAt
            });
        var todoByCustomerStaff = CreateToDoCaseQueryByCustomerStaff(businessUnderStandingIds)
            .Select(x => new
            {
                x.BusinessUnderstandingId,
                x.Id,
                CommunicationPlanType = CommunicationPlanType.ToDo,
                x.Title,
                x.ExpiredAt
            });
        var todos = todoByStaff.Union(todoByCustomerStaff);

        // 課題の仮説協議だけSQLへのUNIONに変換できないので別で処理する
        var communicationPlans = await ourPolicies
            .Concat(customerIdeas!)
            .Concat(sharingOfFinance!)
            .Concat(todos!).ToListAsync();

        communicationPlans = communicationPlans.Concat((await hypotheticalDiscussionOfIssues.ToListAsync())!).ToList();

        // コミュニケーションプランの事業性理解の取得
        var businessUnderstandingIds = communicationPlans.Select(x => x.BusinessUnderstandingId).ToHashSet();

        var businessCustomers = new Dictionary<string, Guid>();
        foreach (var chunk in businessUnderstandingIds.Chunk(1000))
        {
            var list = await _dbContext.BusinessUnderstandings
                .AsNoTracking()
                .Where(x => chunk.Contains(x.Id))
                .Include(x => x.BusinessCustomer)
                .Select(x => new
                {
                    x.Id,
                    x.BusinessCustomer.CustomerIdentificationId
                }).ToListAsync();

            foreach (var item in list)
            {
                businessCustomers.Add(item.Id, item.CustomerIdentificationId);
            }
        }

        // コミュニケーションプランに顧客識別IDを付加し返却
        return communicationPlans
            .Select(x => new CommunicationPlanResult(
                x.BusinessUnderstandingId,
                x.Id,
                x.CommunicationPlanType,
                x.Title,
                x.ExpiredAt)
            {
                CustomerIdentificationId = businessCustomers[x.BusinessUnderstandingId],
            }).ToList();
    }

    private IEnumerable<CommunicationPlanSummaryDetail> CreateCommunicationPlanSummaryDetails(
        IEnumerable<CommunicationPlanResult> details,
        Dictionary<Guid, FindCustomerByIdsResult> customers,
        int maxSummarySize)
    {
        return details
            .OrderBy(x => x.ExpiredAt, new ExpiredAtComparer())
            .ThenBy(x => x.CustomerIdentificationId)
            .ThenBy(x => x.CommunicationPlanId)
            .Take(maxSummarySize)
            .Select(x => new CommunicationPlanSummaryDetail(
                Id: x.CommunicationPlanId,
                BusinessUnderstandingId: x.BusinessUnderstandingId,
                CustomerName: customers.TryGetValue(x.CustomerIdentificationId, out var customer) ? customer.Name : string.Empty,
                CustomerIdentificationId: x.CustomerIdentificationId,
                CategoryName: x.CommunicationPlanCategory.GetAttrubute<DisplayAttribute>()!.Name!,
                Title: !string.IsNullOrEmpty(x.Title) ? x.Title : string.Empty,
                ExpiredAt: x.ExpiredAt));

    }

    private class ExpiredAtComparer : IComparer<DateTimeOffset?>
    {
        public int Compare(DateTimeOffset? x, DateTimeOffset? y)
        {
            if (x is null && y is null) return 0;
            // 日付が入っていないほうが下にくる
            if (x is null && y is not null) return 1;
            if (x is not null && y is null) return -1;

            return x!.Value.CompareTo(y!.Value);
        }
    }
    // 内部データ格納用record
    private record CommunicationPlanResult(
        string BusinessUnderstandingId,
        string CommunicationPlanId,
        CommunicationPlanType CommunicationPlanCategory,
        string Title,
        DateTimeOffset ExpiredAt)
    {
        public Guid CustomerIdentificationId;
    };
}
