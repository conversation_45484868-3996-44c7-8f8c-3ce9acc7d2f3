using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Services.Queries;
using BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindBusinessUnderstanding;
using Microsoft.EntityFrameworkCore;
using Nut.Results;
using Shared.Linq;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;
internal class FindBusinessUnderstandingQueryService : IFindBusinessUnderstandingQueryService
{
    private readonly ApplicationDbContext _dbContext;

    public FindBusinessUnderstandingQueryService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    async Task<Result<List<FindBusinessUnderstandingResult>>> IFindBusinessUnderstandingQueryService.Handle(FindBusinessUnderstandingQuery request)
    {
        return await _dbContext.BusinessUnderstandingMaterializedViews
            .WhereIf(request.CustomerIdentificationIds is not null && request.CustomerIdentificationIds.Any(), x => request.CustomerIdentificationIds!.Contains(x.CustomerIdentificationId))
            .WhereIf(request.TransactionPolicies is not null && request.TransactionPolicies.Any(), x => request.TransactionPolicies!.Contains(x.TransactionPolicy))
            .WhereIf(request.ApproachTypes is not null && request.ApproachTypes.Any(), x => request.ApproachTypes!.Contains(x.BusinessUnderstandingApproachType!.Value))
            .Select(x => new FindBusinessUnderstandingResult(
                    x.BusinessUnderstandingId,
                    x.CustomerIdentificationId,
                    x.CommunicationPlanCount,
                    x.TransactionPolicy,
                    x.BusinessUnderstandingApproachType,
                    x.CustomerStaffId,
                    x.TransactionPolicyConfirmerId,
                    x.TransactionPolicyConfirmedDateTime,
                    x.UpdatedDateTime,
                    x.UpdaterId,
                    x.UpdaterName
            )).ToListAsync();

        // =======================================================================
        // ToDo: 以下、BusinessUnderstandingMaterializedViews本番適用後に削除すること
        // =======================================================================
        //var query = _dbContext.BusinessUnderstandings
        //    .WhereIf(request.TransactionPolicies is not null && request.TransactionPolicies.Any(), bu => request.TransactionPolicies!.Contains(bu.TransactionPolicy))
        //    .Join(_dbContext.BusinessCustomers
        //        .WhereIf(request.ApproachTypes is not null && request.ApproachTypes.Any(),
        //            bu => (_dbContext.BusinessUnderstandingApproaches
        //                .Where(bua => request.ApproachTypes!.Contains(bua.ApproachType))
        //                .Select(bua => bua.CustomerIdentificationId))
        //            .Contains(bu.CustomerIdentificationId)),
        //        bu => bu.BusinessCustomerId,
        //        bc => bc.Id,
        //        (bu, bc) => new FindBusinessUnderstandingResult (
        //            bc.CustomerIdentificationId,
        //            bu.TransactionPolicy,
        //            bu.CommunicationPlanCount ?? 0,
        //            bu.UpdatedDateTime,
        //            bu.TransactionPolicyConfirmedDateTime
        //    ));
        // return await query.ToListAsync();
    }
}
