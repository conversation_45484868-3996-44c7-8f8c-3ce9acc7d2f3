using MediatR;
using Nut.MediatR.ServiceLike;
using Nut.Results;
using Shared.UseCase.AsServiceFilters;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindCustomerIdentificationIdsByBusinessUnderstandingIds;

[AsService("/business-customer-understanding/find-customerids-by-bizids", typeof(StripResultFilter))]
[WithDefaultBehaviors]
public record FindCustomerIdentificationIdsByBusinessUnderstandingIdsQuery : IRequest<Result<List<FindCustomerIdentificationIdsByBusinessUnderstandingIdsResult>>>
{
    public List<string> BusinessUnderstandingIds { get; init; } = default!;
}
