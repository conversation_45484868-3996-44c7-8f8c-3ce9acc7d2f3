using FluentValidation;

namespace CustomerProposalService.UseCases.CaseDiscussionReply.AddGeneralCaseDiscussionReply;

public class AddGeneralCaseDiscussionReplyValidator : AbstractValidator<AddGeneralCaseDiscussionReplyCommand>
{
    public AddGeneralCaseDiscussionReplyValidator()
    {
        RuleFor(v => v.CaseId).NotEmpty();
        RuleFor(v => v.CaseDiscussionThreadId).NotEmpty();
        RuleFor(v => v.CustomerIdentificationId).NotEmpty();
        RuleFor(v => v.CustomerName).NotEmpty();
        RuleFor(v => v.RegistrantName).NotEmpty();
        RuleFor(v => v.RegistrantId).NotEmpty();
        RuleFor(v => v.Purpose).IsInEnum().NotEqual(Domain.Enums.CaseDiscussionPurpose.Undefined);
        RuleFor(v => v.Person).MaximumLength(20);
        RuleFor(v => v.Description).NotEmpty();
    }
}
