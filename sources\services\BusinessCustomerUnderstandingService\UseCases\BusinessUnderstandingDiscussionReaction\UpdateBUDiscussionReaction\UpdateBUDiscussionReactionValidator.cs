using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussionReaction.UpdateBUDiscussionReaction;

public class UpdateBUDiscussionReactionValidator : AbstractValidator<UpdateBUDiscussionReactionCommand>
{
    public UpdateBUDiscussionReactionValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.ReactionType).NotEmpty();
        RuleFor(v => v.UpdatedDateTime).NotEmpty();
        RuleFor(v => v.Version).NotEmpty();
    }
}
