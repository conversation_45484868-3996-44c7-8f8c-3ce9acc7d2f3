using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessCustomer.GetBusinessCustomer;

public class GetBusinessCustomerHandler : IRequestHandler<GetBusinessCustomerQuery, Result<GetBusinessCustomerResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetBusinessCustomerHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetBusinessCustomerResult>> Handle(GetBusinessCustomerQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessCustomer, string>();
        return repository.GetAsync(request.Id)
            .Map(v => new GetBusinessCustomerResult(
                v.Id,
                v.CustomerIdentificationId,
                v.Version));
    }
}
