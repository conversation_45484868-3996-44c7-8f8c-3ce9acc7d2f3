using BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessUnderstandingByBusinessCustomerId;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessUnderstanding;

[WithDefaultBehaviors]
public record GetBusinessUnderstandingByBusinessCustomerIdQuery(string BusinessCustomerId) : IRequest<Result<GetBusinessUnderstandingByBusinessCustomerIdResult>>;
