using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class RelationLevelHistoryConfiguration : IEntityTypeConfiguration<RelationLevelHistory>
{
    public void Configure(EntityTypeBuilder<RelationLevelHistory> builder)
    {
        builder.HasOne<RelationLevel>()
            .WithMany(l => l.Histories)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .HasMaxLength(26);

        builder.Property(entity => entity.InterviewComments)
            .HasMaxLength(1000);

        builder.Property(entity => entity.RelationshipComments)
            .HasMaxLength(1000);

        builder.Property(entity => entity.DisclosureComments)
            .HasMaxLength(1000);

        builder.Property(entity => entity.RelationLevelComment)
            .HasMaxLength(1000);

        builder.Property(entity => entity.Interviewer)
            .HasMaxLength(100);

        builder.Property(entity => entity.Interviewee)
            .HasMaxLength(100);

        builder.Property(entity => entity.InterviewDetail)
            .HasMaxLength(1000);

        builder.Property(entity => entity.OriginalId)
            .HasMaxLength(26);

    }
}
