using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussionFile.GetBUDiscussionFileByDiscussionId;

public class GetBUDiscussionFileByDiscussionIdSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingDiscussionFile>
{
    public GetBUDiscussionFileByDiscussionIdSpecification(string discussionId)
    {
        Query
            .Where(x => x.DiscussionId == discussionId)
            .AsNoTracking();
    }
}
