using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindCustomerIdentificationIdsByBusinessUnderstandingIds;

public interface IFindCustomerIdentificationIdsByBusinessUnderstandingIdsQueryService
{
    Task<Result<List<FindCustomerIdentificationIdsByBusinessUnderstandingIdsQueryServiceResult>>> Handle(FindCustomerIdentificationIdsByBusinessUnderstandingIdsQuery request);
}

public record FindCustomerIdentificationIdsByBusinessUnderstandingIdsQueryServiceResult(
    string BusinessUnderstandingId,
    string BusinessCustomerId,
    Guid CustomerIdentificationId
);
