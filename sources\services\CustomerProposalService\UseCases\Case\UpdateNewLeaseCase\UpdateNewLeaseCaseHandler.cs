using System.Text.Json;
using CustomerProposalService.Domain;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;
using Shared.Domain;
using Shared.Services;

namespace CustomerProposalService.UseCases.Case.UpdateNewLeaseCase;

public class UpdateNewLeaseCaseHandler : IRequestHandler<UpdateNewLeaseCaseCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDateTimeService;
    private readonly ICaseUtility _caseUtility;

    public UpdateNewLeaseCaseHandler(
        IUnitOfWork unitOfWork,
        ICurrentDateTimeService currentDateTimeService,
        ICaseUtility caseUtility)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
        _caseUtility = caseUtility ?? throw new ArgumentNullException(nameof(caseUtility));
    }

    public async Task<Result<string>> Handle(UpdateNewLeaseCaseCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var caseLinks = new List<Domain.Entities.CaseLink>();
        if (request.CaseLinks?.Any() == true)
        {
            foreach (var caseLink in request.CaseLinks)
            {
                var objectLink = JsonSerializer.Deserialize<Domain.Entities.CaseLink>(caseLink);
                caseLinks.Add(objectLink!);
            }
        }

        // 既存の案件データを取得します。
        var repository = _unitOfWork.GetRepository<Domain.Entities.NewLeaseCase, string>();
        var getResult = await repository.SingleAsync(
            new FindByIdSpecification<Domain.Entities.NewLeaseCase, string>(request.Id)
            .Include(c => c.CaseUpdateInformation)
            .Include(c => c.CaseFiles)
            .Include(c => c.CaseLinks)
            ).ConfigureAwait(false);

        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();
        var now = _currentDateTimeService.NowDateTimeOffset();
        currentData.CaseName = request.CaseName;
        currentData.CaseStatus = request.CaseStatus;
        currentData.CaseOutline = request.CaseOutline?.Replace("\r\n", "\n");
        currentData.ExpiredAt = request.ExpiredAt;
        currentData.StaffId = request.StaffId;
        currentData.StaffName = request.StaffName;
        currentData.CaseUpdatedAt = now;
        currentData.CaseUpdateInformation.LastUpdatedAt = now;
        currentData.LeaseCaseType = request.LeaseCaseType;
        currentData.Amount = request.Amount;
        currentData.LeaseSituations = request.LeaseSituations;
        currentData.LeaseStaffId = request.LeaseStaffId;
        currentData.LeaseStaffName = request.LeaseStaffName;
        currentData.CancelType = request.CancelType;
        currentData.CancelReason = request.CancelReason?.Replace("\r\n", "\n");
        currentData.PropertyCategory = request.PropertyCategory;
        currentData.PropertyStatus = request.PropertyStatus;
        currentData.PropertyContractTypes = request.PropertyContractTypes;
        currentData.ContractScheduledAt = request.ContractScheduledAt;
        currentData.BusinessMeetingNumber = request.BusinessMeetingNumber;
        currentData.QuotationNumber = request.QuotationNumber;
        currentData.LeaseDetails = request.LeaseDetails;
        currentData.PaymentCycle = request.PaymentCycle;
        currentData.ResidualValueSettings = request.ResidualValueSettings;
        currentData.QuotationProcurement = request.QuotationProcurement;
        currentData.ScheduledMonthlyMileage = request.ScheduledMonthlyMileage;
        currentData.InstallationLocation = request.InstallationLocation;
        currentData.InstallationLocationCustom = request.InstallationLocationCustom;
        currentData.QuotationNote = request.QuotationNote?.Replace("\r\n", "\n");
        currentData.ContractDate = request.ContractDate;
        currentData.AcceptanceCertificateDate = request.AcceptanceCertificateDate;
        currentData.CaseInputStatus = request.CaseInputStatus;
        currentData.CaseInputCompletedAt = request.CaseInputCompletedAt;
        currentData.IsConsultationTarget = request.IsConsultationTarget;
        currentData.ConsultationStatus = request.ConsultationStatus;
        currentData.ConsultationCompletedAt = request.ConsultationCompletedAt;
        currentData.QuotationCreateStaffId = request.QuotationCreateStaffId;
        currentData.QuotationCreateStaffName = request.QuotationCreateStaffName;
        currentData.QuotationCreateStatus = request.QuotationCreateStatus;
        currentData.QuotationCreateCompletedAt = request.QuotationCreateCompletedAt;
        currentData.QuotationScrutinizeStaffId = request.QuotationScrutinizeStaffId;
        currentData.QuotationScrutinizeStaffName = request.QuotationScrutinizeStaffName;
        currentData.QuotationScrutinizeStatus = request.QuotationScrutinizeStatus;
        currentData.QuotationScrutinizeCompletedAt = request.QuotationScrutinizeCompletedAt;
        currentData.CustomerProposalResult = request.CustomerProposalResult;
        currentData.CustomerProposalCompletedAt = request.CustomerProposalCompletedAt;
        currentData.IndividualApplicationStaffId = request.IndividualApplicationStaffId;
        currentData.IndividualApplicationStaffName = request.IndividualApplicationStaffName;
        currentData.IndividualApplicationStatus = request.IndividualApplicationStatus;
        currentData.IndividualApplicationCompletedAt = request.IndividualApplicationCompletedAt;
        currentData.IsEarthquakeRelated = request.IsEarthquakeRelated;
        currentData.TrafficSource = request.TrafficSource;

        // ファイルをアップロード
        var fileResult = await _caseUtility.ReplaceFiles(_unitOfWork, request.UploadFiles, currentData);
        if (fileResult.IsError) return fileResult;

        // リンクを登録
        var linkResult = await _caseUtility.ReplaceLinks(_unitOfWork, caseLinks, currentData);
        if (linkResult.IsError) return linkResult;

        // バージョン番号は、同時実行制御を有効にするために引数で受け取ったデータの値で必ず上書きします。
        currentData.Version = request.Version;

        return await repository
            .UpdateAsync(currentData) // データを更新として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
