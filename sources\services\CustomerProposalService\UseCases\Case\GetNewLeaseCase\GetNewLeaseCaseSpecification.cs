using Shared.Spec;

namespace CustomerProposalService.UseCases.Case.GetNewLeaseCase;

public class GetNewLeaseCaseSpecification : BaseSpecification<Domain.Entities.NewLeaseCase>
{
    public GetNewLeaseCaseSpecification(GetNewLeaseCaseQuery request)
    {
        Query
            .Where(x => x.Id == request.Id)
            .Include(x => x.CaseUpdateInformation)
            .Include(x => x.CaseFiles)
            .Include(x => x.CaseLinks)
            .AsNoTracking();
    }
}
