using System.Text;
using BusinessCustomerUnderstandingService.Configurations;
using MediatR;
using Newtonsoft.Json.Linq;
using Nut.Results;
using Shared.Linq;
using Shared.Results.Errors;

namespace BusinessCustomerUnderstandingService.UseCases.ApplicationInsightsLog.FindApplicationInsightsLog;

public class FindApplicationInsightsLogHandler : IRequestHandler<FindApplicationInsightsLogQuery, Result<FindApplicationInsightsLogResult>>
{
    private readonly string _appId;
    private readonly string _apiKey;

    public FindApplicationInsightsLogHandler(ServiceSettings settings)
    {
        _appId = settings.ApplicationInsights.AppId;
        _apiKey = settings.ApplicationInsights.ApiKey;
    }

    public async Task<Result<FindApplicationInsightsLogResult>> Handle(FindApplicationInsightsLogQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var name = $"【{request.Environment}】アクセスログ";
        var fromDate = new DateTime(2000, 1, 1);
        var toDate = new DateTime(9999, 12, 31);
        if (request.FromDate != null)
        {
            var tempFromDate = request.FromDate.Value;
            fromDate = new DateTime(tempFromDate.Year, tempFromDate.Month, tempFromDate.Day, 15, 0, 0);
        }
        if (request.ToDate != null)
        {
            var temptoDate = request.ToDate.Value.AddDays(1);
            toDate = new DateTime(temptoDate.Year, temptoDate.Month, temptoDate.Day, 15, 0, 0);
        }

        // ApplicationInsightsで実行するクエリ        
        var queryBuilder = new StringBuilder();
        queryBuilder.AppendLine($"browserTimings");
        queryBuilder.AppendLine($"| project timestamp, customDimensions, name");
        queryBuilder.AppendLine($"| where name has \"{name}\"");
        queryBuilder.AppendLine($"| where timestamp between(datetime({fromDate})..datetime({toDate}))");
        if(request.UserId!= null)
            queryBuilder.AppendLine($"| where customDimensions['userId'] has \"{request.UserId}\"");
        if (request.BranchNumber != null)
            queryBuilder.AppendLine($"| where customDimensions['branchNumber'] has \"{request.BranchNumber}\"");
        queryBuilder.AppendLine($"| order by timestamp desc");

        // リクエスト作成
        var url = $"https://api.applicationinsights.io/v1/apps/{_appId}/query?query={queryBuilder}";
        var httpGetRequest = new HttpRequestMessage(HttpMethod.Get, url);
        httpGetRequest.Headers.Add("ContentType", "application/json");
        httpGetRequest.Headers.Add("x-api-key", _apiKey);

        // API実行
        var client = new HttpClient();
        var response = await client.SendAsync(httpGetRequest);
        var json = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
        var rows = JObject.Parse(json).SelectToken("tables[0].rows");

        // データなしの場合
        if (rows == null)
            return Result.Error<FindApplicationInsightsLogResult>(new DataNotFoundException());

        // トータル
        var itemsTotal = rows.Count();

        // 取得したデータをマッピング
        var itemList = new List<Item>();
        var timestampIndex = 0;
        var customDimensionsIndex = 1;
        foreach (var row in rows)
        {
            var timestamp = row[timestampIndex];          
            var customDimensions = row[customDimensionsIndex];

            var log = GetLog(customDimensions!, timestamp!);
            itemList.Add(log);
        }

        // ページング処理
        itemList = itemList.SortBy(request.Sort).ToList();
        if (request.PageIndex < 1) request.PageIndex = 1;
        if (request.PageSize < 1) request.PageSize = 1;

        var startIndex = ((request.PageIndex -1) * request.PageSize);
        itemList = itemList.Skip(startIndex).Take(request.PageSize).ToList();

        // 戻値
        var returnList = new FindApplicationInsightsLogResult(itemsTotal, itemList);
        return Result.Ok(returnList);
    }

    private Item GetLog(JToken jToken, JToken timestamp)
    {
        var customDimensionsValue = ((JValue)jToken).Value!.ToString();
        var customJObj = JObject.Parse(customDimensionsValue!);
        var userId = customJObj.SelectToken("userId");
        var userName = customJObj.SelectToken("userName");
        var branchNumber = customJObj.SelectToken("branchNumber");
        var branchName = customJObj.SelectToken("branchName");
        var pageTitle = customJObj.SelectToken("pageTitle");
        var customerId = customJObj.SelectToken("customerId");
        var customerName = customJObj.SelectToken("customerName");

        return new Item(
            Timestamp: DateTimeOffset.Parse(((JValue)timestamp).Value!.ToString()!),
            UserId: GetValueFromJToken(userId),
            UserName: GetValueFromJToken(userName),
            BranchNumber: GetValueFromJToken(branchNumber),
            BranchName: GetValueFromJToken(branchName),
            PageTitle: GetValueFromJToken(pageTitle),
            CustomerId: GetValueFromJToken(customerId),
            CustomerName: GetValueFromJToken(customerName)
            );        
    }

    private string GetValueFromJToken(JToken? jToken)
    {
        if (jToken == null) return string.Empty;
        return ((JValue)jToken).Value!.ToString()!;
    }
}
