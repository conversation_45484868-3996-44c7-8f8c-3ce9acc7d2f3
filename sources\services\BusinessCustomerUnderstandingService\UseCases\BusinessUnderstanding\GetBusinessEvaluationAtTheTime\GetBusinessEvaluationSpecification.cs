using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessEvaluationAtTheTime;

public class GetBusinessEvaluationSpecification : BaseSpecification<Domain.Entities.BusinessUnderstanding>
{
    public GetBusinessEvaluationSpecification(string id)
    {
        Query
            .Where(x => x.Id == id)
            .Include(x => x.ManagementPlan)
            .Include(x => x.Management)
            .Include(x => x.FiveForceFramework)
            .Include(x => x.FiveStepFrameWork)
            .Include(x => x.ESGAndSDGs)
            .Include(x => x.ExternalEnvironment)
            .Include(x => x.BusinessUnderstandingMaterializedView)
            .AsNoTracking();
        }
}
