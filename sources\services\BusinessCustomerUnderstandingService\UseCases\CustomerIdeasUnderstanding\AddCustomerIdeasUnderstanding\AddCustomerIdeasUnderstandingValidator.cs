using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.CustomerIdeasUnderstanding.AddCustomerIdeasUnderstanding;

public class AddCustomerIdeasUnderstandingValidator : AbstractValidator<AddCustomerIdeasUnderstandingCommand>
{
    public AddCustomerIdeasUnderstandingValidator()
    {
        RuleFor(v => v.Title).MaximumLength(64).NotEmpty();
        RuleFor(v => v.StaffId).MaximumLength(100);
        RuleFor(v => v.RegistrantId).NotEmpty();
        RuleFor(v => v.RegistrantName).NotEmpty();
        RuleFor(v => v.TargetPerson).MaximumLength(100);
        RuleFor(v => v.Description).MaximumLength(10000);
        RuleFor(v => v.Note).MaximumLength(10000);
        RuleFor(v => v.ExpiredAt).NotEmpty();
        RuleFor(v => v.BusinessUnderstandingId).NotEmpty();
    }
}
