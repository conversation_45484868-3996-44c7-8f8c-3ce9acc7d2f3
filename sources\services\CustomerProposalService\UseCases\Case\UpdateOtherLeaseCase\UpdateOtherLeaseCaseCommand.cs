using CustomerProposalService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.UpdateOtherLeaseCase;

[WithDefaultBehaviors]
public record UpdateOtherLeaseCaseCommand(
    string Id,
    string CaseName,
    CaseStatus CaseStatus,
    string? CaseOutline,
    DateTimeOffset? ExpiredAt,
    string StaffId,
    string StaffName,
    IEnumerable<IFormFile>? UploadFiles,
    IEnumerable<string>? CaseLinks,
    // OtherLeaseCase Entity
    bool IsEarthquakeRelated,
    string Version
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
