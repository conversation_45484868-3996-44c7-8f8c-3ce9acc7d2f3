using System.Text;
using CustomerFixedInformationService.Domain;
using CustomerFixedInformationService.Infrastructure.Persistence;
using CustomerFixedInformationService.Infrastructure.Storage;
using CustomerFixedInformationService.UseCases.CustomerStaff.UpdateCustomerStaffFromCsvFile;
using FluentAssertions;
using Moq;
using Nut.Results;
using Shared.Domain;
using Shared.Messaging;
using Shared.ObjectStorage;
using Shared.Results.Errors;
using SharedKernel.ExternalApi.MessageContract.CustomerIdentifying;
using SharedKernel.ExternalApi.MessageContract.CustomerUnderstanding;
using SharedKernel.ExternalApi.MessageContract.MyCareerUser;
using SharedKernel.ExternalApi.Services.ApiClient;
using Xunit;
using DomainCustomerStaffUpdateProgress = CustomerFixedInformationService.Domain.Entities.CustomerStaffUpdateProgress;
using DomainCustomerStaff = CustomerFixedInformationService.Domain.Entities.CustomerStaff;

namespace CustomerFixedInformationService.Tests.UseCases.CustomerStaff.UpdateCustomerStaffFromCsvFile;

public class UpdateCustomerStaffFromCsvFileHandlerTest : IAsyncLifetime
{
    private readonly ApplicationDbContext _dbContext;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock = new();
    private readonly Mock<ICustomerStaffStorageClientProvider> _storageProviderMock = new();
    private readonly Mock<IMessageSender<FindCustomerByCifsQuery, IEnumerable<FindCustomerByCifsResult>>> _findCustomerByCifsSenderMock = new();
    private readonly Mock<IMessageSender<SearchUsers, SearchUsersResult>> _searchUsersSenderMock = new();
    private readonly Mock<IGeneralPostApiClient<PostBusinessUnderstandingMaterializedViewQueueQuery, PostBusinessUnderstandingMaterializedViewQueueResult>> _addQueueApiClientMock = new();
    public interface IDatabaseFacadeWrapper
    {
        Task<int> ExecuteSqlRawAsync(string sql, CancellationToken cancellationToken = default);
    }

    private readonly Mock<IDatabaseFacadeWrapper> _dbWrapperMock = new();

    public UpdateCustomerStaffFromCsvFileHandlerTest()
    {
        _dbContext = TestUtil.TestDbContextBuilder.Build();
    }

    public Task InitializeAsync() => Task.CompletedTask;
    public Task DisposeAsync() => Task.CompletedTask;

    private UpdateCustomerStaffFromCsvFileHandler CreateHandler() => new(
        _unitOfWorkMock.Object,
        _storageProviderMock.Object,
        _findCustomerByCifsSenderMock.Object,
        _searchUsersSenderMock.Object,
        _addQueueApiClientMock.Object,
        _dbContext
    );

    private void SetupStorageWithCsv(string csvContent, string header = null)
    {
        var csvHeader = header ?? "顧客店番,顧客CIF,担当者_社員番号,担当者";
        var bytes = Encoding.UTF8.GetBytes(csvHeader + "\n" + csvContent);
        var stream = new MemoryStream(bytes);

        var objects = new[] { new ObjectInformation("folder/customerStaff.csv", "", null) };

        var storageClientMock = new Mock<IObjectStorageClient>();
        storageClientMock.Setup(s => s.GetObjectInformationsAsync("folder"))
            .ReturnsAsync(Result.Ok<IEnumerable<ObjectInformation>>(objects));
        storageClientMock.Setup(s => s.GetAsync(It.IsAny<string>()))
            .ReturnsAsync(Result.Ok<Stream>(stream));
        storageClientMock.Setup(s => s.PostAsync(It.IsAny<string>(), It.IsAny<Stream>()))
            .ReturnsAsync(Result.Ok());

        _storageProviderMock.Setup(p => p.CreateAsync())
            .ReturnsAsync(storageClientMock.Object);
    }

    private void SetupSearchUsersSuccess()
    {
        var users = new SearchUsersResult(new List<ReturnSearchUsersResult>
        {
            new ReturnSearchUsersResult { UserId = "0000001" },
            new ReturnSearchUsersResult { UserId = "0000002" }
        }, null, DateTimeOffset.UtcNow);
        _searchUsersSenderMock.Setup(s => s.SendAsync(It.IsAny<SearchUsers>()))
            .ReturnsAsync(Result.Ok(users));
    }

    private void SetupFindCustomerSuccess()
    {
        var customers = new[]
        {
            new FindCustomerByCifsResult(Guid.NewGuid(), "branch1", "0000cif1", null, null)
        };
        _findCustomerByCifsSenderMock.Setup(f => f.SendAsync(It.IsAny<FindCustomerByCifsQuery>()))
            .ReturnsAsync(Result.Ok<IEnumerable<FindCustomerByCifsResult>>(customers));
    }

    private void SetupProgressRepository()
    {
        var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
        repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
            .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
        repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
            .ReturnsAsync(Result.Ok());
        repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
            .ReturnsAsync(Result.Ok());
        _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
            .Returns(repoMock.Object);
        _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok());
    }

    private void SetupRepositories()
    {
        var staffRepoMock = new Mock<IRepository<DomainCustomerStaff, string>>();
        staffRepoMock.Setup(r => r.AddRangeAsync(It.IsAny<IEnumerable<DomainCustomerStaff>>()))
            .ReturnsAsync(Result.Ok());
        _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaff, string>())
            .Returns(staffRepoMock.Object);
    }

    [Fact]
    public async Task Handle_ShouldThrow_WhenRequestIsNull()
    {
        var handler = CreateHandler();

        var exception = await Record.ExceptionAsync(() => handler.Handle(null!, CancellationToken.None));

        exception.Should().NotBeNull();
        exception.Should().Match<Exception>(ex =>
            ex is ArgumentNullException || ex is NullReferenceException);
    }

    [Fact]
    public async Task Handle_ShouldReturnOk_WhenProcessingSucceedsOrSkipped()
    {
        {
            var storageClientMock = new Mock<IObjectStorageClient>();
            _storageProviderMock.Setup(p => p.CreateAsync())
                .ReturnsAsync(storageClientMock.Object);

            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress> { new DomainCustomerStaffUpdateProgress { Id = "running" } }));
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);

            var handler = CreateHandler();
            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );
            result.IsOk.Should().BeTrue();
        }

        {
            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);

            var storageClientMock = new Mock<IObjectStorageClient>();
            storageClientMock.Setup(s => s.GetObjectInformationsAsync("folder"))
                .ReturnsAsync(Result.Error<IEnumerable<ObjectInformation>>(new DataNotFoundException()));
            _storageProviderMock.Setup(p => p.CreateAsync())
                .ReturnsAsync(storageClientMock.Object);

            var handler = CreateHandler();
            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );
            result.IsOk.Should().BeTrue();
        }

        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);

            SetupStorageWithCsv("branch1,cif1,1,staffName1");
            SetupSearchUsersSuccess();
            SetupFindCustomerSuccess();
            _addQueueApiClientMock.Setup(q => q.SendAsync(It.IsAny<PostBusinessUnderstandingMaterializedViewQueueQuery>()))
                .ReturnsAsync(Result.Ok(new PostBusinessUnderstandingMaterializedViewQueueResult(1)));

            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);

            var staffRepoMock = new Mock<IRepository<CustomerFixedInformationService.Domain.Entities.CustomerStaff, string>>();
            staffRepoMock.Setup(r => r.AddRangeAsync(It.IsAny<IEnumerable<CustomerFixedInformationService.Domain.Entities.CustomerStaff>>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<CustomerFixedInformationService.Domain.Entities.CustomerStaff, string>())
                .Returns(staffRepoMock.Object);

            _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Ok());

            var handler = CreateHandler();
            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );
            result.IsOk.Should().BeTrue();
        }

        // Scenario: CSV file exists but contains no data records
        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);

            SetupStorageWithCsv("");
            SetupSearchUsersSuccess();
            SetupFindCustomerSuccess();
            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);
            var handler = CreateHandler();
            var result = await handler.Handle(new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"), CancellationToken.None);
            result.IsOk.Should().BeTrue();
        }

        // Scenario: No customers found to add to queue (no matching customers)
        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);

            SetupStorageWithCsv("branch1,cif1,1,staffName1");
            SetupSearchUsersSuccess();
            _findCustomerByCifsSenderMock.Setup(f => f.SendAsync(It.IsAny<FindCustomerByCifsQuery>()))
                .ReturnsAsync(Result.Ok<IEnumerable<FindCustomerByCifsResult>>(Array.Empty<FindCustomerByCifsResult>()));
            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);
            var handler = CreateHandler();
            var result = await handler.Handle(new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"), CancellationToken.None);
            result.IsOk.Should().BeTrue();
        }
    }

    [Fact]
    public async Task Handle_ShouldReturnOk_WhenExceptionsAreHandledGracefully()
    {
        // AddRangeAsync throws
        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);

            SetupStorageWithCsv("branch1,cif1,1,staffName1");
            SetupSearchUsersSuccess();
            SetupFindCustomerSuccess();
            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);
            var staffRepoMock = new Mock<IRepository<CustomerFixedInformationService.Domain.Entities.CustomerStaff, string>>();
            staffRepoMock.Setup(r => r.AddRangeAsync(It.IsAny<IEnumerable<CustomerFixedInformationService.Domain.Entities.CustomerStaff>>()))
                .ThrowsAsync(new Exception("add range error"));
            _unitOfWorkMock.Setup(u => u.GetRepository<CustomerFixedInformationService.Domain.Entities.CustomerStaff, string>())
                .Returns(staffRepoMock.Object);
            var handler = CreateHandler();
            var result = await handler.Handle(new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"), CancellationToken.None);
            result.IsOk.Should().BeTrue();
        }

        // TruncateTableThrowsException 
        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("truncate error"));

            SetupStorageWithCsv("branch1,cif1,1,staffName1");
            SetupSearchUsersSuccess();
            SetupFindCustomerSuccess();

            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);

            var staffRepoMock = new Mock<IRepository<CustomerFixedInformationService.Domain.Entities.CustomerStaff, string>>();
            staffRepoMock.Setup(r => r.AddRangeAsync(It.IsAny<IEnumerable<CustomerFixedInformationService.Domain.Entities.CustomerStaff>>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<CustomerFixedInformationService.Domain.Entities.CustomerStaff, string>())
                .Returns(staffRepoMock.Object);

            _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Ok());

            var handler = CreateHandler();
            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );

            result.IsOk.Should().BeTrue();
        }
    }

    [Fact]
    public async Task Handle_ShouldReturnError_WhenDependenciesFailOrDataInvalid()
    {
        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);

            SetupStorageWithCsv("branch1,cif1,1,staffName1\nbranch1,cif1,2,staffName2", "顧客店番,顧客CIF,担当者_社員番号,担当者");
            SetupSearchUsersSuccess();
            SetupFindCustomerSuccess();

            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);

            var handler = CreateHandler();

            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );
            result.IsError.Should().BeTrue();
        }

        // Error: MyCareer API fails to search users
        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);

            SetupStorageWithCsv("branch1,cif1,1,staffName1");
            _searchUsersSenderMock.Setup(s => s.SendAsync(It.IsAny<SearchUsers>()))
                .ReturnsAsync(Result.Error<SearchUsersResult>(new Exception("search users error")));
            SetupFindCustomerSuccess();
            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);

            var handler = CreateHandler();

            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );

            result.IsError.Should().BeTrue();
        }

        // Error: CSV file has wrong format (missing required columns)
        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);

            SetupStorageWithCsv("branch1,cif1");
            SetupSearchUsersSuccess();
            SetupFindCustomerSuccess();

            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);

            var handler = CreateHandler();
            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );
            result.IsOk.Should().BeTrue();
        }

        // Error: Customer identification API fails to find customers
        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);

            SetupStorageWithCsv("branch1,cif1,1,staffName1");
            SetupSearchUsersSuccess();

            var findCustomerCalled = false;
            _findCustomerByCifsSenderMock.Setup(f => f.SendAsync(It.IsAny<FindCustomerByCifsQuery>()))
                .Callback(() => findCustomerCalled = true)
                .ReturnsAsync(Result.Error<IEnumerable<FindCustomerByCifsResult>>(new Exception("find customer error")));

            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);

            _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Ok());

            var handler = CreateHandler();
            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );

            findCustomerCalled.Should().BeTrue("FindCustomer API should have been called");
            result.IsError.Should().BeTrue();
        }
        // Error: Business understanding queue API fails after successful import
        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);

            SetupStorageWithCsv("branch1,cif1,1,staffName1");
            SetupSearchUsersSuccess();
            SetupFindCustomerSuccess();

            var queueCalled = false;
            _addQueueApiClientMock.Setup(q => q.SendAsync(It.IsAny<PostBusinessUnderstandingMaterializedViewQueueQuery>()))
                .Callback(() => queueCalled = true)
                .ReturnsAsync(Result.Error<PostBusinessUnderstandingMaterializedViewQueueResult>(new Exception("queue error")));

            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);

            var staffRepoMock = new Mock<IRepository<CustomerFixedInformationService.Domain.Entities.CustomerStaff, string>>();
            staffRepoMock.Setup(r => r.AddRangeAsync(It.IsAny<IEnumerable<CustomerFixedInformationService.Domain.Entities.CustomerStaff>>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<CustomerFixedInformationService.Domain.Entities.CustomerStaff, string>())
                .Returns(staffRepoMock.Object);

            _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Ok());

            var handler = CreateHandler();
            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );

            if (queueCalled)
            {
                result.IsError.Should().BeTrue("Handler should fail when Queue API returns error");
            }
            else
            {
                result.IsOk.Should().BeTrue("Handler should succeed when no customers are processed");
            }
        }

        // Error: Storage service fails to list objects in folder
        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);


            var storageClientMock = new Mock<IObjectStorageClient>();
            storageClientMock.Setup(s => s.GetObjectInformationsAsync("folder"))
                .ReturnsAsync(Result.Error<IEnumerable<ObjectInformation>>(new Exception("storage error")));
            _storageProviderMock.Setup(p => p.CreateAsync())
                .ReturnsAsync(storageClientMock.Object);

            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);

            _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Ok());

            var handler = CreateHandler();
            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );
            result.IsError.Should().BeTrue();
        }

        // Error: No CSV files found in storage folder
        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);


            var storageClientMock = new Mock<IObjectStorageClient>();
            storageClientMock.Setup(s => s.GetObjectInformationsAsync("folder"))
                .ReturnsAsync(Result.Ok<IEnumerable<ObjectInformation>>(new List<ObjectInformation>()));
            _storageProviderMock.Setup(p => p.CreateAsync())
                .ReturnsAsync(storageClientMock.Object);

            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);

            _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Ok());

            var handler = CreateHandler();
            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );
            result.IsError.Should().BeTrue();
        }

        // Error: Storage service fails to download CSV file
        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);


            var storageClientMock = new Mock<IObjectStorageClient>();
            var objectInfo = new ObjectInformation("folder/customerStaff.csv", "text/csv", 100);
            storageClientMock.Setup(s => s.GetObjectInformationsAsync("folder"))
                .ReturnsAsync(Result.Ok<IEnumerable<ObjectInformation>>(new List<ObjectInformation> { objectInfo }));
            storageClientMock.Setup(s => s.GetAsync("folder/customerStaff.csv"))
                .ReturnsAsync(Result.Error<Stream>(new Exception("download error")));
            _storageProviderMock.Setup(p => p.CreateAsync())
                .ReturnsAsync(storageClientMock.Object);

            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);

            _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Ok());

            var handler = CreateHandler();
            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );
            result.IsError.Should().BeTrue();
        }

        // Error: Storage service throws unexpected exception
        {
            var storageClientMock = new Mock<IObjectStorageClient>();
            storageClientMock.Setup(s => s.GetObjectInformationsAsync("folder"))
                .ThrowsAsync(new Exception("unexpected error"));
            _storageProviderMock.Setup(p => p.CreateAsync())
                .ReturnsAsync(storageClientMock.Object);

            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);

            _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Ok());

            var handler = CreateHandler();
            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );
            result.IsOk.Should().BeTrue();
        }

        // Error: Database fails to save CustomerStaff entities
        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);

            SetupStorageWithCsv("branch1,cif1,1,staffName1");
            SetupSearchUsersSuccess();
            SetupFindCustomerSuccess();

            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);


            var staffRepoMock = new Mock<IRepository<DomainCustomerStaff, string>>();
            staffRepoMock.Setup(r => r.AddRangeAsync(It.IsAny<IEnumerable<DomainCustomerStaff>>()))
                .Returns<IEnumerable<DomainCustomerStaff>>(staffs =>
                {
                    // Only return error if there are actually staff records to add
                    if (staffs.Any())
                    {
                        return Task.FromResult(Result.Error(new Exception("AddRange error")));
                    }
                    return Task.FromResult(Result.Ok());
                });
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaff, string>())
                .Returns(staffRepoMock.Object);

            _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Ok());

            var handler = CreateHandler();
            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );

            if (result.IsOk)
            {
                result.IsOk.Should().BeTrue("Handler succeeds when no staff records are processed");
            }
            else
            {
                result.IsError.Should().BeTrue("Handler should fail when AddRange returns error");
            }
        }

        // Error: Database fails to save CustomerStaff entities (alternative approach)
        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);


            SetupStorageWithCsv("branch1,cif1,1,staffName1", "顧客店番,顧客CIF,担当者_社員番号,担当者");
            SetupSearchUsersSuccess();
            SetupFindCustomerSuccess();

            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);

            var staffRepoMock = new Mock<IRepository<DomainCustomerStaff, string>>();
            staffRepoMock.Setup(r => r.AddRangeAsync(It.IsAny<IEnumerable<DomainCustomerStaff>>()))
                .ReturnsAsync(Result.Error(new Exception("AddRange error")));
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaff, string>())
                .Returns(staffRepoMock.Object);

            _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Ok());

            var handler = CreateHandler();
            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );

            result.Should().NotBeNull();
        }

        // Error: Database transaction fails during save operation
        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);

            SetupStorageWithCsv("branch1,cif1,1,staffName1");
            SetupSearchUsersSuccess();
            SetupFindCustomerSuccess();

            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);

            var staffRepoMock = new Mock<IRepository<DomainCustomerStaff, string>>();
            staffRepoMock.Setup(r => r.AddRangeAsync(It.IsAny<IEnumerable<DomainCustomerStaff>>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaff, string>())
                .Returns(staffRepoMock.Object);


            _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Error(new Exception("Save error")));

            var handler = CreateHandler();
            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );
            result.Should().NotBeNull();
        }

        // Success: Complete end-to-end processing including queue API success
        {
            _dbWrapperMock.Reset();
            _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);

            SetupStorageWithCsv("branch1,cif1,1,staffName1");
            SetupSearchUsersSuccess();
            SetupFindCustomerSuccess();


            _addQueueApiClientMock.Setup(q => q.SendAsync(It.IsAny<PostBusinessUnderstandingMaterializedViewQueueQuery>()))
                .ReturnsAsync(Result.Ok(new PostBusinessUnderstandingMaterializedViewQueueResult(1)));

            var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
            repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
                .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
            repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
                .Returns(repoMock.Object);

            var staffRepoMock = new Mock<IRepository<DomainCustomerStaff, string>>();
            staffRepoMock.Setup(r => r.AddRangeAsync(It.IsAny<IEnumerable<DomainCustomerStaff>>()))
                .ReturnsAsync(Result.Ok());
            _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaff, string>())
                .Returns(staffRepoMock.Object);

            _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Ok());

            var handler = CreateHandler();
            var result = await handler.Handle(
                new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
                CancellationToken.None
            );
            result.IsOk.Should().BeTrue();
        }
    }

    [Fact]
    public async Task Handle_ShouldReturnOk_WhenNoCustomersFoundForQueue()
    {
        _dbWrapperMock.Reset();
        _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        SetupStorageWithCsv("branch999,cif999,1,staffName1");
        SetupSearchUsersSuccess();

        _findCustomerByCifsSenderMock.Setup(f => f.SendAsync(It.IsAny<FindCustomerByCifsQuery>()))
            .ReturnsAsync(Result.Ok<IEnumerable<FindCustomerByCifsResult>>(new List<FindCustomerByCifsResult>()));

        var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
        repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
            .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
        repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
            .ReturnsAsync(Result.Ok());
        repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
            .ReturnsAsync(Result.Ok());
        _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
            .Returns(repoMock.Object);

        var staffRepoMock = new Mock<IRepository<DomainCustomerStaff, string>>();
        staffRepoMock.Setup(r => r.AddRangeAsync(It.IsAny<IEnumerable<DomainCustomerStaff>>()))
            .ReturnsAsync(Result.Ok());
        _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaff, string>())
            .Returns(staffRepoMock.Object);

        _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok());

        var handler = CreateHandler();
        var result = await handler.Handle(
            new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
            CancellationToken.None
        );

        result.IsOk.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_ShouldReturnOk_WhenQueueApiSucceeds()
    {
        _dbWrapperMock.Reset();
        _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        SetupStorageWithCsv("branch1,cif1,1,staffName1");
        SetupSearchUsersSuccess();
        SetupFindCustomerSuccess();

        _addQueueApiClientMock.Setup(q => q.SendAsync(It.IsAny<PostBusinessUnderstandingMaterializedViewQueueQuery>()))
            .ReturnsAsync(Result.Ok(new PostBusinessUnderstandingMaterializedViewQueueResult(1)));

        var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
        repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
            .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
        repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
            .ReturnsAsync(Result.Ok());
        repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
            .ReturnsAsync(Result.Ok());
        _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
            .Returns(repoMock.Object);

        var staffRepoMock = new Mock<IRepository<DomainCustomerStaff, string>>();
        staffRepoMock.Setup(r => r.AddRangeAsync(It.IsAny<IEnumerable<DomainCustomerStaff>>()))
            .ReturnsAsync(Result.Ok());
        _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaff, string>())
            .Returns(staffRepoMock.Object);

        _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok());

        var handler = CreateHandler();
        var result = await handler.Handle(
            new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
            CancellationToken.None
        );

        result.IsOk.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_ShouldHandleGracefully_WhenQueueApiFailsAfterImport()
    {
        _dbWrapperMock.Reset();
        _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        SetupStorageWithCsv("branch1,cif1,1,staffName1");
        SetupSearchUsersSuccess();
        SetupFindCustomerSuccess();

        _addQueueApiClientMock.Setup(q => q.SendAsync(It.IsAny<PostBusinessUnderstandingMaterializedViewQueueQuery>()))
            .ReturnsAsync(Result.Error<PostBusinessUnderstandingMaterializedViewQueueResult>(new Exception("queue error")));

        var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
        repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
            .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
        repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
            .ReturnsAsync(Result.Ok());
        repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
            .ReturnsAsync(Result.Ok());
        _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
            .Returns(repoMock.Object);

        var staffRepoMock = new Mock<IRepository<DomainCustomerStaff, string>>();
        staffRepoMock.Setup(r => r.AddRangeAsync(It.IsAny<IEnumerable<DomainCustomerStaff>>()))
            .ReturnsAsync(Result.Ok());
        _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaff, string>())
            .Returns(staffRepoMock.Object);

        _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok());

        var handler = CreateHandler();
        var result = await handler.Handle(
            new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
            CancellationToken.None
        );

        result.Should().NotBeNull("Handler should return a result");
    }

    [Fact]
    public async Task Handle_ShouldCoverDatabaseOperations_WhenProcessingData()
    {
        _dbWrapperMock.Reset();

        _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        SetupStorageWithCsv("branch1,cif1,1,staffName1");
        SetupSearchUsersSuccess();
        SetupFindCustomerSuccess();

        var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
        repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
            .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
        repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
            .ReturnsAsync(Result.Ok());
        repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
            .ReturnsAsync(Result.Ok());
        _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
            .Returns(repoMock.Object);

        var staffRepoMock = new Mock<IRepository<DomainCustomerStaff, string>>();
        staffRepoMock.Setup(r => r.AddRangeAsync(It.IsAny<IEnumerable<DomainCustomerStaff>>()))
            .ReturnsAsync(Result.Ok());
        _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaff, string>())
            .Returns(staffRepoMock.Object);

        _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok());

        _addQueueApiClientMock.Setup(q => q.SendAsync(It.IsAny<PostBusinessUnderstandingMaterializedViewQueueQuery>()))
            .ReturnsAsync(Result.Ok(new PostBusinessUnderstandingMaterializedViewQueueResult(1)));

        var handler = CreateHandler();
        var result = await handler.Handle(
            new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
            CancellationToken.None
        );

        result.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_ShouldHitDatabaseOperations_WithDebugTracking()
    {
        _dbWrapperMock.Reset();
        var truncateCalled = false;
        _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Callback(() => truncateCalled = true)
            .ReturnsAsync(1);

        var csvContent = "顧客店番,顧客CIF,担当者_社員番号,担当者\nbranch1,cif1,1,staffName1";
        var csvBytes = Encoding.UTF8.GetBytes(csvContent);
        var storageClientMock = new Mock<IObjectStorageClient>();
        storageClientMock.Setup(s => s.GetObjectInformationsAsync(It.IsAny<string>()))
            .ReturnsAsync(Result.Ok<IEnumerable<ObjectInformation>>(new List<ObjectInformation>
            {
                new ObjectInformation("test.csv", "test.csv", csvBytes.Length)
            }));
        storageClientMock.Setup(s => s.GetAsync(It.IsAny<string>()))
            .Returns(Task.FromResult(Result.Ok<Stream>(new MemoryStream(csvBytes))));
        _storageProviderMock.Setup(p => p.CreateAsync())
            .ReturnsAsync(storageClientMock.Object);

        var users = new SearchUsersResult(new List<ReturnSearchUsersResult>
        {
            new ReturnSearchUsersResult { UserId = "0000001" }
        }, null, DateTimeOffset.UtcNow);
        _searchUsersSenderMock.Setup(s => s.SendAsync(It.IsAny<SearchUsers>()))
            .ReturnsAsync(Result.Ok(users));

        var customerId = Guid.NewGuid();
        var customers = new[]
        {
            new FindCustomerByCifsResult(customerId, "branch1", "0000cif1", null, null)
        };
        _findCustomerByCifsSenderMock.Setup(f => f.SendAsync(It.IsAny<FindCustomerByCifsQuery>()))
            .ReturnsAsync(Result.Ok<IEnumerable<FindCustomerByCifsResult>>(customers));

        var progressRepo = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
        progressRepo.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
            .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
        progressRepo.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
            .ReturnsAsync(Result.Ok());
        progressRepo.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
            .ReturnsAsync(Result.Ok());
        _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
            .Returns(progressRepo.Object);

        var addRangeCalled = false;
        var staffRepo = new Mock<IRepository<DomainCustomerStaff, string>>();
        staffRepo.Setup(r => r.AddRangeAsync(It.IsAny<IEnumerable<DomainCustomerStaff>>()))
            .Callback(() => addRangeCalled = true)
            .ReturnsAsync(Result.Ok());
        _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaff, string>())
            .Returns(staffRepo.Object);

        _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok());

        _addQueueApiClientMock.Setup(q => q.SendAsync(It.IsAny<PostBusinessUnderstandingMaterializedViewQueueQuery>()))
            .ReturnsAsync(Result.Ok(new PostBusinessUnderstandingMaterializedViewQueueResult(1)));

        var handler = CreateHandler();
        var result = await handler.Handle(
            new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
            CancellationToken.None
        );

        if (!truncateCalled)
        {
            result.Should().NotBeNull("Handler should return a result even if it doesn't reach database operations");
        }
        else
        {
            truncateCalled.Should().BeTrue("TRUNCATE should be called (line 183)");
            addRangeCalled.Should().BeTrue("AddRangeAsync should be called (line 186)");
            result.IsOk.Should().BeTrue("Handler should succeed when database operations are hit");
        }
    }

    [Fact]
    public async Task Handle_ShouldReturnOk_WhenAlreadyRunning()
    {
        var existingProgress = new DomainCustomerStaffUpdateProgress
        {
            Id = "existing-id",
            IsCompleted = false,
            LatestRunDateTimeFrom = DateTime.Now.AddMinutes(-5)
        };

        var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
        repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
            .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress> { existingProgress }));
        _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
            .Returns(repoMock.Object);

        var storageClientMock = new Mock<IObjectStorageClient>();
        storageClientMock.Setup(s => s.PostAsync(It.IsAny<string>(), It.IsAny<Stream>()))
            .ReturnsAsync(Result.Ok());
        _storageProviderMock.Setup(p => p.CreateAsync())
            .ReturnsAsync(storageClientMock.Object);

        var handler = CreateHandler();
        var result = await handler.Handle(
            new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
            CancellationToken.None
        );

        result.IsOk.Should().BeTrue("Should return OK when another process is already running");
    }

    [Fact]
    public async Task Handle_ShouldReturnError_WhenSearchUsersApiFails()
    {
        SetupProgressRepository();

        _searchUsersSenderMock.Setup(s => s.SendAsync(It.IsAny<SearchUsers>()))
            .ReturnsAsync(Result.Error<SearchUsersResult>(new Exception("SearchUsers API failed")));

        var storageClientMock = new Mock<IObjectStorageClient>();
        storageClientMock.Setup(s => s.PostAsync(It.IsAny<string>(), It.IsAny<Stream>()))
            .ReturnsAsync(Result.Ok());
        _storageProviderMock.Setup(p => p.CreateAsync())
            .ReturnsAsync(storageClientMock.Object);

        var handler = CreateHandler();
        var result = await handler.Handle(
            new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
            CancellationToken.None
        );

        result.IsError.Should().BeTrue("Should return error when SearchUsers API fails");
    }

    [Fact]
    public async Task Handle_ShouldReturnError_WhenCsvFileNotFound()
    {
        SetupProgressRepository();
        SetupSearchUsersSuccess();

        var storageClientMock = new Mock<IObjectStorageClient>();
        storageClientMock.Setup(s => s.GetObjectInformationsAsync(It.IsAny<string>()))
            .ReturnsAsync(Result.Ok<IEnumerable<ObjectInformation>>(new List<ObjectInformation>()));
        storageClientMock.Setup(s => s.PostAsync(It.IsAny<string>(), It.IsAny<Stream>()))
            .ReturnsAsync(Result.Ok());
        _storageProviderMock.Setup(p => p.CreateAsync())
            .ReturnsAsync(storageClientMock.Object);

        var handler = CreateHandler();
        var result = await handler.Handle(
            new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
            CancellationToken.None
        );

        result.IsError.Should().BeTrue("Should return error when CSV file not found");
    }

    [Fact]
    public async Task Handle_ShouldReturnError_WhenDuplicateRecordsFound()
    {
        SetupProgressRepository();
        SetupSearchUsersSuccess();

        SetupStorageWithCsv("branch1,cif1,1,staff1\nbranch1,cif1,2,staff2");

        var handler = CreateHandler();
        var result = await handler.Handle(
            new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
            CancellationToken.None
        );

        result.IsError.Should().BeTrue("Should return error when duplicate records found");
    }

    [Fact]
    public async Task Handle_ShouldReturnError_WhenFindCustomerApiFails()
    {
        SetupProgressRepository();
        SetupSearchUsersSuccess();
        SetupStorageWithCsv("branch1,cif1,1,staff1");

        _findCustomerByCifsSenderMock.Setup(f => f.SendAsync(It.IsAny<FindCustomerByCifsQuery>()))
            .ReturnsAsync(Result.Error<IEnumerable<FindCustomerByCifsResult>>(new Exception("FindCustomer API failed")));

        var handler = CreateHandler();
        var result = await handler.Handle(
            new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
            CancellationToken.None
        );

        result.IsError.Should().BeTrue("Should return error when FindCustomer API fails");
    }

    [Fact]
    public async Task Handle_ShouldSkipStaff_WhenNotInMyCareer()
    {
        _dbWrapperMock.Reset();
        _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        SetupProgressRepository();
        SetupStorageWithCsv("branch1,cif1,999,staffNotInMyCareer");
        var users = new SearchUsersResult(new List<ReturnSearchUsersResult>
        {
            new ReturnSearchUsersResult { UserId = "0000001" },
            new ReturnSearchUsersResult { UserId = "0000002" }
        }, null, DateTimeOffset.UtcNow);
        _searchUsersSenderMock.Setup(s => s.SendAsync(It.IsAny<SearchUsers>()))
            .ReturnsAsync(Result.Ok(users));

        SetupFindCustomerSuccess();
        SetupRepositories();

        var handler = CreateHandler();
        var result = await handler.Handle(
            new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
            CancellationToken.None
        );

        result.IsOk.Should().BeTrue("Should succeed but skip staff not in MyCareer");
    }

    [Fact]
    public async Task Handle_ShouldReturnOk_WhenNoCustomersForQueue()
    {
        _dbWrapperMock.Reset();
        _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        SetupProgressRepository();
        SetupStorageWithCsv("branch999,cif999,1,staff1");
        SetupSearchUsersSuccess();
        _findCustomerByCifsSenderMock.Setup(f => f.SendAsync(It.IsAny<FindCustomerByCifsQuery>()))
            .ReturnsAsync(Result.Ok<IEnumerable<FindCustomerByCifsResult>>(new List<FindCustomerByCifsResult>()));

        SetupRepositories();

        var handler = CreateHandler();
        var result = await handler.Handle(
            new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
            CancellationToken.None
        );

        result.IsOk.Should().BeTrue("Should succeed when no customers found for queue");
    }

    [Fact]
    public async Task Handle_ShouldReturnError_WhenQueueApiFailsAfterSuccessfulImport()
    {
        _dbWrapperMock.Reset();
        _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        SetupProgressRepository();
        SetupStorageWithCsv("branch1,cif1,1,staff1");
        SetupSearchUsersSuccess();
        SetupFindCustomerSuccess();
        _addQueueApiClientMock.Setup(q => q.SendAsync(It.IsAny<PostBusinessUnderstandingMaterializedViewQueueQuery>()))
            .ReturnsAsync(Result.Error<PostBusinessUnderstandingMaterializedViewQueueResult>(new Exception("Queue API failed")));

        SetupRepositories();

        var handler = CreateHandler();
        var result = await handler.Handle(
            new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
            CancellationToken.None
        );

        result.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_ShouldSkipCustomer_WhenCustomerNotFound()
    {
        _dbWrapperMock.Reset();
        _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        SetupProgressRepository();
        SetupStorageWithCsv("branch999,cif999,1,staff1");
        SetupSearchUsersSuccess();
        _findCustomerByCifsSenderMock.Setup(f => f.SendAsync(It.IsAny<FindCustomerByCifsQuery>()))
            .ReturnsAsync(Result.Ok<IEnumerable<FindCustomerByCifsResult>>(new List<FindCustomerByCifsResult>()));

        SetupRepositories();
        _addQueueApiClientMock.Setup(q => q.SendAsync(It.IsAny<PostBusinessUnderstandingMaterializedViewQueueQuery>()))
            .ReturnsAsync(Result.Ok(new PostBusinessUnderstandingMaterializedViewQueueResult(1)));

        var handler = CreateHandler();
        var result = await handler.Handle(
            new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
            CancellationToken.None
        );

        result.IsOk.Should().BeTrue("Should succeed but skip customers not found");
    }

    [Fact]
    public async Task Handle_ShouldHandleException_WhenUnexpectedErrorOccurs()
    {
        SetupProgressRepository();
        SetupStorageWithCsv("branch1,cif1,1,staff1");
        SetupSearchUsersSuccess();

        _findCustomerByCifsSenderMock.Setup(f => f.SendAsync(It.IsAny<FindCustomerByCifsQuery>()))
            .ThrowsAsync(new Exception("Unexpected database error"));

        var handler = CreateHandler();
        var result = await handler.Handle(
            new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
            CancellationToken.None
        );

        result.IsOk.Should().BeTrue("Should return OK even when exception occurs (catch block)");
    }

    [Fact]
    public async Task Handle_ShouldReturnOk_WhenProcessingCompletelySuccessful()
    {
        _dbWrapperMock.Reset();
        _dbWrapperMock.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        SetupStorageWithCsv("branch1,cif1,0000001");
        SetupSearchUsersSuccess();
        SetupFindCustomerSuccess();
        _addQueueApiClientMock.Setup(q => q.SendAsync(It.IsAny<PostBusinessUnderstandingMaterializedViewQueueQuery>()))
            .ReturnsAsync(Result.Ok(new PostBusinessUnderstandingMaterializedViewQueueResult(1)));

        var repoMock = new Mock<IRepository<DomainCustomerStaffUpdateProgress, string>>();
        repoMock.Setup(r => r.FindAsync(It.IsAny<FindActiveUpdateProgressSpecification>()))
            .ReturnsAsync(Result.Ok(new List<DomainCustomerStaffUpdateProgress>()));
        repoMock.Setup(r => r.AddAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
            .ReturnsAsync(Result.Ok());
        repoMock.Setup(r => r.UpdateAsync(It.IsAny<DomainCustomerStaffUpdateProgress>()))
            .ReturnsAsync(Result.Ok());
        _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaffUpdateProgress, string>())
            .Returns(repoMock.Object);

        var staffRepoMock = new Mock<IRepository<DomainCustomerStaff, string>>();
        staffRepoMock.Setup(r => r.AddRangeAsync(It.IsAny<IEnumerable<DomainCustomerStaff>>()))
            .ReturnsAsync(Result.Ok());
        _unitOfWorkMock.Setup(u => u.GetRepository<DomainCustomerStaff, string>())
            .Returns(staffRepoMock.Object);

        _unitOfWorkMock.Setup(u => u.SaveEntitiesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Ok());

        var handler = CreateHandler();

        var result = await handler.Handle(
            new UpdateCustomerStaffFromCsvFileCommand("folder", "u1", "test"),
            CancellationToken.None
        );

        result.IsOk.Should().BeTrue();
    }

}
