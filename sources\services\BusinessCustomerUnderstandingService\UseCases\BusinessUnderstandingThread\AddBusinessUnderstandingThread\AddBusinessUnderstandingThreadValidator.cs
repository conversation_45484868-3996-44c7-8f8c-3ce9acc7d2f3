using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThread.AddBusinessUnderstandingThread;

public class AddBusinessUnderstandingThreadValidator : AbstractValidator<AddBusinessUnderstandingThreadCommand>
{
    public AddBusinessUnderstandingThreadValidator()
    {
        RuleFor(v => v.Title).NotEmpty();
        RuleFor(v => v.Description).NotEmpty();
        RuleFor(v => v.BusinessUnderstandingId).NotEmpty();
        RuleFor(v => v.Purpose).NotEmpty().NotEqual(Domain.Enums.DiscussionPurpose.Undefined);
        RuleFor(v => v.Person).MaximumLength(20);
        When(v => v.Purpose.Equals(Domain.Enums.DiscussionPurpose.Internal), () =>
        {
            RuleFor(v => v.IsCorporateDepositTheme).Null();
            RuleFor(v => v.IsFundSettlementTheme).Null();
        });
        When(v => v.Purpose.Equals(Domain.Enums.DiscussionPurpose.External), () =>
        {
            RuleFor(v => v.IsCorporateDepositTheme).NotNull();
            RuleFor(v => v.IsFundSettlementTheme).NotNull();
        });
    }
}
