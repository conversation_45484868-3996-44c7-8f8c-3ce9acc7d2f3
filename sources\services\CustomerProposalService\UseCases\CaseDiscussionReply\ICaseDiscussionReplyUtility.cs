using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionReply;

public interface ICaseDiscussionReplyUtility
{
    Task<Result<IEnumerable<string>>> SendNotifyAsync<T>(
        T caseDiscussionReply,
        IEnumerable<string>? mentionTargetTeamMemberUserIds,
        string caseId,
        string customerName,
        string? customerStaffId
        ) where T : Domain.Entities.CaseDiscussionReply;
    Task<Result<string>> DeleteAllFiles(Domain.Entities.CaseDiscussionReply caseDiscussionReply);
    Task<Result<string>> UpdateCaseLastUpdatedAt(string caseId, DateTimeOffset updateTime);
    Task<Result<string>> ArrangeReplyContentByUrlWhenSaveAsync(string delta, string replyId);
    Task<Result<string>> GetArrangedDeltaWhenPublish(string delta);
    Task<Result<string>> DeleteAllContentsAsync(string replyId);
}
