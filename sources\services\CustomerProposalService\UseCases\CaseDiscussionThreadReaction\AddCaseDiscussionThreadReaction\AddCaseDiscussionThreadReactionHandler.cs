using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionThreadReaction.AddCaseDiscussionThreadReaction;

public class AddCaseDiscussionThreadReactionHandler : IRequestHandler<AddCaseDiscussionThreadReactionCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;

    public AddCaseDiscussionThreadReactionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<string>> Handle(AddCaseDiscussionThreadReactionCommand request, CancellationToken cancellationToken)
    {
        if (request is null) throw new ArgumentNullException(nameof(request));

        var newData = new Domain.Entities.CaseDiscussionThreadReaction()
        {
            Id = Ulid.NewUlid().ToString(),
            CaseDiscussionThreadId = request.CaseDiscussionThreadId,
            ReactionType = request.ReactionType,
            UpdaterId = request.UpdaterId,
            UpdaterName = request.UpdaterName,
            UpdatedDateTime = request.UpdatedDateTime,
        };

        var repository = _unitOfWork.GetRepository<Domain.Entities.CaseDiscussionThreadReaction>();

        return await repository
            .AddAsync(newData)
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync())
            .FlatMap(() => Result.Ok(newData.Id))
            .ConfigureAwait(false);
    }
}
