using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class BusinessPartnershipAndCreativeAccountingDetailConfiguration : IEntityTypeConfiguration<BusinessPartnershipAndCreativeAccountingDetail>
{
    public void Configure(EntityTypeBuilder<BusinessPartnershipAndCreativeAccountingDetail> builder)
    {
        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.BusinessUnderstandingId)
            .IsRequired()
            .HasMaxLength(26);

        builder.HasOne<BusinessUnderstanding>()
            .WithOne(u => u.BusinessPartnershipAndCreativeAccountingDetail)
            .HasForeignKey<BusinessPartnershipAndCreativeAccountingDetail>(u => u.BusinessUnderstandingId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}