using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class SWOTAnalysisHistoryConfiguration : IEntityTypeConfiguration<SWOTAnalysisHistory>
{
    public void Configure(EntityTypeBuilder<SWOTAnalysisHistory> builder)
    {
        builder.HasOne<SWOTAnalysis>()
            .WithMany(entity => entity.Histories)
            .HasForeignKey(entity => entity.OriginalId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(entity => entity.Id)
                .HasMaxLength(26);

        builder.Property(entity => entity.Strengths)
                .HasMaxLength(1000);

        builder.Property(entity => entity.Weaknesses)
                .HasMaxLength(1000);

        builder.Property(entity => entity.Opportunities)
                .HasMaxLength(1000);

        builder.Property(entity => entity.Threats)
                .HasMaxLength(1000);

        builder.Property(entity => entity.OriginalId)
                .HasMaxLength(26);
    }
}
