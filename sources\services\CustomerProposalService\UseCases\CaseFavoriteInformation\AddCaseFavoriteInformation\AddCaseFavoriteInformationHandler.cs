using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;
using Shared.Services;

namespace CustomerProposalService.UseCases.CaseFavoriteInformation.AddCaseFavoriteInformation;

public class AddCaseFavoriteInformationHandler : IRequestHandler<AddCaseFavoriteInformationCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;

    public AddCaseFavoriteInformationHandler(IUnitOfWork unitOfWork, ICurrentUserService currentUserService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
    }

    public async Task<Result<string>> Handle(AddCaseFavoriteInformationCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var userId = (await _currentUserService.GetAsync()).UserId!;

        // 更新する値を作成します。
        var newData = new Domain.Entities.CaseFavoriteInformation()
        {
            // キーの値をUlidで生成します。
            Id = Ulid.NewUlid().ToString(),
            UserId = userId,
            IsFavorite = true,
            CaseId = request.CaseId,
        };

        var repository = _unitOfWork.GetRepository<Domain.Entities.CaseFavoriteInformation>();
        return await repository
            .AddAsync(newData) // データを追加します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(newData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
