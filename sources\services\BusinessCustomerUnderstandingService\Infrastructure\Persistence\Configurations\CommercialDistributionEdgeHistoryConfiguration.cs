using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class CommercialDistributionEdgeHistoryConfiguration : IEntityTypeConfiguration<CommercialDistributionEdgeHistory>
{
    public void Configure(EntityTypeBuilder<CommercialDistributionEdgeHistory> builder)
    {
        builder.HasOne<CommercialDistributionHistory>()
            .WithMany(c => c.CommercialDistributionEdgeHistories)
            .HasForeignKey(e => e.CommercialDistributionHistoryId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(e => e.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(e => e.CommercialDistributionEdgeId)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(e => e.SourceNode)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(e => e.Source)
            .IsRequired()
            .HasMaxLength(32);

        builder.Property(e => e.TargetNode)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(e => e.Target)
            .IsRequired()
            .HasMaxLength(32);

    }
}
