using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Domain.Enums;
using BusinessCustomerUnderstandingService.Externals.ServiceManagement;
using BusinessCustomerUnderstandingService.Services.Queries;
using BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingSummary.GetBusinessUnderstandingSummary;
using MediatR;
using Nut.Results;
using Shared.Domain;
using Shared.Messaging;
using Shared.Results.Errors;

namespace BusinessCustomerUnderstandingService.BusinessUnderstandingSummary.GetBusinessUnderstandingSummary;

public class GetBusinessUnderstandingSummaryHandler : IRequestHandler<GetBusinessUnderstandingSummaryQuery, Result<GetBusinessUnderstandingSummaryResult>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IGetBusinessUnderstandingSummaryQueryService _getBusinessUnderstandingSummaryQueryService;
    private readonly IGetCommunicationPlanSummaryQueryService _getCommunicationPlanSummaryQueryService;
    private readonly IMessageSender<GetAllowanceByBusinessCustomerId, List<GetAllowanceByBusinessCustomerIdResult>> _getAllowanceByBusinessCustomerIdSender;
    public GetBusinessUnderstandingSummaryHandler(
        IUnitOfWork unitOfWork,
        IGetBusinessUnderstandingSummaryQueryService getBusinessUnderstandingSummaryQueryService,
        IGetCommunicationPlanSummaryQueryService getCommunicationPlanSummaryQueryService,
        IMessageSender<GetAllowanceByBusinessCustomerId, List<GetAllowanceByBusinessCustomerIdResult>> getAllowanceByBusinessCustomerIdSender)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _getBusinessUnderstandingSummaryQueryService = getBusinessUnderstandingSummaryQueryService ?? throw new ArgumentNullException(nameof(getBusinessUnderstandingSummaryQueryService));
        _getCommunicationPlanSummaryQueryService = getCommunicationPlanSummaryQueryService ?? throw new ArgumentNullException(nameof(getCommunicationPlanSummaryQueryService));
        _getAllowanceByBusinessCustomerIdSender = getAllowanceByBusinessCustomerIdSender ?? throw new ArgumentNullException(nameof(getAllowanceByBusinessCustomerIdSender));
    }

    public async Task<Result<GetBusinessUnderstandingSummaryResult>> Handle(GetBusinessUnderstandingSummaryQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 事業性理解に紐づくデータの取得
        var getBusinessUnderstandingSummaryResult = await _getBusinessUnderstandingSummaryQueryService.Handle(request.customerIdentificationId).ConfigureAwait(false);
        if (getBusinessUnderstandingSummaryResult.IsError) return getBusinessUnderstandingSummaryResult.PreserveErrorAs<GetBusinessUnderstandingSummaryResult>();
        var businessUnderstandingSummary = getBusinessUnderstandingSummaryResult.Get();

        // 法人顧客・事業性理解のいずれかが存在しない場合はDataNotFoundExceptionを返す
        if (businessUnderstandingSummary.BusinessCustomerId is null || businessUnderstandingSummary.BusinessUnderstandingId is null) return Result.Error<GetBusinessUnderstandingSummaryResult>(new DataNotFoundException("BusinessCustomer/BusinessUnderstandingが存在しません"));

        var transactionPolicyText = Enum.TryParse<TransactionPolicy>(businessUnderstandingSummary.TransactionPolicy, out var transactionPolicy) ? transactionPolicy.DisplayName() : null;
        var commercialDistributionUpdatedDateOfJst = GetJstDateFromDateTimeOffsetOrNull(businessUnderstandingSummary.CommercialDistributionUpdatedAt);
        var financeSharedDateOfJst = GetJstDateFromDateTimeOffsetOrNull(businessUnderstandingSummary.FinanceSharedAt);

        // コミュニケーションプランの集計、引当区分の取得は並列で実行する
        var getCommunicationPlanSummaryTask = _getCommunicationPlanSummaryQueryService.Handle(businessUnderstandingSummary.BusinessUnderstandingId!);
        var getAllowanceByBusinessCustomerIdTask = _getAllowanceByBusinessCustomerIdSender.SendAsync(new GetAllowanceByBusinessCustomerId() { BusinessCustomerId = businessUnderstandingSummary.BusinessCustomerId });
        await Task.WhenAll(new List<Task> { getCommunicationPlanSummaryTask, getAllowanceByBusinessCustomerIdTask }).ConfigureAwait(false);

        // コミュニケーションプラン集計結果から返却値を作成
        var getCommunicationPlanSummaryResult = await getCommunicationPlanSummaryTask;
        if (getCommunicationPlanSummaryResult.IsError) return getCommunicationPlanSummaryResult.PreserveErrorAs<GetBusinessUnderstandingSummaryResult>();
        var communicationPlanSummaries = getCommunicationPlanSummaryResult.Get();
        // 集計結果に存在しない種類のCommunicationPlanSummaryは空(0件)の状態で作成して返す
        var notContainedSummaries =
            new List<CommunicationPlanSummary>(){
                GenerateEmptyCommunicationPlanSummary(CommunicationPlanType.OurPolicyUnderstanding),
                GenerateEmptyCommunicationPlanSummary(CommunicationPlanType.CustomerIdeasUnderstanding),
                GenerateEmptyCommunicationPlanSummary(CommunicationPlanType.HypotheticalDiscussionOfIssues),
                GenerateEmptyCommunicationPlanSummary(CommunicationPlanType.ToDo)
            }.ExceptBy(communicationPlanSummaries.Select(x => x.CommunicationPlanType), x => x.CommunicationPlanType);

        // 引当区分取得結果から返却値を作成
        var getAllowanceByBusinessCustomerIdResult = await getAllowanceByBusinessCustomerIdTask;
        if (getAllowanceByBusinessCustomerIdResult.IsError) return getAllowanceByBusinessCustomerIdResult.PreserveErrorAs<GetBusinessUnderstandingSummaryResult>();
        var allowances = getAllowanceByBusinessCustomerIdResult.Get();
        string? allowanceText = null;
        if (allowances.Any())
        {
            allowanceText = allowances[0].AllowanceType.DisplayName();
        }

        return Result.Ok(new GetBusinessUnderstandingSummaryResult()
        {
            Id = businessUnderstandingSummary.BusinessUnderstandingId,
            TransactionPolicy = transactionPolicyText,
            Allowance = allowanceText,
            FinancialsAlreadyShared = businessUnderstandingSummary.FinancialsAlreadySharedType?.DisplayName(),
            FinanceSharedAt = financeSharedDateOfJst,
            ManagementPlan = businessUnderstandingSummary.BusinessPlanRatingType?.DisplayName(),
            CommercialDistributionUpdatedAt = commercialDistributionUpdatedDateOfJst,
            CommunicationPlanCounts = communicationPlanSummaries.Concat(notContainedSummaries).OrderBy(c => c.CommunicationPlanType)
                // 財務の共有は不要
                .Where(summary => summary.CommunicationPlanType != CommunicationPlanType.SharingOfFinance)
                .Select(summary => new CommunicationPlanCount
                {
                    Category = summary.CommunicationPlanType.DisplayName()!,
                    LessThan1MonthCount = summary.LessThan1MonthCount,
                    LessThan6MonthCount = summary.LessThan6MonthCount,
                    GraterThanEqual6MonthCount = summary.GraterThanEqual6MonthCount
                })
        });
    }

    /// <summary>
    /// DateTimeOffsetから日本時間(日付まで)を取得する
    /// </summary>
    /// <param name="dateTimeOffset"></param>
    /// <returns></returns>
    private DateTime? GetJstDateFromDateTimeOffsetOrNull(DateTimeOffset? dateTimeOffset)
    {
        return dateTimeOffset?.UtcDateTime.AddHours(9).Date;
    }

    /// <summary>
    /// 空のCommunicationPlanSummaryを返す
    /// </summary>
    /// <param name="communicationPlanType"></param>
    /// <returns></returns>
    private CommunicationPlanSummary GenerateEmptyCommunicationPlanSummary(CommunicationPlanType communicationPlanType)
    {
        return new CommunicationPlanSummary() { CommunicationPlanType = communicationPlanType, LastRegisteredAt = null, LessThan1MonthCount = 0, LessThan6MonthCount = 0, GraterThanEqual6MonthCount = 0 };
    }
}
