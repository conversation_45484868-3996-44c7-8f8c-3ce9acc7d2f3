using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.CustomerIdeasUnderstanding.DeleteCustomerIdeasUnderstanding;

public class DeleteCustomerIdeasUnderstandingValidator : AbstractValidator<DeleteCustomerIdeasUnderstandingCommand>
{
    public DeleteCustomerIdeasUnderstandingValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.Version).NotEmpty();
    }
}
