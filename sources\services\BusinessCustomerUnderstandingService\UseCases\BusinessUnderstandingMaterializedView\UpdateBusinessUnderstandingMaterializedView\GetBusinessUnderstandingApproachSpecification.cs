using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingMaterializedView.UpdateBusinessUnderstandingMaterializedView;

public class GetBusinessUnderstandingApproachSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingApproach>
{
    public GetBusinessUnderstandingApproachSpecification(Guid CustomerIdentificationId)
    {
        Query
            .Where(x => x.CustomerIdentificationId == CustomerIdentificationId)
            .AsNoTracking();
    }
}
