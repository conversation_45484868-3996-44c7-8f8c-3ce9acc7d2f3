using CustomerProposalService.Domain;
using CustomerProposalService.Services.Domain;
using MediatR;
using Nut.Results;
using Shared.Domain;
using Shared.Services;

namespace CustomerProposalService.UseCases.CaseDiscussionReply.UpdateCaseDiscussionReply;

public class UpdateCaseDiscussionReplyHandler : IRequestHandler<UpdateCaseDiscussionReplyCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDateTimeService;
    private readonly ICaseDiscussionReplyUtility _utility;
    private readonly IFileProcessingService _fileProcessingService;

    private readonly string _containerName = "case-discussion";
    private readonly string _folderName = "case-discussion-reply";

    public UpdateCaseDiscussionReplyHandler(
        IUnitOfWork unitOfWork,
        ICurrentDateTimeService currentDateTimeService,
        ICaseDiscussionReplyUtility utility,
        IFileProcessingService fileProcessingService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
        _utility = utility ?? throw new ArgumentNullException(nameof(utility));
        _fileProcessingService = fileProcessingService ?? throw new ArgumentNullException(nameof(fileProcessingService));
    }

    public async Task<Result<string>> Handle(UpdateCaseDiscussionReplyCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CaseDiscussionReply, string>();
        var getResult = await repository.SingleAsync(
            new FindByIdSpecification<Domain.Entities.CaseDiscussionReply, string>(request.Id)
            .Include(c => c.Files)
            ).ConfigureAwait(false);

        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();
        var newDelta = await _utility.ArrangeReplyContentByUrlWhenSaveAsync(request.Description, request.Id);

        // 協議返信コメント変換失敗の場合、エラーをそのまま返却
        if (newDelta.IsError) return Result.Error<string>(newDelta.GetError());

        currentData.Description = newDelta.Get();
        currentData.MentionTargetUserIds = request.MentionTargetUserIds;
        currentData.MentionTargetTeamIds = request.MentionTargetTeamIds;
        currentData.Purpose = request.Purpose;
        currentData.Person = request.Person;
        currentData.IsPersonOfPower = request.IsPersonOfPower;

        // 案件更新情報の最終更新日を更新する
        var updateResult = await _utility.UpdateCaseLastUpdatedAt(request.CaseId, _currentDateTimeService.NowDateTimeOffset());
        if (updateResult.IsError) return updateResult;

        // バージョン番号は、同時実行制御を有効にするために引数で受け取ったデータの値で必ず上書きします。
        currentData.Version = request.Version;

        if (request.UploadFiles != null || request.FilesToRemove != null)
        {
            var uploadResult = await _fileProcessingService.UpdateFiles<Domain.Entities.CaseDiscussionReply, Domain.Entities.CaseDiscussionReplyFile>(currentData, _containerName, _folderName, request.UploadFiles, request.FilesToRemove);
            if (uploadResult.IsError) return uploadResult;
        }

        // 通知の実施
        await _utility.SendNotifyAsync(currentData, request.MentionTargetTeamMemberUserIds, request.CaseId, request.CustomerName, request.CustomerStaffId).ConfigureAwait(false);

        return await repository
            .UpdateAsync(currentData) // データを更新として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
