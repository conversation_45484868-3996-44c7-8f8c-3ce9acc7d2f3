using Microsoft.EntityFrameworkCore;
using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessCustomer.GetBusinessCustomerByCustomerIdentificationId;

public class GetBusinessCustomerByCustomerIdentificationIdSpecification : BaseSpecification<Domain.Entities.BusinessCustomer>
{
    public GetBusinessCustomerByCustomerIdentificationIdSpecification(Guid customerIdentificationId)
    {
        Query
            .Where(x => x.CustomerIdentificationId == customerIdentificationId)
            .AsNoTracking();
    }
}
