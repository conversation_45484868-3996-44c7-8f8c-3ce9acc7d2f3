using System.Reflection;
using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.EntityFrameworkCore;
using Shared.Services;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence;

public class ApplicationDbContext : DbContext
{
    private readonly ICurrentUserService _userService;
    private readonly ICurrentDateTimeService _dateTimeService;
    private readonly IApplicationEnvironment _applicationEnvironment;

    public ApplicationDbContext(
        DbContextOptions<ApplicationDbContext> options,
        ICurrentUserService userService,
        ICurrentDateTimeService dateTimeService,
        IApplicationEnvironment applicationEnvironment)
        : base(options)
    {
        _userService = userService ?? throw new ArgumentNullException(nameof(userService));
        _dateTimeService = dateTimeService ?? throw new ArgumentNullException(nameof(dateTimeService));
        _applicationEnvironment = applicationEnvironment ?? throw new ArgumentNullException(nameof(applicationEnvironment));
    }

    public DbSet<ExternalEnvironmentMaster> ExternalEnvironmentMaster => Set<ExternalEnvironmentMaster>();
    public DbSet<BusinessCustomer> BusinessCustomers => Set<BusinessCustomer>();
    public DbSet<BusinessUnderstanding> BusinessUnderstandings => Set<BusinessUnderstanding>();
    public DbSet<BusinessUnderstandingMaterializedView> BusinessUnderstandingMaterializedViews => Set<BusinessUnderstandingMaterializedView>();
    public DbSet<BusinessUnderstandingMaterializedViewQueue> BusinessUnderstandingMaterializedViewQueues => Set<BusinessUnderstandingMaterializedViewQueue>();
    public DbSet<BusinessUnderstandingHistory> BusinessUnderstandingHistories => Set<BusinessUnderstandingHistory>();
    public DbSet<Management> Managements => Set<Management>();
    public DbSet<FiveStepFrameWork> FiveStepFrameWorks => Set<FiveStepFrameWork>();
    public DbSet<FiveStepFrameWorkHistory> FiveStepFrameWorkHistories => Set<FiveStepFrameWorkHistory>();
    public DbSet<ManagementPlan> ManagementPlans => Set<ManagementPlan>();
    public DbSet<ManagementPlanHistory> ManagementPlanHistories => Set<ManagementPlanHistory>();
    public DbSet<FiveForceFramework> FiveForceFrameworks => Set<FiveForceFramework>();
    public DbSet<FiveForceFrameworkHistory> FiveForceFrameworkHistories => Set<FiveForceFrameworkHistory>();
    public DbSet<ESGAndSDGs> ESGAndSDGs => Set<ESGAndSDGs>();
    public DbSet<ESGAndSDGsHistory> ESGAndSDGsHistories => Set<ESGAndSDGsHistory>();
    public DbSet<RelationLevel> RelationLevels => Set<RelationLevel>();
    public DbSet<RelationLevelHistory> RelationLevelHistories => Set<RelationLevelHistory>();
    public DbSet<ExternalEnvironment> ExternalEnvironments => Set<ExternalEnvironment>();
    public DbSet<ExternalEnvironmentHistory> ExternalEnvironmentHistories => Set<ExternalEnvironmentHistory>();
    public DbSet<ThreeCAnalysis> ThreeCAnalyses => Set<ThreeCAnalysis>();
    public DbSet<SWOTAnalysis> SWOTAnalyses => Set<SWOTAnalysis>();
    public DbSet<SWOTAnalysisHistory> SWOTAnalysesHistories => Set<SWOTAnalysisHistory>();
    public DbSet<CommercialDistribution> CommercialDistributions => Set<CommercialDistribution>();
    public DbSet<CommercialDistributionHistory> CommercialDistributionHistories => Set<CommercialDistributionHistory>();
    public DbSet<CommercialDistributionNode> CommercialDistributionNodes => Set<CommercialDistributionNode>();
    public DbSet<CommercialDistributionNodeHistory> CommercialDistributionNodeHistories => Set<CommercialDistributionNodeHistory>();
    public DbSet<CommercialDistributionEdge> CommercialDistributionEdges => Set<CommercialDistributionEdge>();
    public DbSet<CommercialDistributionEdgeHistory> CommercialDistributionEdgeHistories => Set<CommercialDistributionEdgeHistory>();
    public DbSet<CommercialDistributionTemplate> CommercialDistributionTemplates => Set<CommercialDistributionTemplate>();
    public DbSet<CommercialDistributionTemplateNode> CommercialDistributionTemplateNodes => Set<CommercialDistributionTemplateNode>();
    public DbSet<CommercialDistributionTemplateEdge> CommercialDistributionTemplateEdges => Set<CommercialDistributionTemplateEdge>();
    public DbSet<FamilyTree> FamilyTrees => Set<FamilyTree>();
    public DbSet<FamilyTreeHistory> FamilyTreeHistories => Set<FamilyTreeHistory>();
    public DbSet<FamilyTreeNode> FamilyTreeNodes => Set<FamilyTreeNode>();
    public DbSet<FamilyTreeNodeHistory> FamilyTreeNodeHistories => Set<FamilyTreeNodeHistory>();
    public DbSet<FamilyTreeEdge> FamilyTreeEdges => Set<FamilyTreeEdge>();
    public DbSet<FamilyTreeEdgeHistory> FamilyTreeEdgeHistories => Set<FamilyTreeEdgeHistory>();
    public DbSet<OurPolicyUnderstanding> OurPolicyUnderstandings => Set<OurPolicyUnderstanding>();
    public DbSet<OurPolicyUnderstandingThread> OurPolicyUnderstandingThreads => Set<OurPolicyUnderstandingThread>();
    public DbSet<OurPolicyUnderstandingThreadReaction> OurPolicyUnderstandingThreadReactions => Set<OurPolicyUnderstandingThreadReaction>();
    public DbSet<OurPolicyUnderstandingThreadFile> OurPolicyUnderstandingThreadFiles => Set<OurPolicyUnderstandingThreadFile>();
    public DbSet<OurPolicyUnderstandingComment> OurPolicyUnderstandingComments => Set<OurPolicyUnderstandingComment>();
    public DbSet<OurPolicyUnderstandingCommentReaction> OurPolicyUnderstandingCommentReactions => Set<OurPolicyUnderstandingCommentReaction>();
    public DbSet<OurPolicyUnderstandingCommentFile> OurPolicyUnderstandingCommentFiles => Set<OurPolicyUnderstandingCommentFile>();
    public DbSet<HypotheticalDiscussionOfIssues> HypotheticalDiscussionOfIssues => Set<HypotheticalDiscussionOfIssues>();
    public DbSet<HypotheticalDiscussionOfIssuesThread> HypotheticalDiscussionOfIssuesThreads => Set<HypotheticalDiscussionOfIssuesThread>();
    public DbSet<HypotheticalDiscussionOfIssuesThreadReaction> HypotheticalDiscussionOfIssuesThreadReactions => Set<HypotheticalDiscussionOfIssuesThreadReaction>();
    public DbSet<HypotheticalDiscussionOfIssuesThreadFile> HypotheticalDiscussionOfIssuesThreadFiles => Set<HypotheticalDiscussionOfIssuesThreadFile>();
    public DbSet<HypotheticalDiscussionOfIssuesComment> HypotheticalDiscussionOfIssuesComments => Set<HypotheticalDiscussionOfIssuesComment>();
    public DbSet<HypotheticalDiscussionOfIssuesCommentReaction> HypotheticalDiscussionOfIssuesCommentReactions => Set<HypotheticalDiscussionOfIssuesCommentReaction>();
    public DbSet<HypotheticalDiscussionOfIssuesCommentFile> HypotheticalDiscussionOfIssuesCommentFiles => Set<HypotheticalDiscussionOfIssuesCommentFile>();
    public DbSet<CustomerIdeasUnderstanding> CustomerIdeasUnderstandings => Set<CustomerIdeasUnderstanding>();
    public DbSet<CustomerIdeasUnderstandingThread> CustomerIdeasUnderstandingThreads => Set<CustomerIdeasUnderstandingThread>();
    public DbSet<CustomerIdeasUnderstandingThreadReaction> CustomerIdeasUnderstandingThreadReactions => Set<CustomerIdeasUnderstandingThreadReaction>();
    public DbSet<CustomerIdeasUnderstandingComment> CustomerIdeasUnderstandingComments => Set<CustomerIdeasUnderstandingComment>();
    public DbSet<CustomerIdeasUnderstandingCommentReaction> CustomerIdeasUnderstandingCommentReactions => Set<CustomerIdeasUnderstandingCommentReaction>();
    public DbSet<CustomerIdeasUnderstandingCommentFile> CustomerIdeasUnderstandingCommentFiles => Set<CustomerIdeasUnderstandingCommentFile>();
    public DbSet<CustomerIdeasUnderstandingThreadFile> CustomerIdeasUnderstandingThreadFiles => Set<CustomerIdeasUnderstandingThreadFile>();
    public DbSet<ToDo> ToDo => Set<ToDo>();
    public DbSet<ToDoThread> ToDoThreads => Set<ToDoThread>();
    public DbSet<ToDoThreadReaction> ToDoThreadReactions => Set<ToDoThreadReaction>();
    public DbSet<ToDoComment> ToDoComments => Set<ToDoComment>();
    public DbSet<ToDoCommentReaction> ToDoCommentReactions => Set<ToDoCommentReaction>();
    public DbSet<ToDoCommentFile> ToDoCommentFiles => Set<ToDoCommentFile>();
    public DbSet<ToDoThreadFile> ToDoThreadFiles => Set<ToDoThreadFile>();
    public DbSet<CustomerReaction> CustomerReactions => Set<CustomerReaction>();
    public DbSet<IssueTypeMaster> IssueTypeMasters => Set<IssueTypeMaster>();
    public DbSet<ThreeCAnalysisHistory> ThreeCAnalysisHistories => Set<ThreeCAnalysisHistory>();
    public DbSet<ManagementHistory> ManagementHistories => Set<ManagementHistory>();
    public DbSet<FamilyTreeFile> FamilyTreeFiles => Set<FamilyTreeFile>();
    public DbSet<BusinessUnderstandingApproach> BusinessUnderstandingApproaches => Set<BusinessUnderstandingApproach>();
    public DbSet<BusinessUnderstandingFile> BusinessUnderstandingFiles => Set<BusinessUnderstandingFile>();
    public DbSet<SharingOfFinance> SharingOfFinances => Set<SharingOfFinance>();
    public DbSet<SharingOfFinanceThread> SharingOfFinanceThreads => Set<SharingOfFinanceThread>();
    public DbSet<SharingOfFinanceThreadReaction> SharingOfFinanceThreadReactions => Set<SharingOfFinanceThreadReaction>();
    public DbSet<SharingOfFinanceThreadFile> SharingOfFinanceThreadFiles => Set<SharingOfFinanceThreadFile>();

    public DbSet<SharingOfFinanceComment> SharingOfFinanceComments => Set<SharingOfFinanceComment>();
    public DbSet<SharingOfFinanceCommentReaction> SharingOfFinanceCommentReactions => Set<SharingOfFinanceCommentReaction>();
    public DbSet<SharingOfFinanceCommentFile> SharingOfFinanceCommentFiles => Set<SharingOfFinanceCommentFile>();
    public DbSet<Domain.Entities.CustomerLink> CustomerLinks => Set<Domain.Entities.CustomerLink>();
    public DbSet<BusinessUnderstandingDiscussion> BusinessUnderstandingDiscussions => Set<BusinessUnderstandingDiscussion>();
    public DbSet<BusinessUnderstandingDiscussionReaction> BusinessUnderstandingDiscussionReactions => Set<BusinessUnderstandingDiscussionReaction>();
    public DbSet<BusinessUnderstandingThread> BusinessUnderstandingThreads => Set<BusinessUnderstandingThread>();
    public DbSet<BusinessUnderstandingThreadReaction> BusinessUnderstandingThreadReactions => Set<BusinessUnderstandingThreadReaction>();
    public DbSet<ExternalEnvironmentMasterUploadProgress> ExternalEnvironmentMasterUploadProgresses => Set<ExternalEnvironmentMasterUploadProgress>();
    public DbSet<BusinessUnderstandingThreadFile> BusinessUnderstandingThreadFiles => Set<BusinessUnderstandingThreadFile>();
    public DbSet<BusinessUnderstandingDiscussionFile> BusinessUnderstandingDiscussionFiles => Set<BusinessUnderstandingDiscussionFile>();
    public DbSet<BusinessPartner> BusinessPartners => Set<BusinessPartner>();
    public DbSet<BusinessPartnershipAndCreativeAccountingDetail> BusinessPartnershipAndCreativeAccountingDetails => Set<BusinessPartnershipAndCreativeAccountingDetail>();
    public DbSet<BusinessPartnerIndustry> BusinessPartnerIndustries => Set<BusinessPartnerIndustry>();

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        // 保存時にインターフェイスにしたがって適切な値を自動的に設定するインターセプターを設定します。
        optionsBuilder.AddInterceptors(new ApplyStandardValuesInterceptor(_userService, _dateTimeService, _applicationEnvironment));
        base.OnConfiguring(optionsBuilder);
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // モデルの推奨される設定を行います。
        modelBuilder.ApplyRecommendModelSettings();
        // モデルの個別の設定を行います。
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        base.OnModelCreating(modelBuilder);
    }
}
