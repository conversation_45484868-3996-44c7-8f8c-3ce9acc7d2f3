using CustomerProposalService.Domain.Enums;

namespace CustomerProposalService.UseCases.Case.GetForeignExchangeTransactionCase;

public record GetForeignExchangeTransactionCaseResult(
    // Case Entity
    string Id,
    Guid CustomerIdentificationId,
    CaseCategory CaseCategory,
    string CaseName,
    CaseStatus CaseStatus,
    string? CaseOutline,
    DateTimeOffset? ExpiredAt,
    string StaffId,
    string StaffName,
    DateTimeOffset RegisteredAt,
    DateTimeOffset CaseUpdatedAt,
    DateTimeOffset LastUpdatedAt,
    IEnumerable<Domain.Entities.CaseFile>? CaseFiles,
    IEnumerable<Domain.Entities.CaseLink>? CaseLinks,
    string Version,
    // ForeignExchangeTransactionCase Entity
    string? ApprovalType,
    decimal? Amount,
    bool RelatedEsg,
    bool PreConsultationStandard,
    bool IsEarthquakeRelated,
    string? CollateralType,
    string? CollateralOrGuaranteeCustom,
    string? GuaranteeType,
    CancelTypeOfLoan? CancelTypeOfLoan,
    string? CancelReason,
    IEnumerable<Domain.Entities.Guarantor>? Guarantors
);
