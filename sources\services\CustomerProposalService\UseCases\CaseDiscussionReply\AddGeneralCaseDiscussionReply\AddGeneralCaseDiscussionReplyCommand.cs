using CustomerProposalService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionReply.AddGeneralCaseDiscussionReply;

[WithDefaultBehaviors]
public record AddGeneralCaseDiscussionReplyCommand(
    string CaseId,
    string CaseDiscussionThreadId,
    Guid CustomerIdentificationId,
    string CustomerName,
    string CustomerStaffId,
    string RegistrantId,
    string RegistrantName,
    CaseDiscussionPurpose Purpose,
    string? Person,
    bool? IsPersonOfPower,
    IEnumerable<string>? MentionTargetUserIds,
    IEnumerable<string>? MentionTargetTeamIds,
    IEnumerable<string>? MentionTargetTeamMemberUserIds,
    string Description,
    IEnumerable<IFormFile>? UploadFiles
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
