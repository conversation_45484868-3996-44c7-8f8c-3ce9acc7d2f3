using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;
using Shared.Application;
using Shared.Domain;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.FindBusinessUnderstandingApproach;

public class FindBusinessUnderstandingApproachHandler : IRequestHandler<FindBusinessUnderstandingApproachQuery, Result<PaginatedResult<FindBusinessUnderstandingApproachResult>>>
{
    private readonly IRepository<Domain.Entities.BusinessUnderstandingApproach> _repository;

    public FindBusinessUnderstandingApproachHandler(IRepositoryFactory repositoryFactory)
    {
        if (repositoryFactory is null) throw new ArgumentNullException(nameof(repositoryFactory));
        _repository = repositoryFactory.GetRepository<Domain.Entities.BusinessUnderstandingApproach>();
    }

    public Task<Result<PaginatedResult<FindBusinessUnderstandingApproachResult>>> Handle(FindBusinessUnderstandingApproachQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));
        // 検索を行います。
        var spec = new FindBusinessUnderstandingApproachSpecification(request);
        var result = _repository.FindWithPaginationAsync(
            spec,
            request.ExtractSafePagenationOption(sort: new List<Sort>()
            {
                new Sort() { Target = nameof(Domain.Entities.BusinessUnderstandingApproach.Id) },
            }));
        // 検索結果を FindBusinessUnderstandingApproachResult にマップして返します。
        return result.Map(pr =>
            pr.Map(v => new FindBusinessUnderstandingApproachResult(v.Id, v.CustomerIdentificationId, v.ApproachType, v.Comment, v.Version)));
    }
}
