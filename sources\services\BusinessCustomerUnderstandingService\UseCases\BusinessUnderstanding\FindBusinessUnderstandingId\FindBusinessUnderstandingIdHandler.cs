using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;
using Shared.Domain;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindBusinessUnderstandingId;

public class FindBusinessUnderstandingIdHandler : IRequestHandler<FindBusinessUnderstandingIdQuery, Result<List<FindBusinessUnderstandingIdResult>>>
{
    private readonly IRepository<Domain.Entities.BusinessUnderstanding> _repository;

    public FindBusinessUnderstandingIdHandler(IRepositoryFactory repositoryFactory)
    {
        if (repositoryFactory is null) throw new ArgumentNullException(nameof(repositoryFactory));
        _repository = repositoryFactory.GetRepository<Domain.Entities.BusinessUnderstanding>();
    }

    public async Task<Result<List<FindBusinessUnderstandingIdResult>>> Handle(FindBusinessUnderstandingIdQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 検索を行います。
        var spec = new FindBusinessUnderstandingIdSpecification(request);
        var result = await _repository.FindAsync(spec)
            .ConfigureAwait(false);

        return result.Map(f => f.Select(v => new FindBusinessUnderstandingIdResult(
            v.Id,
            v.BusinessCustomerId,
            v.Version
            )).ToList());
    }
}
