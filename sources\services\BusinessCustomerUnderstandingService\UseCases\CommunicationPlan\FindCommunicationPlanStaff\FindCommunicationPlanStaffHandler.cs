using MediatR;
using Nut.Results;
using BusinessCustomerUnderstandingService.Services.Queries;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.FindCommunicationPlanStaff;

public class FindCommunicationPlanStaffHandler : IRequestHandler<FindCommunicationPlanStaffQuery, Result<IEnumerable<FindCommunicationPlanStaffResult>>>
{
    private readonly IFindCommunicationPlanStaffQueryService _queryService;

    public FindCommunicationPlanStaffHandler(IFindCommunicationPlanStaffQueryService queryService)
    {
        _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
    }

    public async Task<Result<IEnumerable<FindCommunicationPlanStaffResult>>> Handle(FindCommunicationPlanStaffQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        return await _queryService.Handle();
    }
}
