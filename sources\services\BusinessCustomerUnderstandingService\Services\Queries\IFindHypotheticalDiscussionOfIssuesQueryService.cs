using Nut.Results;
using BusinessCustomerUnderstandingService.Domain.Entities;

namespace BusinessCustomerUnderstandingService.Services.Queries;

public interface IFindHypotheticalDiscussionOfIssuesQueryService
{
    Task<Result<IEnumerable<HypotheticalDiscussionOfIssues>>> Handle(GetHypotheticalDiscussionOfIssuesQuery request);
}

public record GetHypotheticalDiscussionOfIssuesQuery(
    string? Id,
    string? HypothesisForIssue,
    string? HypothesisForSolution,
    DateTimeOffset? ExpiredDateTimeFrom,
    DateTimeOffset? ExpiredDateTimeTo,
    string? Title,
    BusinessCustomerUnderstandingService.Domain.Enums.Status? Status,
    string? BusinessUnderstandingId);
