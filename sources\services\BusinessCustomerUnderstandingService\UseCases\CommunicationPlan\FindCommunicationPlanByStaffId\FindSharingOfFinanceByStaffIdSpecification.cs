using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.FindCommunicationPlanByStaffIds;

public class FindSharingOfFinanceByStaffIdSpecification : BaseSpecification<Domain.Entities.SharingOfFinance>
{
    public FindSharingOfFinanceByStaffIdSpecification(string staffId)
    {
        Query
            .Where(e => e.StaffId == staffId)
            .Where(e => e.Status != Domain.Enums.Status.Completed)
            .AsNoTracking();
    }
}
