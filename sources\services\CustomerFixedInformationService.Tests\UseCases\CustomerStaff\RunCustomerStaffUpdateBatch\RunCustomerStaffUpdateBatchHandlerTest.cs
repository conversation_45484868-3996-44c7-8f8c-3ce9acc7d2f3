using CustomerFixedInformationService.Configurations;
using CustomerFixedInformationService.UseCases.CustomerStaff.RunCustomerStaffUpdateBatch;
using Xunit;

namespace CustomerFixedInformationService.Tests.UseCases.CustomerStaff.RunCustomerStaffUpdateBatch;

public class RunCustomerStaffUpdateBatchHandlerTests
{
    private RunCustomerStaffUpdateBatchHandler CreateHandler()
        => new(new ServiceSettings
        {
            CustomerStaffUpdateBatch = new CustomerStaffUpdateBatchSettings
            {
                BatchUrl = "http://localhost/test"
            }
        });

    [Fact]
    public async Task Handle_RequestIsNull_ThrowsArgumentNullException()
    {
        var handler = CreateHandler();

        await Assert.ThrowsAsync<ArgumentNullException>(
            () => handler.Handle(null!, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_RequestIsValid_ReturnsOkResult()
    {
        var handler = CreateHandler();
        var request = new RunCustomerStaffUpdateBatchCommand();

        var result = await handler.Handle(request, CancellationToken.None);

        Assert.True(result.IsOk);
        Assert.Equal(string.Empty, result);
    }

    [Fact]
    public async Task Handle_ValidRequest_TriggersSendAsyncWithoutException()
    {
        var handler = CreateHandler();
        var request = new RunCustomerStaffUpdateBatchCommand();

        var result = await handler.Handle(request, CancellationToken.None);

        Assert.True(result.IsOk);
    }
}
