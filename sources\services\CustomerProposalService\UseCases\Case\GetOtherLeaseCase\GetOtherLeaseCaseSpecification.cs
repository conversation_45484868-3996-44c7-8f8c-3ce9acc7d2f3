using Shared.Spec;

namespace CustomerProposalService.UseCases.Case.GetOtherLeaseCase;

public class GetOtherLeaseCaseSpecification : BaseSpecification<Domain.Entities.OtherLeaseCase>
{
    public GetOtherLeaseCaseSpecification(GetOtherLeaseCaseQuery request)
    {
        Query
            .Where(x => x.Id == request.Id)
            .Include(x => x.CaseUpdateInformation)
            .Include(x => x.CaseFiles)
            .Include(x => x.CaseLinks)
            .AsNoTracking();
    }
}
