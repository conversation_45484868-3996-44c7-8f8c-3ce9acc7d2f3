using Microsoft.EntityFrameworkCore;
using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistribution.GetCommercialDistributionByBusinessUnderstandingId;

public class GetCommercialDistributionByBusinessUnderstandingIdSpecification : BaseSpecification<Domain.Entities.CommercialDistribution>
{
    public GetCommercialDistributionByBusinessUnderstandingIdSpecification(string businessUnderstandingId)
    {
        Query
            .Where(x => x.BusinessUnderstandingId == businessUnderstandingId)
            .Include(x => x.Nodes)
            .Include(x => x.Edges)
            .AsNoTracking();
    }
}
