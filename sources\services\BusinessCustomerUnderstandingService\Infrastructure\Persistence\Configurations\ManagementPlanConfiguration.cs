using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;

public class ManagementPlanConfiguration : IEntityTypeConfiguration<ManagementPlan>
{
    public void Configure(EntityTypeBuilder<ManagementPlan> builder)
    {
        builder.Property(u => u.Id)
            .HasMaxLength(26);

        builder.Property(u => u.ManagementPlanOverview)
            .HasMaxLength(1000);

        builder.Property(u => u.ManagementPlanCurrentComment)
            .HasMaxLength(1000);

        builder.Property(u => u.StatusOfAchievementOfManagementPlan)
            .HasMaxLength(1000);

        builder.Property(u => u.Ideal)
            .HasMaxLength(1000);

        builder.Property(u => u.Issue)
            .HasMaxLength(1000);
    }
}
