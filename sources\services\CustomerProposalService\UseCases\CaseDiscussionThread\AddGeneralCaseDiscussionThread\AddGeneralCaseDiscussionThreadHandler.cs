using System.Text;
using CustomerProposalService.Domain;
using CustomerProposalService.Domain.Entities;
using CustomerProposalService.Domain.Enums;
using CustomerProposalService.Services.Domain;
using MediatR;
using Nut.Results;
using Shared.Domain;
using Shared.Services;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.AddGeneralCaseDiscussionThread;

public class AddGeneralCaseDiscussionThreadHandler : IRequestHandler<AddGeneralCaseDiscussionThreadCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDateTimeService;
    private readonly ICaseDiscussionThreadUtility _utility;
    private readonly IFileProcessingService _fileProcessingService;

    private readonly string _folderName = "case-discussion-thread";
    private readonly string _containerName = "case-discussion";

    public AddGeneralCaseDiscussionThreadHandler(
        IUnitOfWork unitOfWork,
        ICurrentDateTimeService currentDateTimeService,
        ICaseDiscussionThreadUtility utility,
        IFileProcessingService fileProcessingService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
        _utility = utility ?? throw new ArgumentNullException(nameof(utility));
        _fileProcessingService = fileProcessingService ?? throw new ArgumentNullException(nameof(fileProcessingService));
    }

    public async Task<Result<string>> Handle(AddGeneralCaseDiscussionThreadCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var now = _currentDateTimeService.NowDateTimeOffset();

        var displayText = new StringBuilder();
        displayText.AppendLine($"【目的】{request.Purpose.DisplayName()}");
        if (request.Purpose == Domain.Enums.CaseDiscussionPurpose.External)
        {
            displayText.AppendLine($"【相手】{request.Person}");
            if (request.IsPersonOfPower == true)
            {
                displayText.AppendLine($"【実権者】☑");
            }
        }
        displayText.AppendLine($"【協議コメント】");
        displayText.AppendLine($"{request.Description}");

        // 更新する値を作成します。
        var newData = new Domain.Entities.GeneralCaseDiscussionThread()
        {
            // キーの値をUlidで生成します。
            Id = Ulid.NewUlid().ToString(),
            CaseId = request.CaseId,
            ThreadName = request.ThreadName,
            RegistrantId = request.RegistrantId,
            RegistrantName = request.RegistrantName,
            RegisteredAt = now,
            Purpose = request.Purpose,
            Person = request.Person,
            IsPersonOfPower = request.IsPersonOfPower,
            MentionTargetsHtml = request.MentionTargetsHtml,
            MentionTargetUserIds = request.MentionTargetUserIds,
            MentionTargetTeamIds = request.MentionTargetTeamIds,
            Description = request.Description,
            DisplayText = displayText.ToString(),
        };

        // 案件更新情報の最終更新日を更新する
        var updateResult = await _utility.UpdateCaseLastUpdatedAt(request.CaseId, now);
        if (updateResult.IsError) return updateResult;


        if (request.UploadFiles != null)
        {
            // ファイルをアップロードします。
            var uploadResult = await _fileProcessingService.UpdateFiles<Domain.Entities.CaseDiscussionThread, CaseDiscussionThreadFile>(newData, _containerName, _folderName, request.UploadFiles, null);
            if (uploadResult.IsError) return uploadResult;
        }

        // 通知処理を行います。
        await _utility.SendNotifyAsync(newData, request.MentionTargetTeamMemberUserIds, request.CustomerName, request.CustomerStaffId, NotificationType.Post).ConfigureAwait(false);

        var repository = _unitOfWork.GetRepository<Domain.Entities.GeneralCaseDiscussionThread>();
        return await repository
            .AddAsync(newData) // データを追加します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(newData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
