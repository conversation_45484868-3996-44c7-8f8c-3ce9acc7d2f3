using CustomerProposalService.Domain.Enums;

namespace CustomerProposalService.UseCases.Case.GetNewLoanCase;

public record GetNewLoanCaseResult(
    // Case Entity
    string Id,
    Guid CustomerIdentificationId,
    CaseCategory CaseCategory,
    string CaseName,
    CaseStatus CaseStatus,
    string? CaseOutline,
    DateTimeOffset? ExpiredAt,
    string StaffId,
    string StaffName,
    DateTimeOffset RegisteredAt,
    DateTimeOffset CaseUpdatedAt,
    DateTimeOffset LastUpdatedAt,
    IEnumerable<Domain.Entities.CaseFile>? CaseFiles,
    IEnumerable<Domain.Entities.CaseLink>? CaseLinks,
    string Version,
    // NewLoanCase Entity
    SubjectType? SubjectType,
    decimal? Amount,
    int? Period,
    decimal? InterestRate,
    string? InterestRateCustom,
    UseOfFundsType? UseOfFundsType,
    string? UseOfFundsCustom,
    RepaymentMethodType? RepaymentMethodType,
    string? RepaymentMethodCustom,
    bool? CollateralOrGuarantee,
    string? CollateralOrGuaranteeCustom,
    CancelTypeOfLoan? CancelTypeOfLoan,
    string? CancelReason,
    bool? PreConsultationStandardTarget,
    bool? RelatedEsg,
    bool IsEarthquakeRelated,
    string? OnSitePhysicalConfirmer,
    DateTimeOffset? OnSitePhysicalConfirmationDateTime,
    TrafficSource? TrafficSource
);
