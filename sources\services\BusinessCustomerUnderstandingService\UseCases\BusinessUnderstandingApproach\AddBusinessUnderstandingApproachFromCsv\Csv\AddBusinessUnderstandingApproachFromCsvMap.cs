using CsvHelper.Configuration;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.AddBusinessUnderstandingApproachFromCsv.Csv;

public class AddBusinessUnderstandingApproachFromCsvMap : ClassMap<AddBusinessUnderstandingApproachFromCsvDto>
{
    public AddBusinessUnderstandingApproachFromCsvMap()
    {
        Map(m => m.BranchNumber).Name("店番");
        Map(m => m.CifNumber).Convert(row => FormatCifNumber(row.Row.GetField("CIF番号")));
        Map(m => m.ApproachType).Name("セグメント");
    }

    private static string FormatCifNumber(string cifNumber)
        => cifNumber.PadLeft(8, '0');
}
