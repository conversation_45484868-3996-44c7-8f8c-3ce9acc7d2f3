using Shared.Spec;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.FindCaseDiscussionThread;

public class FindCaseDiscussionThreadSpecification : BaseSpecification<Domain.Entities.CaseDiscussionThread>
{
    public FindCaseDiscussionThreadSpecification(string caseId, DateTimeOffset? fromDate, DateTimeOffset? toDate)
    {
        Query
            .Where(e => e.CaseId == caseId)
            .WhereIf(fromDate != null, x => fromDate <= x.RegisteredAt)
            .WhereIf(toDate != null, x => toDate!.Value.AddDays(1) > x.RegisteredAt)
            .Include(x => x.Files)
            .Include(x => x.Reactions!.OrderByDescending(r => r.UpdatedDateTime))
            .Include(x => x.Replies!.OrderBy(t => t.RegisteredAt), x => x.ThenInclude(x => x.Files))
            .Include(x => x.Replies!.OrderBy(t => t.RegisteredAt), x => x.ThenInclude(x => x.Reactions!.OrderByDescending(r => r.UpdatedDateTime)))
            .OrderByDescending(e => e.RegisteredAt)
            .AsNoTracking();
    }
}
