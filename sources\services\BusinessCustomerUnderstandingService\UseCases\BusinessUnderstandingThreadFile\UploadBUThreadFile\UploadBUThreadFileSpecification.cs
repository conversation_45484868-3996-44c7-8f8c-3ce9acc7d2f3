using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThreadFile.UploadBUThreadFile;

public class UploadBUThreadFileSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingThreadFile>
{
    public UploadBUThreadFileSpecification(string threadId)
    {
        Query
            .Where(x => x.ThreadId == threadId)
            .AsNoTracking();
    }
}
