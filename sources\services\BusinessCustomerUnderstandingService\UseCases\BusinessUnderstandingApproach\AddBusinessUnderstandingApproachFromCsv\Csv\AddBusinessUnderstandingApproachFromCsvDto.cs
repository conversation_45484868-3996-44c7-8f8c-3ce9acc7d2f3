using BusinessCustomerUnderstandingService.Domain.Enums;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.AddBusinessUnderstandingApproachFromCsv.Csv;

public class AddBusinessUnderstandingApproachFromCsvDto
{
    public string BranchNumber { get; init; } = default!;

    public string CifNumber { get; init; } = default!;

    public BusinessUnderstandingApproachType ApproachType { get; init; } = default!;
}
