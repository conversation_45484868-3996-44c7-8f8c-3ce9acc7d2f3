using Nut.MediatR;
using Shared.Services;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussion.DeleteBusinessUnderstandingDiscussion;
public class DeleteBusinessUnderstandingDiscussionAuthorizer : IAuthorizer<DeleteBusinessUnderstandingDiscussionCommand>
{
    private readonly ICurrentUserService _currentUserService;

    public DeleteBusinessUnderstandingDiscussionAuthorizer(ICurrentUserService currentUserService)
    {
        _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
    }

    public async Task<AuthorizationResult> AuthorizeAsync(DeleteBusinessUnderstandingDiscussionCommand request, CancellationToken cancellationToken)
    {
        var currentUser = await _currentUserService.GetAsync().ConfigureAwait(false);

        if (currentUser.UserId == request.RegistrantId)
        {
            return AuthorizationResult.Success();
        }

        return AuthorizationResult.Failed("不正なリクエストです。");
    }

}
