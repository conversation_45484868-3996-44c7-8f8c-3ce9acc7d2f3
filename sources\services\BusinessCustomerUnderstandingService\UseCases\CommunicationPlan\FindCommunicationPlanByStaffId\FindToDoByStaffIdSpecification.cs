using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.FindCommunicationPlanByStaffIds;

public class FindToDoByStaffIdSpecification : BaseSpecification<Domain.Entities.ToDo>
{
    public FindToDoByStaffIdSpecification(string staffId)
    {
        Query
            .Where(e => e.StaffId == staffId)
            .Where(e => e.Status != Domain.Enums.Status.Completed)
            .AsNoTracking();
    }
}
