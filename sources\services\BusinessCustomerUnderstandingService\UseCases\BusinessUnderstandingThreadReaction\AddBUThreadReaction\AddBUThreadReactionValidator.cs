using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThreadReaction.AddBUThreadReaction;

public class AddBUThreadReactionValidator : AbstractValidator<AddBUThreadReactionCommand>
{
    public AddBUThreadReactionValidator()
    {
        RuleFor(v => v.ThreadId).NotEmpty();
        RuleFor(v => v.StaffId).NotEmpty();
        RuleFor(v => v.StaffName).NotEmpty();
        RuleFor(v => v.ReactionType).NotEmpty();
        RuleFor(v => v.UpdatedDateTime).NotEmpty();
    }
}
