using Shared.Spec;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.GetLeaseCaseDiscussionThread;

public class GetLeaseCaseDiscussionThreadSpecification : BaseSpecification<Domain.Entities.LeaseCaseDiscussionThread>
{
    public GetLeaseCaseDiscussionThreadSpecification(GetLeaseCaseDiscussionThreadQuery request)
    {
        Query
            .Where(x => x.Id == request.Id)
            .Include(x => x.Files)
            .AsNoTracking();
    }
}
