using BusinessCustomerUnderstandingService.Domain.Entities;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Services.Queries;
using Microsoft.EntityFrameworkCore;
using Nut.Results;
using Shared.Linq;
using Shared.Results.Errors;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;

internal class OurPolicyUnderstandingQueryService : IGetOurPolicyUnderstandingQueryService
{
    private readonly ApplicationDbContext _dbContext;

    public OurPolicyUnderstandingQueryService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    async Task<Result<OurPolicyUnderstanding>> IGetOurPolicyUnderstandingQueryService.Handle(string id)
    {
        var hypotheticalDiscussionOfIssue = await _dbContext.OurPolicyUnderstandings
           .Where(e => e.Id == id)
           .Include(x => x.Threads!).ThenInclude(x => x.Comments).ThenInclude(x => x.Files)
           .Include(x => x.Threads!).ThenInclude(x => x.Comments).ThenInclude(x => x.Reactions)
           .Include(x => x.Threads).ThenInclude(x => x.Files)
           .Include(x => x.Threads).ThenInclude(x => x.Reactions)
           .OrderBy(x => x.Order)
           .AsNoTracking()
           .FirstOrDefaultAsync();

        if (hypotheticalDiscussionOfIssue is null)
        {
            return Result.Error<OurPolicyUnderstanding>(new DataNotFoundException());
        }

        return Result.Ok<OurPolicyUnderstanding>(hypotheticalDiscussionOfIssue!);
    }
}
