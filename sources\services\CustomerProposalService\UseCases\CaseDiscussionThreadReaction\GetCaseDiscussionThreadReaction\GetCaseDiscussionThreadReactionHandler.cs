using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionThreadReaction.GetCaseDiscussionThreadReaction;

public class GetCaseDiscussionThreadReactionHandler : IRequestHandler<GetCaseDiscussionThreadReactionQuery, Result<GetCaseDiscussionThreadReactionResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetCaseDiscussionThreadReactionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetCaseDiscussionThreadReactionResult>> Handle(GetCaseDiscussionThreadReactionQuery request, CancellationToken cancellationToken)
    {
        if (request is null) throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CaseDiscussionThreadReaction, string>();
        return repository.GetAsync(request.Id)
            .Map(v => new GetCaseDiscussionThreadReactionResult(
                v.Id,
                v.CaseDiscussionThreadId,
                v.UpdaterId,
                v.UpdaterName,
                v.ReactionType,
                v.UpdatedDateTime,
                v.Version));
    }
}
