using Dto = BusinessCustomerUnderstandingService.Domain.Dto.BusinessUnderstanding;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessUnderstandingByCustomerIdentificationId;

public record GetBusinessUnderstandingByCustomerIdentificationIdResult(
    string Id,
    Dto.ManagementPlan ManagementPlan,
    Dto.Management Management,
    Dto.FiveForceFramework FiveForceFramework,
    Dto.FiveStepFrameWork FiveStepFrameWork,
    Dto.ESGAndSDGs ESGAndSDGs,
    Dto.ExternalEnvironment ExternalEnvironment,
    Dto.RelationLevel RelationLevel,
    Dto.ThreeCAnalysis ThreeCAnalysis,
    Dto.SWOTAnalysis SWOTAnalysis,
    Dto.CommercialDistribution CommercialDistribution,
    Dto.FamilyTree FamilyTree,
    Dto.CalculationResult CalculationResult,
    string TransactionPolicy,
    string? TransactionPolicyConfirmerId,
    DateTimeOffset? TransactionPolicyConfirmedDateTime,
    string BusinessCustomerId,
    DateTimeOffset? UpdatedDateTime,
    string Version
);
