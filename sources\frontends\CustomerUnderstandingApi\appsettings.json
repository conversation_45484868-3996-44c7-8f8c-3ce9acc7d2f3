{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      // Entity Framework の SQL
      "Microsoft.EntityFrameworkCore.Database.Command": "Information",
      // エンドポイントの実行
      "Microsoft.AspNetCore.Routing.EndpointMiddleware": "Information",
      // サービス間メッセージ
      "Shared.Messaging.MessagingLogger": "Trace"
    }
  },
  // Azure ADに接続するための設定です。Instanceの値は公で固定のため、ほとんどの場合、変更は必要ありません。
  "AzureAd": {
    "Instance": "https://login.microsoftonline.com/"
  },
  "Application": {
    "PathBase": "/",
    "ApiKey": {
      "CrmBff": { // <- キー。キーとなりえるフォーマットなら値は自由。CurrentUserの追加プロパティのキー"requester"に設定される。
        "Value": "ABCDEFGHIJKLMN1234567890", // <- APIキー。自由な値
      }
    }
  },
  "AllowedHosts": "*"
}
