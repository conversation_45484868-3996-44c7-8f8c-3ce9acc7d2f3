using Microsoft.EntityFrameworkCore;
using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessUnderstandingByBusinessCustomerId;

public class GetBusinessUnderstandingByBusinessCustomerIdSpecification : BaseSpecification<Domain.Entities.BusinessUnderstanding>
{
    public GetBusinessUnderstandingByBusinessCustomerIdSpecification(string businessCustomerId)
    {
        Query
            .Where(x => x.BusinessCustomerId == businessCustomerId)
            .Include(x => x.Management)
            .Include(x => x.FiveStepFrameWork)
            .Include(x => x.FiveForceFramework)
            .Include(x => x.BusinessCustomer)
            .Include(x => x.ManagementPlan)
            .Include(x => x.ESGAndSDGs)
            .Include(x => x.ExternalEnvironment, x => x.ThenInclude(x => x.ExternalEnvironmentMaster))
            .Include(x => x.RelationLevel)
            .Include(x => x.ThreeCAnalysis)
            .Include(x => x.SWOTAnalysis)
            .Include(x => x.CommercialDistribution)
            .Include(x => x.FamilyTree)
            .Include(x => x.BusinessUnderstandingMaterializedView)
            .AsNoTracking();
    }
}
