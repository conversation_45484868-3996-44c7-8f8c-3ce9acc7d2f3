using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussionReaction.AddBUDiscussionReaction;

public class AddBUDiscussionReactionHandler : IRequestHandler<AddBUDiscussionReactionCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;

    public AddBUDiscussionReactionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<string>> Handle(AddBUDiscussionReactionCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 更新する値を作成します。
        var newData = new Domain.Entities.BusinessUnderstandingDiscussionReaction()
        {
            // キーの値をUlidで生成します。
            Id = Ulid.NewUlid().ToString(),
            CommentId = request.CommentId,
            ReactionType = request.ReactionType,
            StaffId = request.StaffId,
            StaffName = request.StaffName,
            UpdatedDateTime = request.UpdatedDateTime,
        };

        var reactionRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingDiscussionReaction>();

        // 通知を飛ばすタスクで使う
        //var issueProjectDiscussionCommentRepo = _unitOfWork.GetRepository<Domain.Entities.IssueProjectDiscussionComment>();
        //var issueProjectDiscussionCommentGetResult = await issueProjectDiscussionCommentRepo.SingleAsync(
        //    new FindByIdSpecification<Domain.Entities.IssueProjectDiscussionComment, string>(request.CommentId))
        //    .ConfigureAwait(false);
        //if (issueProjectDiscussionCommentGetResult.IsError) return issueProjectDiscussionCommentGetResult.PreserveErrorAs<string>();
        //var issueProjectDiscussionComment = issueProjectDiscussionCommentGetResult.Get();

        //var notificationTargetUserId = issueProjectDiscussionComment.RegistrantId;

        return await reactionRepository
            .AddAsync(newData) // データを追加します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(newData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
