using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.FindCommunicationPlanByStaffId;

public class FindOurPolicyUnderstandingByStaffIdSpecification : BaseSpecification<Domain.Entities.OurPolicyUnderstanding>
{
    public FindOurPolicyUnderstandingByStaffIdSpecification(string staffId)
    {
        Query
            .Where(e => e.StaffId == staffId)
            .Where(e => e.Status != Domain.Enums.Status.Completed)
            .AsNoTracking();
    }
}
