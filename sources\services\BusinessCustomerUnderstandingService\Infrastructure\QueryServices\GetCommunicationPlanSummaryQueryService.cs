using BusinessCustomerUnderstandingService.Domain.Enums;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Services.Queries;
using Microsoft.EntityFrameworkCore;
using Nut.Results;
using Shared.Services;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;

public class GetCommunicationPlanSummaryQueryService : IGetCommunicationPlanSummaryQueryService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ICurrentDateTimeService _currentDateTimeService;

    public GetCommunicationPlanSummaryQueryService(ApplicationDbContext dbContext, ICurrentDateTimeService currentDateTimeService)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
    }

    public async Task<Result<IEnumerable<CommunicationPlanSummary>>> Handle(string businessUnderstandingId)
    {
        var baseDate = new DateTimeOffset(_currentDateTimeService.NowDateTimeOffset().ToOffset(TimeSpan.FromHours(9)).Date, TimeSpan.FromHours(9));
        // 1か月(6か月)後の23:59:59までのデータを取得するため、基準日に+1(+6)か月と1日を加えて閾値とする(閾値未満, 閾値以上で判別する)
        var withinOneMonthAddOneDay = baseDate.AddMonths(1).AddDays(1);
        var withinSixMonthAddOneDay = baseDate.AddMonths(6).AddDays(1);
        var query = _dbContext.OurPolicyUnderstandings
                        .Where(x => x.BusinessUnderstandingId == businessUnderstandingId)
                        .GroupBy(x => true)
                        .Select(
                            x => new CommunicationPlanSummary()
                            {
                                CommunicationPlanType = CommunicationPlanType.OurPolicyUnderstanding,
                                LessThan1MonthCount = x.Count(item => item.ExpiredAt < withinOneMonthAddOneDay && item.Status != Status.Completed),
                                LessThan6MonthCount = x.Count(item => item.ExpiredAt >= withinOneMonthAddOneDay && item.ExpiredAt < withinSixMonthAddOneDay && item.Status != Status.Completed),
                                GraterThanEqual6MonthCount = x.Count(item => item.ExpiredAt >= withinSixMonthAddOneDay && item.Status != Status.Completed),
                                LastRegisteredAt = x.Max(item => item.RegisteredDateTime)
                            }
                        ).AsNoTracking()
                    .Concat(
                        _dbContext.CustomerIdeasUnderstandings
                        .Where(x => x.BusinessUnderstandingId == businessUnderstandingId)
                        .GroupBy(x => true)
                        .Select(
                            x => new CommunicationPlanSummary()
                            {
                                CommunicationPlanType = CommunicationPlanType.CustomerIdeasUnderstanding,
                                LessThan1MonthCount = x.Count(item => item.ExpiredAt < withinOneMonthAddOneDay && item.Status != Status.Completed),
                                LessThan6MonthCount = x.Count(item => item.ExpiredAt >= withinOneMonthAddOneDay && item.ExpiredAt < withinSixMonthAddOneDay && item.Status != Status.Completed),
                                GraterThanEqual6MonthCount = x.Count(item => item.ExpiredAt >= withinSixMonthAddOneDay && item.Status != Status.Completed),
                                LastRegisteredAt = x.Max(item => item.RegisteredDateTime)
                            }
                        ).AsNoTracking()
                    ).Concat(
                        _dbContext.SharingOfFinances
                        .Where(x => x.BusinessUnderstandingId == businessUnderstandingId)
                        .GroupBy(x => true)
                        .Select(
                            x => new CommunicationPlanSummary()
                            {
                                CommunicationPlanType = CommunicationPlanType.SharingOfFinance,
                                // カウントは利用されないので0を代入
                                LessThan1MonthCount = 0,
                                LessThan6MonthCount = 0,
                                GraterThanEqual6MonthCount = 0,
                                LastRegisteredAt = x.Max(item => item.RegisteredDateTime)
                            }
                        ).AsNoTracking()
                    ).Concat(
                        _dbContext.HypotheticalDiscussionOfIssues
                        .Where(x => x.BusinessUnderstandingId == businessUnderstandingId)
                        .GroupBy(x => true)
                        .Select(
                            x => new CommunicationPlanSummary()
                            {
                                CommunicationPlanType = CommunicationPlanType.HypotheticalDiscussionOfIssues,
                                LessThan1MonthCount = x.Count(item => item.ExpiredAt < withinOneMonthAddOneDay && item.Status != Status.Completed),
                                LessThan6MonthCount = x.Count(item => item.ExpiredAt >= withinOneMonthAddOneDay && item.ExpiredAt < withinSixMonthAddOneDay && item.Status != Status.Completed),
                                GraterThanEqual6MonthCount = x.Count(item => item.ExpiredAt >= withinSixMonthAddOneDay && item.Status != Status.Completed),
                                LastRegisteredAt = x.Max(item => item.RegisteredDateTime)
                            }
                        ).AsNoTracking()
                    ).Concat(
                        _dbContext.ToDo
                        .Where(x => x.BusinessUnderstandingId == businessUnderstandingId)
                        .GroupBy(x => true)
                        .Select(
                            x => new CommunicationPlanSummary()
                            {
                                CommunicationPlanType = CommunicationPlanType.ToDo,
                                LessThan1MonthCount = x.Count(item => item.ExpiredAt < withinOneMonthAddOneDay && item.Status != Status.Completed),
                                LessThan6MonthCount = x.Count(item => item.ExpiredAt >= withinOneMonthAddOneDay && item.ExpiredAt < withinSixMonthAddOneDay && item.Status != Status.Completed),
                                GraterThanEqual6MonthCount = x.Count(item => item.ExpiredAt >= withinSixMonthAddOneDay && item.Status != Status.Completed),
                                LastRegisteredAt = x.Max(item => item.RegisteredDateTime)
                            }
                        ).AsNoTracking()
                    );

        return await query.ToListAsync();
    }
}
