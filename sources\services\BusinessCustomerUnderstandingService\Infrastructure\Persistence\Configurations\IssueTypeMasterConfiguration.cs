using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class IssueTypeMasterConfiguration : IEntityTypeConfiguration<IssueTypeMaster>
{
    public void Configure(EntityTypeBuilder<IssueTypeMaster> builder)
    {
        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.Name)
            .HasMaxLength(100);
    }
}
