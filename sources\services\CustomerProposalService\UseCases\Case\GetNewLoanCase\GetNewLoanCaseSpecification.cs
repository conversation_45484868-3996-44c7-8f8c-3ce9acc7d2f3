using Shared.Spec;

namespace CustomerProposalService.UseCases.Case.GetNewLoanCase;

public class GetNewLoanCaseSpecification : BaseSpecification<Domain.Entities.NewLoanCase>
{
    public GetNewLoanCaseSpecification(GetNewLoanCaseQuery request)
    {
        Query
            .Where(x => x.Id == request.Id)
            .Include(x => x.CaseUpdateInformation)
            .Include(x => x.CaseFiles)
            .Include(x => x.CaseLinks)
            .AsNoTracking();
    }
}
