using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;
using Shared.Services;
using SharedKernel.ExternalApi.MessageContract.MyCareerUser;
using SharedKernel.ExternalApi.Services.ApiClient;
using BusinessCustomerUnderstandingService.Domain.Entities;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.DeleteBusinessUnderstandingApproach;

public class DeleteBusinessUnderstandingApproachHandler : IRequestHandler<DeleteBusinessUnderstandingApproachCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDatetimeService;
    private readonly ICurrentUserService _currentUserService;
    private readonly IGeneralGetApiClient<SearchUsers, SearchUsersResult> _searchUserApiClient;

    public DeleteBusinessUnderstandingApproachHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        ICurrentDateTimeService currentDateTimeService,
        IGeneralGetApiClient<SearchUsers, SearchUsersResult> searchUserApiClient
        )
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentDatetimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
        _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
        _searchUserApiClient = searchUserApiClient ?? throw new ArgumentNullException(nameof(searchUserApiClient));
    }

    public async Task<Result<string>> Handle(DeleteBusinessUnderstandingApproachCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var now = _currentDatetimeService.NowDateTimeOffset();

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingApproach, string>();
        // 既存データを取得します。
        var getResult = await repository.GetAsync(request.Id);
        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();
        // バージョン番号は、同時実行制御を有効にするために引数で受け取ったデータの値で必ず上書きします。
        currentData.Version = request.Version;

        // 事業性理解の取得
        var bizUnderstandingRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding>();
        var getBizUnderstandingResult = await bizUnderstandingRepository.SingleAsync(
            new FindBusinessUnderstandingSpecification(currentData.CustomerIdentificationId)
            ).ConfigureAwait(false);
        if (getBizUnderstandingResult.IsError) return getBizUnderstandingResult.PreserveErrorAs<string>();
        var bizUnderstanding = getBizUnderstandingResult.Get();

        // ユーザーの取得
        var userId = (await _currentUserService.GetAsync()).UserId!;
        var getUsersResult = await _searchUserApiClient.SendAsync(new SearchUsers { });
        if (getUsersResult.IsError) return getUsersResult.PreserveErrorAs<string>();
        var users = getUsersResult.Get().ReturnSearchUsersResults;
        var userName = users.Where(u => u.UserId == userId).Select(u => u.NameKanji).SingleOrDefault();

        // 履歴の追加
        var historyRepository = _unitOfWork.GetRepository<BusinessUnderstandingHistory>();
        var history = bizUnderstanding.Histories.Any() ? bizUnderstanding.Histories.OrderByDescending(x => x.UpdatedDateTime).First() : null;
        var newHistory = new BusinessUnderstandingHistory()
        {
            Id = Ulid.NewUlid().ToString(),
            // 取引方針
            TransactionPolicy = history is not null ? history.TransactionPolicy : string.Empty,
            // 事業性理解評点
            BusinessEvaluation = history is not null ? history.BusinessEvaluation : 0,
            ManagementPlanScore = history is not null ? history.ManagementPlanScore : 0,
            ManagementScore = history is not null ? history.ManagementScore : 0,
            FiveForceScore = history is not null ? history.FiveForceScore : 0,
            FiveStepScore = history is not null ? history.FiveStepScore : 0,
            ExternalEnvironmentScore = history is not null ? history.ExternalEnvironmentScore : 0,
            ESGAndSDGsScore = history is not null ? history.ESGAndSDGsScore : 0,
            // リレーションレベル評点
            RelationLevelEvaluation = history is not null ? history.RelationLevelEvaluation : 0,
            AuthorityScore = history is not null ? history.AuthorityScore : 0,
            RelationScore = history is not null ? history.RelationScore : 0,
            DisclosureScore = history is not null ? history.DisclosureScore : 0,
            // 事業整理解の取り組み方
            ApproachType = null,
            // 更新日時等
            UpdatedDateTime = now,
            UpdaterDisplayName = userName ?? string.Empty,
            UpdaterId = userId,
            OriginalId = bizUnderstanding.Id,
        };
        await historyRepository.AddAsync(newHistory);

        // キューの作成
        var queue = new Domain.Entities.BusinessUnderstandingMaterializedViewQueue()
        {
            Id = Ulid.NewUlid().ToString(),
            BusinessUnderstandingId = bizUnderstanding.Id,
            CustomerIdentificationId = currentData.CustomerIdentificationId,
            NumberOfRetries = 0,
            UpdaterId = userId,
            UpdaterName = userName ?? string.Empty,
            UpdatedDateTime = _currentDatetimeService.NowDateTimeOffset()
        };
        var queueRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingMaterializedViewQueue>();
        await queueRepository.AddAsync(queue);

        return await repository
            .DeleteAsync(currentData) // データを削除として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
