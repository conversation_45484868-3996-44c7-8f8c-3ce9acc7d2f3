using BusinessCustomerUnderstandingService.Domain.Enums;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThread.FindBusinessUnderstandingThread;

public record FindBusinessUnderstandingThreadResult(
    string Id,
    DateTimeOffset RegisteredDateTime,
    string Title,
    string Registrant,
    string RegistrantId,
    string Description,
    string BusinessUnderstandingId,
    List<Domain.Entities.BusinessUnderstandingThreadReaction>? Reactions,
    List<Domain.Entities.BusinessUnderstandingDiscussion>? Comments,
    IEnumerable<Domain.Entities.BusinessUnderstandingThreadFile>? Files,
    string? MentionTargetsHtml,
    IEnumerable<string>? MentionTargetUserIds,
    IEnumerable<string>? MentionTargetTeamIds,
    DiscussionPurpose Purpose,
    string? Person,
    bool? IsPersonOfPower,
    DateTimeOffset? CorrespondenceDate,
    bool? IsCorporateDepositTheme,
    bool? IsFundSettlementTheme,
    string Version
);
