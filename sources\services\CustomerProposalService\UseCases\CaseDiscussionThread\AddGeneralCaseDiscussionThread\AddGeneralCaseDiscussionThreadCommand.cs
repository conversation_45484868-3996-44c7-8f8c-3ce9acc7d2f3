using CustomerProposalService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.AddGeneralCaseDiscussionThread;

[WithDefaultBehaviors]
public record AddGeneralCaseDiscussionThreadCommand(
    string CaseId,
    string ThreadName,
    string RegistrantId,
    string RegistrantName,
    Guid CustomerIdentificationId,
    string CustomerName,
    string CustomerStaffId,
    CaseDiscussionPurpose Purpose,
    string? Person,
    bool? IsPersonOfPower,
    string? MentionTargetsHtml,
    IEnumerable<string>? MentionTargetUserIds,
    IEnumerable<string>? MentionTargetTeamIds,
    IEnumerable<string>? MentionTargetTeamMemberUserIds,
    string Description,
    IEnumerable<IFormFile>? UploadFiles
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
