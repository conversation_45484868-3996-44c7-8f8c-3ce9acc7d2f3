using System.Reflection;
using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.Extensions.Logging;
using Shared.EntityFrameworkCore;
using Shared.EntityFrameworkCore.Configuration;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence;

public class ApplicationDbContextSetup : DatabaseSetup<ApplicationDbContext>
{
    private readonly ApplicationDbContext _dbContext;

    public ApplicationDbContextSetup(
        ApplicationDbContext dbContext,
        DatabaseSettings settings,
        ILoggerFactory loggerFactory)
        : base(dbContext, settings, loggerFactory.CreateLogger<DatabaseSetup<ApplicationDbContext>>())
    {
        _dbContext = dbContext;
    }
    // 指定した順番にテストデータが投入されます。また、削除時は逆順にデータが削除されます。
    protected override IEnumerable<Type> GetInsertDataTypes()
        => new Type[]
        {
            typeof(ExternalEnvironmentMaster),
            typeof(BusinessCustomer),
            typeof(BusinessUnderstanding),
            typeof(Management),
            typeof(FiveStepFrameWork),
            typeof(ManagementPlan),
            typeof(FiveForceFramework),
            typeof(ESGAndSDGs),
            typeof(RelationLevel),
            typeof(ExternalEnvironment),
            typeof(ThreeCAnalysis),
            typeof(SWOTAnalysis),
            typeof(BusinessUnderstandingDiscussion),
            typeof(BusinessUnderstandingDiscussionReaction),
            typeof(BusinessUnderstandingThread),
            typeof(BusinessUnderstandingThreadReaction),
            typeof(CommercialDistribution),
            typeof(IssueTypeMaster),
            typeof(HypotheticalDiscussionOfIssues),
            typeof(CustomerReaction),
            typeof(FamilyTree),
            typeof(FamilyTreeEdge),
            typeof(FamilyTreeNode),
            typeof(OurPolicyUnderstanding),
            typeof(OurPolicyUnderstandingThread),
            typeof(OurPolicyUnderstandingThreadReaction),
            typeof(OurPolicyUnderstandingComment),
            typeof(OurPolicyUnderstandingCommentReaction),
            typeof(CustomerIdeasUnderstanding),
            typeof(CustomerIdeasUnderstandingThread),
            typeof(CustomerIdeasUnderstandingThreadReaction),
            typeof(CustomerIdeasUnderstandingComment),
            typeof(CustomerIdeasUnderstandingCommentReaction),
            typeof(HypotheticalDiscussionOfIssuesThread),
            typeof(HypotheticalDiscussionOfIssuesThreadReaction),
            typeof(HypotheticalDiscussionOfIssuesComment),
            typeof(HypotheticalDiscussionOfIssuesCommentReaction),
            typeof(ToDo),
            typeof(ToDoThread),
            typeof(ToDoThreadReaction),
            typeof(ToDoComment),
            typeof(ToDoCommentReaction),
            typeof(SharingOfFinance),
            typeof(SharingOfFinanceThread),
            typeof(SharingOfFinanceThreadReaction),
            typeof(SharingOfFinanceComment),
            typeof(SharingOfFinanceCommentReaction),
            typeof(Domain.Entities.CustomerLink),
            typeof(ExternalEnvironmentMasterUploadProgress),
            typeof(BusinessPartner),
            typeof(BusinessPartnerIndustry),
            typeof(BusinessPartnershipAndCreativeAccountingDetail),
        };

    // エンティティのタイプごとにテストデータを取得する実装を指定
    protected override ISeedDataProvider? GetSeedDataProvider(Type entityType)
        =>
        entityType == typeof(ExternalEnvironmentMaster) ? new ExternalEnvironmentMasterSeedDataProvider() :
        entityType == typeof(BusinessCustomer) ? new BusinessCustomerSeedDataProvider() :
        entityType == typeof(BusinessUnderstanding) ? new BusinessUnderstandingSeedDataProvider() :
        entityType == typeof(BusinessUnderstandingMaterializedView) ? new BusinessUnderstandingMaterializedViewSeedDataProvider() :
        entityType == typeof(Management) ? new ManagementSeedDataProvider() :
        entityType == typeof(FiveStepFrameWork) ? new FiveStepFrameWorkSeedDataProvider() :
        entityType == typeof(ManagementPlan) ? new ManagementPlanSeedDataProvider() :
        entityType == typeof(FiveForceFramework) ? new FiveForceFrameworkSeedDataProvider() :
        entityType == typeof(ESGAndSDGs) ? new ESGAndSDGsSeedDataProvider() :
        entityType == typeof(RelationLevel) ? new RelationLevelSeedDataProvider() :
        entityType == typeof(ExternalEnvironment) ? new ExternalEnvironmentSeedDataProvider() :
        entityType == typeof(ThreeCAnalysis) ? new ThreeCAnalysisSeedDataProvider() :
        entityType == typeof(SWOTAnalysis) ? new SWOTAnalysisSeedDataProvider() :
        entityType == typeof(BusinessUnderstandingDiscussion) ? new BusinessUnderstandingDiscussionSeedDataProvider() :
        entityType == typeof(BusinessUnderstandingDiscussionReaction) ? new BusinessUnderstandingDiscussionReactionSeedDataProvider() :
        entityType == typeof(BusinessUnderstandingThread) ? new BusinessUnderstandingThreadSeedDataProvider() :
        entityType == typeof(BusinessUnderstandingThreadReaction) ? new BusinessUnderstandingThreadReactionSeedDataProvider() :
        entityType == typeof(CommercialDistribution) ? new CommercialDistributionDataProvider() :
        entityType == typeof(IssueTypeMaster) ? new IssueTypeMasterSeedDataProvider() :
        entityType == typeof(HypotheticalDiscussionOfIssues) ? new HypotheticalDiscussionOfIssuesSeedDataProvider() :
        entityType == typeof(FamilyTree) ? new FamilyTreeDataProvider() :
        entityType == typeof(FamilyTreeNode) ? new FamilyTreeNodeDataProvider() :
        entityType == typeof(FamilyTreeEdge) ? new FamilyTreeEdgeDataProvider() :
        entityType == typeof(OurPolicyUnderstanding) ? new OurPolicyUnderstandingDataProvider() :
        entityType == typeof(OurPolicyUnderstandingThread) ? new OurPolicyUnderstandingThreadDataProvider() :
        entityType == typeof(OurPolicyUnderstandingThreadReaction) ? new OurPolicyUnderstandingThreadReactionDataProvider() :
        entityType == typeof(OurPolicyUnderstandingComment) ? new OurPolicyUnderstandingCommentDataProvider() :
        entityType == typeof(OurPolicyUnderstandingCommentReaction) ? new OurPolicyUnderstandingCommentReactionDataProvider() :
        entityType == typeof(HypotheticalDiscussionOfIssuesThread) ? new HypotheticalDiscussionOfIssuesThreadDataProvider(_dbContext) :
        entityType == typeof(HypotheticalDiscussionOfIssuesThreadReaction) ? new HypotheticalDiscussionOfIssuesThreadReactionDataProvider() :
        entityType == typeof(HypotheticalDiscussionOfIssuesComment) ? new HypotheticalDiscussionOfIssuesCommentDataProvider(_dbContext) :
        entityType == typeof(HypotheticalDiscussionOfIssuesCommentReaction) ? new HypotheticalDiscussionOfIssuesCommentReactionDataProvider() :
        entityType == typeof(CustomerIdeasUnderstanding) ? new CustomerIdeasUnderstandingDataProvider() :
        entityType == typeof(CustomerIdeasUnderstandingThread) ? new CustomerIdeasUnderstandingThreadDataProvider() :
        entityType == typeof(CustomerIdeasUnderstandingThreadReaction) ? new CustomerIdeasUnderstandingThreadReactionDataProvider() :
        entityType == typeof(CustomerIdeasUnderstandingComment) ? new CustomerIdeasUnderstandingCommentDataProvider() :
        entityType == typeof(CustomerIdeasUnderstandingCommentReaction) ? new CustomerIdeasUnderstandingCommentReactionDataProvider() :
        entityType == typeof(ToDo) ? new ToDoDataProvider() :
        entityType == typeof(ToDoThread) ? new ToDoThreadSeedDataProvider() :
        entityType == typeof(ToDoThreadReaction) ? new ToDoThreadReactionSeedDataProvider() :
        entityType == typeof(ToDoComment) ? new ToDoCommentSeedDataProvider() :
        entityType == typeof(ToDoCommentReaction) ? new ToDoCommentReactionSeedDataProvider() :
        entityType == typeof(BusinessUnderstandingApproach) ? new BusinessUnderstandingApproachSeedDataProvider() :
        entityType == typeof(CustomerReaction) ? new CustomerReactionSeedDataProvider() :
        entityType == typeof(SharingOfFinance) ? new SharingOfFinanceSeedDataProvider() :
        entityType == typeof(SharingOfFinanceThread) ? new SharingOfFinanceThreadSeedDataProvider() :
        entityType == typeof(SharingOfFinanceThreadReaction) ? new SharingOfFinanceThreadReactionSeedDataProvider() :
        entityType == typeof(SharingOfFinanceComment) ? new SharingOfFinanceCommentSeedDataProvider() :
        entityType == typeof(SharingOfFinanceCommentReaction) ? new SharingOfFinanceCommentReactionSeedDataProvider() :
        entityType == typeof(Domain.Entities.CustomerLink) ? new CustomerLinkSeedDataProvider() :
        entityType == typeof(ExternalEnvironmentMasterUploadProgress) ? new JobSeedDataProvider() :
        entityType == typeof(BusinessPartner) ? new BusinessPartnerSeedDataProvider() :
        entityType == typeof(BusinessPartnerIndustry) ? new BusinessPartnerIndustrySeedDataProvider() :
        entityType == typeof(BusinessPartnershipAndCreativeAccountingDetail) ? new BusinessPartnershipAndCreativeAccountingDetailSeedDataProvider() : null;

    private class ExternalEnvironmentMasterSeedDataProvider : ResourceCsvSeedDataProvider<ExternalEnvironmentMaster>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class BusinessCustomerSeedDataProvider : ResourceCsvSeedDataProvider<BusinessCustomer>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class BusinessUnderstandingSeedDataProvider : ResourceCsvSeedDataProvider<BusinessUnderstanding>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class BusinessUnderstandingMaterializedViewSeedDataProvider : ResourceCsvSeedDataProvider<BusinessUnderstandingMaterializedView>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class ManagementSeedDataProvider : ResourceCsvSeedDataProvider<Management>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class FiveStepFrameWorkSeedDataProvider : ResourceCsvSeedDataProvider<FiveStepFrameWork>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class ManagementPlanSeedDataProvider : ResourceCsvSeedDataProvider<ManagementPlan>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class FiveForceFrameworkSeedDataProvider : ResourceCsvSeedDataProvider<FiveForceFramework>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class ESGAndSDGsSeedDataProvider : ResourceCsvSeedDataProvider<ESGAndSDGs>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class RelationLevelSeedDataProvider : ResourceCsvSeedDataProvider<RelationLevel>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class ExternalEnvironmentSeedDataProvider : ResourceCsvSeedDataProvider<ExternalEnvironment>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class ThreeCAnalysisSeedDataProvider : ResourceCsvSeedDataProvider<ThreeCAnalysis>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class SWOTAnalysisSeedDataProvider : ResourceCsvSeedDataProvider<SWOTAnalysis>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class BusinessUnderstandingDiscussionSeedDataProvider : ResourceCsvSeedDataProvider<BusinessUnderstandingDiscussion>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class BusinessUnderstandingDiscussionReactionSeedDataProvider : ResourceCsvSeedDataProvider<BusinessUnderstandingDiscussionReaction>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class BusinessUnderstandingThreadSeedDataProvider : ResourceCsvSeedDataProvider<BusinessUnderstandingThread>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class BusinessUnderstandingThreadReactionSeedDataProvider : ResourceCsvSeedDataProvider<BusinessUnderstandingThreadReaction>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class CommercialDistributionDataProvider : ResourceCsvSeedDataProvider<CommercialDistribution>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class IssueTypeMasterSeedDataProvider : ResourceCsvSeedDataProvider<IssueTypeMaster>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class HypotheticalDiscussionOfIssuesSeedDataProvider : ResourceCsvSeedDataProvider<HypotheticalDiscussionOfIssues>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class FamilyTreeDataProvider : ResourceCsvSeedDataProvider<FamilyTree>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }
    private class FamilyTreeEdgeDataProvider : ResourceCsvSeedDataProvider<FamilyTreeEdge>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }
    private class FamilyTreeNodeDataProvider : ResourceCsvSeedDataProvider<FamilyTreeNode>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class OurPolicyUnderstandingDataProvider : ResourceCsvSeedDataProvider<OurPolicyUnderstanding>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class OurPolicyUnderstandingThreadDataProvider : ResourceCsvSeedDataProvider<OurPolicyUnderstandingThread>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class OurPolicyUnderstandingThreadReactionDataProvider : ResourceCsvSeedDataProvider<OurPolicyUnderstandingThreadReaction>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class OurPolicyUnderstandingCommentDataProvider : ResourceCsvSeedDataProvider<OurPolicyUnderstandingComment>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class OurPolicyUnderstandingCommentReactionDataProvider : ResourceCsvSeedDataProvider<OurPolicyUnderstandingCommentReaction>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class CustomerIdeasUnderstandingDataProvider : ResourceCsvSeedDataProvider<CustomerIdeasUnderstanding>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class CustomerIdeasUnderstandingThreadDataProvider : ResourceCsvSeedDataProvider<CustomerIdeasUnderstandingThread>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class CustomerIdeasUnderstandingThreadReactionDataProvider : ResourceCsvSeedDataProvider<CustomerIdeasUnderstandingThreadReaction>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class CustomerIdeasUnderstandingCommentDataProvider : ResourceCsvSeedDataProvider<CustomerIdeasUnderstandingComment>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class CustomerIdeasUnderstandingCommentReactionDataProvider : ResourceCsvSeedDataProvider<CustomerIdeasUnderstandingCommentReaction>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class HypotheticalDiscussionOfIssuesThreadDataProvider : ResourceCsvSeedDataProvider<HypotheticalDiscussionOfIssuesThread>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        private readonly ApplicationDbContext _context;

        public HypotheticalDiscussionOfIssuesThreadDataProvider(ApplicationDbContext context)
        {
            if (context is null) throw new ArgumentNullException(nameof(context));
            _context = context;
        }

        public override IEnumerable<object> GetSeedData()
        {
            var threads = base.GetSeedData().Cast<HypotheticalDiscussionOfIssuesThread>();

            // csvからはセットできないので、ここでMainIndustoryCodeをセットする
            foreach (var thread in threads)
            {
                thread.Description = Newtonsoft.Json.JsonConvert.SerializeObject(new { ops = new[] { new { insert = "あいうえこ、かきくけこ、さしすせそ" } } });
            }

            return threads;
        }


        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class HypotheticalDiscussionOfIssuesThreadReactionDataProvider : ResourceCsvSeedDataProvider<HypotheticalDiscussionOfIssuesThreadReaction>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class HypotheticalDiscussionOfIssuesCommentDataProvider : ResourceCsvSeedDataProvider<HypotheticalDiscussionOfIssuesComment>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        private readonly ApplicationDbContext _context;

        public HypotheticalDiscussionOfIssuesCommentDataProvider(ApplicationDbContext context)
        {
            if (context is null) throw new ArgumentNullException(nameof(context));
            _context = context;
        }

        public override IEnumerable<object> GetSeedData()
        {
            var comments = base.GetSeedData().Cast<HypotheticalDiscussionOfIssuesComment>();

            // csvからはセットできないので、ここでMainIndustoryCodeをセットする
            foreach (var comment in comments)
            {
                comment.Description = Newtonsoft.Json.JsonConvert.SerializeObject(new { ops = new[] { new { insert = "こめんと、こめんと、あいうえこ、かきくけこ、さしすせそ" } } });
            }

            return comments;
        }

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class HypotheticalDiscussionOfIssuesCommentReactionDataProvider : ResourceCsvSeedDataProvider<HypotheticalDiscussionOfIssuesCommentReaction>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class ToDoDataProvider : ResourceCsvSeedDataProvider<ToDo>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class BusinessUnderstandingApproachSeedDataProvider : ResourceCsvSeedDataProvider<BusinessUnderstandingApproach>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class CustomerReactionSeedDataProvider : ResourceCsvSeedDataProvider<CustomerReaction>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class ToDoCommentSeedDataProvider : ResourceCsvSeedDataProvider<ToDoComment>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class ToDoCommentReactionSeedDataProvider : ResourceCsvSeedDataProvider<ToDoCommentReaction>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    //private class ToDoCommentFileSeedDataProvider : ResourceCsvSeedDataProvider<ToDoCommentFile>
    //{
    //    protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

    //    protected override string GetResourceFileName()
    //        => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    //}

    private class ToDoThreadSeedDataProvider : ResourceCsvSeedDataProvider<ToDoThread>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class ToDoThreadReactionSeedDataProvider : ResourceCsvSeedDataProvider<ToDoThreadReaction>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    //private class ToDoThreadFileSeedDataProvider : ResourceCsvSeedDataProvider<ToDoThreadFile>
    //{
    //    protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

    //    protected override string GetResourceFileName()
    //        => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    //}

    private class SharingOfFinanceSeedDataProvider : ResourceCsvSeedDataProvider<SharingOfFinance>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class SharingOfFinanceThreadSeedDataProvider : ResourceCsvSeedDataProvider<SharingOfFinanceThread>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class SharingOfFinanceThreadReactionSeedDataProvider : ResourceCsvSeedDataProvider<SharingOfFinanceThreadReaction>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class SharingOfFinanceCommentSeedDataProvider : ResourceCsvSeedDataProvider<SharingOfFinanceComment>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class SharingOfFinanceCommentReactionSeedDataProvider : ResourceCsvSeedDataProvider<SharingOfFinanceCommentReaction>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class CustomerLinkSeedDataProvider : ResourceCsvSeedDataProvider<Domain.Entities.CustomerLink>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class JobSeedDataProvider : ResourceCsvSeedDataProvider<ExternalEnvironmentMasterUploadProgress>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }

    private class BusinessPartnerSeedDataProvider : ResourceCsvSeedDataProvider<BusinessPartner>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }
    private class BusinessPartnerIndustrySeedDataProvider : ResourceCsvSeedDataProvider<BusinessPartnerIndustry>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }
    private class BusinessPartnershipAndCreativeAccountingDetailSeedDataProvider : ResourceCsvSeedDataProvider<BusinessPartnershipAndCreativeAccountingDetail>
    {
        protected override Assembly TargetAssembly => typeof(ApplicationDbContextSetup).Assembly;

        protected override string GetResourceFileName()
            => $"{typeof(ApplicationDbContextSetup).Namespace}.SeedData.{TargetType.Name}.csv";
    }
}
