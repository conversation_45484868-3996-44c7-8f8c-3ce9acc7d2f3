using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartner.GetAllBusinessPartners;

public class GetAllBusinessPartnersSpecification : BaseSpecification<Domain.Entities.BusinessPartner>
{
    public GetAllBusinessPartnersSpecification(string businessUnderstandingId)
    {
        Query
            .Where(x => x.BusinessUnderstandingId == businessUnderstandingId)
            .Include(x => x.BusinessPartnerIndustry)
            .OrderBy(x => x.Id)
            .AsNoTracking();
    }
}
