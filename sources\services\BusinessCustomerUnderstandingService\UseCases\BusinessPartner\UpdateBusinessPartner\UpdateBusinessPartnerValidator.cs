using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartner.UpdateBusinessPartner;

public class UpdateBusinessPartnerValidator : AbstractValidator<UpdateBusinessPartnerCommand>
{
    public UpdateBusinessPartnerValidator()
    {
        RuleFor(v => v.Id).NotEmpty().MaximumLength(26);
        RuleFor(v => v.Version).NotEmpty();
        RuleFor(v => v.BusinessPartnerCustomerIdentificationId.ToString()).MaximumLength(36);
        RuleFor(v => v.BusinessPartnerCustomerName).MaximumLength(50);
        RuleFor(v => v.Note).MaximumLength(500);
        RuleFor(v => v.staffId).NotEmpty();
        RuleFor(v => v.staffName).NotEmpty();

        // 顧客識別IDか顧客名のどちらか一方のみが指定されていること(=一方は空であること)
        RuleFor(v => v)
            .Must(v => string.IsNullOrWhiteSpace(v.BusinessPartnerCustomerName) ^ v.BusinessPartnerCustomerIdentificationId is null)
            .WithMessage("顧客名と顧客識別IDの一方のみに値を指定してください");

        RuleFor(v => v.BusinessPartnerIndustry)
            .NotEmpty()
            .ChildRules(item =>
                {
                    item.RuleFor(v => v.SubIndustryCode).NotEmpty().MaximumLength(20);
                    item.RuleFor(v => v.DetailIndustryCode).NotEmpty().MaximumLength(20);
                    item.RuleFor(v => v.IndustryCode).NotEmpty().MaximumLength(20);
                }
            );
    }
}
