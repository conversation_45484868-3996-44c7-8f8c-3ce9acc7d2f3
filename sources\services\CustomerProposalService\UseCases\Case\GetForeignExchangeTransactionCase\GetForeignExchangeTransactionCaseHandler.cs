using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.GetForeignExchangeTransactionCase;

public class GetForeignExchangeTransactionCaseHandler : IRequestHandler<GetForeignExchangeTransactionCaseQuery, Result<GetForeignExchangeTransactionCaseResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetForeignExchangeTransactionCaseHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetForeignExchangeTransactionCaseResult>> Handle(GetForeignExchangeTransactionCaseQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.ForeignExchangeTransactionCase, string>();
        return repository.SingleAsync(new GetForeignExchangeTransactionCaseSpecification(request))
            .Map(v =>
                new GetForeignExchangeTransactionCaseResult
                (
                    Id: v.Id,
                    CustomerIdentificationId: v.CustomerIdentificationId,
                    CaseCategory: v.CaseCategory,
                    CaseName: v.CaseName,
                    CaseStatus: v.CaseStatus,
                    CaseOutline: v.CaseOutline,
                    ExpiredAt: v.ExpiredAt,
                    StaffId: v.StaffId,
                    StaffName: v.StaffName,
                    RegisteredAt: v.RegisteredAt,
                    CaseUpdatedAt: v.CaseUpdatedAt,
                    LastUpdatedAt: v.CaseUpdateInformation.LastUpdatedAt,
                    CaseFiles: v.CaseFiles,
                    CaseLinks: v.CaseLinks,
                    Version: v.Version,
                    ApprovalType: v.ApprovalType,
                    Amount: v.Amount,
                    RelatedEsg: v.RelatedEsg,
                    PreConsultationStandard: v.PreConsultationStandard,
                    IsEarthquakeRelated: v.IsEarthquakeRelated,
                    CollateralType: v.CollateralType,
                    CollateralOrGuaranteeCustom: v.CollateralOrGuaranteeCustom,
                    GuaranteeType: v.GuaranteeType,
                    CancelTypeOfLoan: v.CancelTypeOfLoan,
                    CancelReason: v.CancelReason,
                    Guarantors: v.Guarantors
                ));
    }
}
