using MediatR;
using Microsoft.Extensions.Logging;
using Nut.MediatR;
using Shared.UseCase.PipelineBehaviors;

namespace CustomerFixedInformationService.UseCases;
public class LoggingIncludeInOutValueBehavior<TRequest, TResponse> :
    OpenTelemetryBehavior<TRequest, TResponse> where TRequest : notnull, IRequest<TResponse>
{
    public LoggingIncludeInOutValueBehavior(ILogger<OpenTelemetryBehavior<TRequest, TResponse>> logger, IServiceProvider serviceFactory) : base(logger, serviceFactory)
    {
    }

    protected override ILoggingInOutValueCollector<TRequest, TResponse>? GetDefaultCollector()
    {
        return new ToStringInOutValueCollector<TRequest, TResponse>();
    }
}
