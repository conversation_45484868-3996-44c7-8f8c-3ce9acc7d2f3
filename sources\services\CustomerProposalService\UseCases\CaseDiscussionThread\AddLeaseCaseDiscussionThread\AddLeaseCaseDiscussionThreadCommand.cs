using CustomerProposalService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.AddLeaseCaseDiscussionThread;

[WithDefaultBehaviors]
public record AddLeaseCaseDiscussionThreadCommand(
    // LeaseCaseDiscussionThreadエンティティ
    string CaseId,
    string RegistrantId,
    string RegistrantName,
    CaseDiscussionPurpose Purpose,
    string? Person,
    bool? IsPersonOfPower,
    string? MentionTargetsHtml,
    IEnumerable<string>? MentionTargetUserIds,
    IEnumerable<string>? MentionTargetTeamIds,
    string Description,
    IEnumerable<IFormFile>? UploadFiles,
    IEnumerable<ThreadNameType> ThreadNameTypes,
    string? ThreadNameForOther,
    // 通知、ファイルアップロード用のデータ
    Guid CustomerIdentificationId,
    string CustomerName,
    string CustomerStaffId,
    IEnumerable<string>? MentionTargetTeamMemberUserIds
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
