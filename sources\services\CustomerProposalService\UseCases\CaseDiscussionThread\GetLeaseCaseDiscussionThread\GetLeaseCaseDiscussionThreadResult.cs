using CustomerProposalService.Domain.Enums;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.GetLeaseCaseDiscussionThread;

public record GetLeaseCaseDiscussionThreadResult(
    string Id,
    DateTimeOffset RegisteredAt,
    string ThreadName,
    string RegistrantId,
    string RegistrantName,
    CaseDiscussionPurpose Purpose,
    string? Person,
    bool? IsPersonOfPower,
    string? MentionTargetsHtml,
    IEnumerable<string>? MentionTargetUserIds,
    IEnumerable<string>? MentionTargetTeamIds,
    string Description,
    IEnumerable<Domain.Entities.CaseDiscussionThreadFile>? Files,
    IEnumerable<ThreadNameType> ThreadNameTypes,
    string? ThreadNameForOther,
    string Version
);
