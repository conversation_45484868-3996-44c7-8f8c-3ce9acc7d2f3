using BusinessCustomerUnderstandingService.Domain.Enums;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.AddBusinessUnderstandingApproach;

[WithDefaultBehaviors]
public record AddBusinessUnderstandingApproachCommand(
    Guid CustomerIdentificationId,
    BusinessUnderstandingApproachType ApproachType,
    string? Comment
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
