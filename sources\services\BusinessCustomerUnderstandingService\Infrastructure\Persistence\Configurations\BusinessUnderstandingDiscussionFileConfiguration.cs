using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class BUsinessUnderstandingFileConfiguration : IEntityTypeConfiguration<BusinessUnderstandingDiscussionFile>
{
    public void Configure(EntityTypeBuilder<BusinessUnderstandingDiscussionFile> builder)
    {
        builder.HasOne<BusinessUnderstandingDiscussion>()
            .WithMany(l => l.Files)
            .HasForeignKey(l => l.DiscussionId)
            .HasConstraintName("fk_business_understanding_discussion_business_understanding_discussion_file_id")
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.FileName)
            .IsRequired()
            .HasMaxLength(300);
    }
}
