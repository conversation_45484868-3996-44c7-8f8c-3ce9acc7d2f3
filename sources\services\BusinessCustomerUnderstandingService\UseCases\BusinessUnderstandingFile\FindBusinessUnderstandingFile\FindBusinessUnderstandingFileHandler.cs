using MediatR;
using Nut.Results;
using Shared.Application;
using Shared.Domain;
using BusinessCustomerUnderstandingService.Domain;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingFile.FindBusinessUnderstandingFile;

public class FindBusinessUnderstandingFileHandler : IRequestHandler<FindBusinessUnderstandingFileQuery, Result<PaginatedResult<FindBusinessUnderstandingFileResult>>>
{
    private readonly IRepository<Domain.Entities.BusinessUnderstandingFile> _repository;

    public FindBusinessUnderstandingFileHandler(IRepositoryFactory repositoryFactory)
    {
        if (repositoryFactory is null) throw new ArgumentNullException(nameof(repositoryFactory));
        _repository = repositoryFactory.GetRepository<Domain.Entities.BusinessUnderstandingFile>();
    }

    public Task<Result<PaginatedResult<FindBusinessUnderstandingFileResult>>> Handle(FindBusinessUnderstandingFileQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 検索を行います。
        var spec = new FindBusinessUnderstandingFileSpecification(request);
        var result = _repository.FindWithPaginationAsync(
            spec,
            request.ExtractSafePagenationOption(sort: new List<Sort>()
            {
                new Sort() { Target = nameof(Domain.Entities.BusinessUnderstandingFile.Id) },
            }));

        // 検索結果を FindBusinessUnderstandingFileResult にマップして返します。
        return result.Map(pr =>
            pr.Map(v => new FindBusinessUnderstandingFileResult(
                v.Id,
                v.FileName,
                v.UpdatedDateTime,
                v.UpdaterId,
                v.UpdaterName,
                v.BusinessUnderstandingId,
                v.Version)));
    }
}
