using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class BusinessUnderstandingThreadReactionConfiguration : IEntityTypeConfiguration<BusinessUnderstandingThreadReaction>
{
    public void Configure(EntityTypeBuilder<BusinessUnderstandingThreadReaction> builder)
    {
        builder.HasOne<BusinessUnderstandingThread>()
            .WithMany(l => l.Reactions)
            .HasForeignKey(j => j.ThreadId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.ThreadId)
            .HasMaxLength(26)
            .IsRequired();

        builder.Property(u => u.ReactionType)
               .IsRequired();

        builder.Property(u => u.StaffId)
                .IsRequired();

        builder.Property(u => u.StaffName)
                .IsRequired();

        builder.Property(u => u.UpdatedDateTime)
                .IsRequired();
    }
}
