using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;

public class OurPolicyUnderstandingConfiguration : IEntityTypeConfiguration<OurPolicyUnderstanding>
{
    public void Configure(EntityTypeBuilder<OurPolicyUnderstanding> builder)
    {
        builder.HasOne<BusinessUnderstanding>()
            .WithMany(l => l.OurPolicyUnderstanding)
            .HasForeignKey(j => j.BusinessUnderstandingId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(entity => entity.Id)
                .HasMaxLength(26)
                .IsRequired();

        builder.Property(entity => entity.Title)
                .HasMaxLength(64)
                .IsRequired();

        builder.Property(entity => entity.StaffId)
                .HasMaxLength(100);

        builder.Property(e => e.RegistrantId)
                .IsRequired();

        builder.Property(e => e.RegistrantName)
                .IsRequired();

        builder.Property(entity => entity.TargetPerson)
                .HasMaxLength(100);

        builder.Property(entity => entity.Description)
                .HasMaxLength(10000);

        builder.Property(entity => entity.Note)
                .HasMaxLength(10000);

        builder.Property(entity => entity.ExpiredAt)
            .IsRequired();
    }
}
