using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussionReaction.GetBUDiscussionReaction;

public class GetBUDiscussionReactionHandler : IRequestHandler<GetBUDiscussionReactionQuery, Result<GetBUDiscussionReactionResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetBUDiscussionReactionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetBUDiscussionReactionResult>> Handle(GetBUDiscussionReactionQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingDiscussionReaction, string>();
        return repository.GetAsync(request.Id)
            .Map(v => new GetBUDiscussionReactionResult(
                v.Id,
                v.CommentId,
                v.StaffId,
                v.StaffName,
                v.ReactionType,
                v.UpdatedDateTime,
                v.Version));
    }
}
