using BusinessCustomerUnderstandingService.Domain.Entities;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Services.Queries;
using BusinessCustomerUnderstandingService.UseCases.External.GetBusinessUnderstandingForCgc;
using Microsoft.EntityFrameworkCore;
using Nut.Results;
using Shared.Domain;
using Shared.Results.Errors;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices.External;
public class GetBusinessUnderstandingForCgcQueryService : IGetBusinessUnderstandingForCgcQueryService
{
    private readonly ApplicationDbContext _dbContext;

    public GetBusinessUnderstandingForCgcQueryService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    public async Task<Result<GetBusinessUnderstandingForCgcResult>> Handle(GetBusinessUnderstandingForCgcQuery request)
    {
        var materializedViewEntity = await _dbContext.BusinessUnderstandingMaterializedViews
            .AsNoTracking()
            .SingleOrDefaultAsync(x => x.CustomerIdentificationId == request.CustomerIdentificationId);

        if (materializedViewEntity is null) return Result.Error<GetBusinessUnderstandingForCgcResult>(new DataNotFoundException("事業性理解マテリアライズドビューが存在しません。"));

        var managementPlan = await CreateManagementPlan(materializedViewEntity.BusinessUnderstandingId);

        var management = await CreateManagement(materializedViewEntity.BusinessUnderstandingId);

        var fiveForceFramework = await CreateFiveForceFramework(materializedViewEntity.BusinessUnderstandingId);

        var fiveStepFrameWork = await CreateFiveStepFrameWork(materializedViewEntity.BusinessUnderstandingId);

        var esgAndSDGs = await CreateESGAndSDGs(materializedViewEntity.BusinessUnderstandingId);

        var relationLevel = await CreateRelationLevel(materializedViewEntity.BusinessUnderstandingId);

        var swotAnalysis = await CreateSWOTAnalysis(materializedViewEntity.BusinessUnderstandingId);

        var threeCAnalysis = await CreateThreeCAnalysis(materializedViewEntity.BusinessUnderstandingId);

        var threads = await CreateThreads(materializedViewEntity.BusinessUnderstandingId);

        return new GetBusinessUnderstandingForCgcResult(
            materializedViewEntity.TransactionPolicy,
            managementPlan,
            management,
            fiveForceFramework,
            fiveStepFrameWork,
            esgAndSDGs,
            relationLevel,
            swotAnalysis,
            threeCAnalysis,
            threads);
    }

    private async Task<GetBusinessUnderstandingForCgcManagementPlan> CreateManagementPlan(string businessUnderstandingId)
    {
        var entity = await _dbContext.ManagementPlans
            .AsNoTracking()
            .SingleAsync(x => x.BusinessUnderstandingId == businessUnderstandingId);
        return new GetBusinessUnderstandingForCgcManagementPlan(
            entity.ManagementPlanOverview,
            entity.ManagementPlanCurrentComment,
            entity.StatusOfAchievementOfManagementPlan,
            entity.PlanningDate,
            entity.BusinessPlanRating?.DisplayName(),
            entity.Ideal,
            entity.Issue,
            entity.UpdatedDateTime);
    }

    private async Task<GetBusinessUnderstandingForCgcManagement> CreateManagement(string businessUnderstandingId)
    {
        var entity = await _dbContext.Managements
            .AsNoTracking()
            .SingleAsync(x => x.BusinessUnderstandingId == businessUnderstandingId);
        return new GetBusinessUnderstandingForCgcManagement(
            entity.Ideal,
            entity.Issue,
            entity.MediumToLongTermVisionRating?.DisplayName(),
            entity.MediumToLongTermVisionComment,
            entity.ExperienceRating?.DisplayName(),
            entity.ExperienceComment,
            entity.ExpertiseRating?.DisplayName(),
            entity.ExpertiseComment,
            entity.CentripetalForceRating?.DisplayName(),
            entity.CentripetalForceComment,
            entity.SuccessorRating?.DisplayName(),
            entity.SuccessorComment,
            entity.UpdatedDateTime);
    }

    private async Task<GetBusinessUnderstandingForCgcFiveForceFramework> CreateFiveForceFramework(string businessUnderstandingId)
    {
        var entity = await _dbContext.FiveForceFrameworks
            .AsNoTracking()
            .SingleAsync(x => x.BusinessUnderstandingId == businessUnderstandingId);
        return new GetBusinessUnderstandingForCgcFiveForceFramework(
            entity.Ideal,
            entity.Issue,
            entity.NewComerComment,
            entity.NewComerScore?.DisplayName(),
            entity.AbilityToNegotiateWithSalesPartnersComment,
            entity.AbilityToNegotiateWithSalesPartnersScore?.DisplayName(),
            entity.AbilityToNegotiateWithSuppliersComment,
            entity.AbilityToNegotiateWithSuppliersScore?.DisplayName(),
            entity.CompetitorComment,
            entity.CompetitorScore?.DisplayName(),
            entity.SubstituteArticleComment,
            entity.SubstituteArticleScore?.DisplayName(),
            entity.UpdatedDateTime);
    }

    private async Task<GetBusinessUnderstandingForCgcFiveStepFrameWork> CreateFiveStepFrameWork(string businessUnderstandingId)
    {
        var entity = await _dbContext.FiveStepFrameWorks
            .AsNoTracking()
            .SingleAsync(x => x.BusinessUnderstandingId == businessUnderstandingId);
        return new GetBusinessUnderstandingForCgcFiveStepFrameWork(
            entity.Ideal,
            entity.Issue,
            entity.CostReductionComment,
            entity.CostReductionRating?.DisplayName(),
            entity.ManagementComment,
            entity.ManagementRating?.DisplayName(),
            entity.ICTAndBPRComment,
            entity.ICTAndBPRRating?.DisplayName(),
            entity.HumanResourcesAndEvaluationAndDevelopmentComment,
            entity.HumanResourcesAndEvaluationAndDevelopmentRating?.DisplayName(),
            entity.MarketingThinkingComment,
            entity.MarketingThinkingRating?.DisplayName(),
            entity.UpdatedDateTime);
    }

    private async Task<GetBusinessUnderstandingForCgcEsgAndSDGs> CreateESGAndSDGs(string businessUnderstandingId)
    {
        var entity = await _dbContext.ESGAndSDGs
            .AsNoTracking()
            .SingleAsync(x => x.BusinessUnderstandingId == businessUnderstandingId);

        return new GetBusinessUnderstandingForCgcEsgAndSDGs(
            entity.ConceptOfESGAndSDGs?.DisplayName(),
            entity.IsCertificationAndDeclaration,
            entity.Ideal,
            entity.Issue,
            entity.DepartmentOfEnvironmentAndManagementRating?.DisplayName(),
            entity.EffortsForGreenhouseGasEmissionsRating?.DisplayName(),
            entity.SubjectsOfCertificationAndDeclaration?.Select(x => x.DisplayName()!).ToArray(),
            entity.SubjectsOfCertificationAndDeclarationComment,
            entity.UpdatedDateTime);
    }

    private async Task<GetBusinessUnderstandingForCgcRelationLevel> CreateRelationLevel(string businessUnderstandingId)
    {
        var entity = await _dbContext.RelationLevels
            .AsNoTracking()
            .SingleAsync(x => x.BusinessUnderstandingId == businessUnderstandingId);
        return new GetBusinessUnderstandingForCgcRelationLevel(
            entity.AlreadyIdentifiedRealAuthority,
            entity.VisitorsInTheLastOneYearStaff,
            entity.VisitorsInTheLastOneYearAdministrator,
            entity.VisitorsInTheLastOneYearOfficer,
            entity.QualityOfInterviews?.DisplayName(),
            entity.InterviewComments,
            entity.FinancialsAlreadyShared?.DisplayName(),
            entity.InterviewDate,
            entity.Interviewer,
            entity.Interviewee,
            entity.ConceptOfBusinessGrowth?.DisplayName(),
            entity.FundraisingConcept?.DisplayName(),
            entity.CollateralApproach?.DisplayName(),
            entity.ConceptOfBanksNumber?.DisplayName(),
            entity.ConceptOfConsulting?.DisplayName(),
            entity.ConceptOfLeaseUseNoUse,
            entity.ConceptOfLeaseUseOtherUse,
            entity.ConceptOfLeaseUseMainUse,
            entity.AlumniDispatch,
            entity.UnderstandingOfImprovement?.DisplayName(),
            entity.UnderstandingOfOurPolicy?.DisplayName(),
            entity.RelationshipComments,
            entity.HasFinancialStatements,
            entity.HasAppraisalDetails,
            entity.HasTrialBalanceAndCashManagement,
            entity.ManagementInformation?.DisplayName(),
            entity.OwnerDisclosure?.DisplayName(),
            entity.DisclosureComments,
            entity.Policy?.DisplayName(),
            entity.AuthorityLevel,
            entity.RelationalLevel,
            entity.DisclosureLevel,
            entity.RelationLevelComment,
            entity.UpdatedDateTime);
    }

    private async Task<GetBusinessUnderstandingForCgcSwotAnalysis> CreateSWOTAnalysis(string businessUnderstandingId)
    {
        var entity = await _dbContext.SWOTAnalyses
            .AsNoTracking()
            .SingleAsync(x => x.BusinessUnderstandingId == businessUnderstandingId);
        return new GetBusinessUnderstandingForCgcSwotAnalysis(
            entity.Strengths,
            entity.Weaknesses,
            entity.Opportunities,
            entity.Threats,
            entity.UpdatedDateTime);
    }

    private async Task<GetBusinessUnderstandingForCgcThreeCAnalysis> CreateThreeCAnalysis(string businessUnderstandingId)
    {
        var entity = await _dbContext.ThreeCAnalyses
            .AsNoTracking()
            .SingleAsync(x => x.BusinessUnderstandingId == businessUnderstandingId);
        return new GetBusinessUnderstandingForCgcThreeCAnalysis(
            entity.Customer,
            entity.Company,
            entity.Competitor,
            entity.UpdatedDateTime);
    }

    private async Task<IEnumerable<GetBusinessUnderstandingForCgcThread>> CreateThreads(string businessUnderstandingId)
    {
        var threadEntities = await _dbContext.BusinessUnderstandingThreads
            .AsNoTracking()
            .Where(x => x.BusinessUnderstandingId == businessUnderstandingId)
            .ToListAsync();

        var threadIds = threadEntities.Select(x => x.Id).ToHashSet();
        var discussionDictionary = (await _dbContext.BusinessUnderstandingDiscussions
            .AsNoTracking()
            .Where(x => threadIds.Contains(x.ThreadId))
            .ToListAsync())
            .GroupBy(x => x.ThreadId)
            .ToDictionary(x => x.Key, x => x.ToList());

        var threads = new List<GetBusinessUnderstandingForCgcThread>();
        foreach (var threadEntity in threadEntities)
        {
            discussionDictionary.TryGetValue(threadEntity.Id, out var discussionEntities);
            discussionEntities ??= new List<BusinessUnderstandingDiscussion>();

            var discussions = discussionEntities.Select(x => new GetBusinessUnderstandingForCgcDiscussion(
                x.RegisteredDateTime,
                x.Registrant,
                x.Description,
                x.Purpose.DisplayName()!,
                x.Person,
                x.IsPersonOfPower)).ToArray();

            var thread = new GetBusinessUnderstandingForCgcThread(
                threadEntity.RegisteredDateTime,
                threadEntity.Registrant,
                threadEntity.Title,
                threadEntity.Description,
                threadEntity.Purpose.DisplayName()!,
                threadEntity.Person,
                threadEntity.IsPersonOfPower,
                threadEntity.CorrespondenceDate,
                discussions);

            threads.Add(thread);
        }

        return threads;
    }
}
