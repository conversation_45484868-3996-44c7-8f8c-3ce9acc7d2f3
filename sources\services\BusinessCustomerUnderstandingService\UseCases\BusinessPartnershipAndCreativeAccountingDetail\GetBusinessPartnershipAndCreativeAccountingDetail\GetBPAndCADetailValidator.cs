using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartnershipAndCreativeAccountingDetail.GetBusinessPartnershipAndCreativeAccountingDetail;
public class GetBPAndCADetailValidator : AbstractValidator<GetBPAndCADetailQuery>
{
    public GetBPAndCADetailValidator()
    {
        // どちらか一方のみが指定されていること
        RuleFor(v => v).Must(v => string.IsNullOrWhiteSpace(v.Id) ^ v.BusinessUnderstandingId is null)
            .WithMessage("IdかBusinessUnderstandingIdのどちらかを指定してください。");
        ;
    }
}
