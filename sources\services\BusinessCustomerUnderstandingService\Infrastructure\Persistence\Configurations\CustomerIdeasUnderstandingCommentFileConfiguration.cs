using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class CustomerIdeasUnderstandingCommentFileConfiguration : IEntityTypeConfiguration<CustomerIdeasUnderstandingCommentFile>
{
    public void Configure(EntityTypeBuilder<CustomerIdeasUnderstandingCommentFile> builder)
    {
        builder.HasOne<CustomerIdeasUnderstandingComment>()
            .WithMany(l => l.Files)
            .HasForeignKey(l => l.CommentId)
            .HasConstraintName("fk_customer_ideas_understanding_comment_customer_ideas_understanding_comment_file_id")
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.FileName)
            .IsRequired()
            .HasMax<PERSON>ength(300);
    }
}
