using MediatR;
using Nut.MediatR.ServiceLike;
using Nut.Results;
using Shared.UseCase.AsServiceFilters;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessCustomer.BatchAddBusinessCustomer;

[AsService("customer-understanding/batch-add-business-customer/", typeof(StripResultFilter))]
[WithDefaultBehaviors]
public record BatchAddBusinessCustomerCommand(
    List<Guid> CustomerIdentificationIdList
) : IRequest<Result<List<BatchAddBusinessCustomerResult>>> // エンティティのキーの型を設定します。
{
}
