using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class CustomerIdeasUnderstandingConfiguration : IEntityTypeConfiguration<CustomerIdeasUnderstanding>
{
    public void Configure(EntityTypeBuilder<CustomerIdeasUnderstanding> builder)
    {
        builder.HasOne<BusinessUnderstanding>()
            .WithMany(l => l.CustomerIdeasUnderstanding)
            .HasForeignKey(j => j.BusinessUnderstandingId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(e => e.Id)
                .HasMaxLength(26);

        builder.Property(e => e.Title)
                .HasMaxLength(64)
                .IsRequired();

        builder.Property(e => e.StaffId)
                .HasMaxLength(100);

        builder.Property(e => e.RegistrantId)
                .IsRequired();

        builder.Property(e => e.RegistrantName)
                .IsRequired();

        builder.Property(e => e.TargetPerson)
                .HasMaxLength(100);

        builder.Property(e => e.Description)
                .HasMaxLength(10000);

        builder.Property(e => e.Note)
                .HasMaxLength(10000);

        builder.Property(e => e.ExpiredAt)
                .IsRequired();

    }
}
