using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.AddBusinessUnderstandingApproach;

public class FindBusinessUnderstandingApproachSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingApproach>
{
    public FindBusinessUnderstandingApproachSpecification(Guid customerIdentificationId)
    {
        Query
            .Where(x => x.CustomerIdentificationId == customerIdentificationId)
            .AsNoTracking();
    }
}
