using BusinessCustomerUnderstandingService.Domain.Entities;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistribution.GetCommercialDistributionWithHistory;

public record GetCommercialDistributionWithHistoryResult(
    string Id,
    string CanvasColor,
    string? UpdaterId,
    string? UpdaterName,
    DateTimeOffset? UpdatedDateTime,
    List<CommercialDistributionNode> Nodes,
    List<CommercialDistributionEdge> Edges,
    string BusinessUnderstandingId,
    string Version,
    IEnumerable<CommercialDistributionHistory> Histories
);
