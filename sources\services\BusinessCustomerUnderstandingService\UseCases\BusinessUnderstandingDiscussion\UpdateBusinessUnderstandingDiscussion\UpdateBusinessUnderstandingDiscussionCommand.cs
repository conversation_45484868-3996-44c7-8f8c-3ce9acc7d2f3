using BusinessCustomerUnderstandingService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussion.UpdateBusinessUnderstandingDiscussion;

[WithDefaultBehaviors]
public record UpdateBusinessUnderstandingDiscussionCommand(
    string Id,
    string Registrant,
    string RegistrantId,
    string Description,
    string CustomerName,
    string ThreadTitle,
    IEnumerable<string>? MentionTargetUserIds,
    IEnumerable<string>? MentionTargetTeamIds,
    IEnumerable<string>? MentionTargetTeamMemberUserIds,
    DiscussionPurpose Purpose,
    string? Person,
    bool? IsPersonOfPower,
    bool? IsCorporateDepositTheme,
    bool? IsFundSettlementTheme,
    string Version,
    IEnumerable<IFormFile> UploadFiles,
    IEnumerable<string> FilesToRemove
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
