using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Domain.Dto.BusinessUnderstanding;
using BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.FindBusinessUnderstandingApproach;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessUnderstandingWithHistory;

public class GetBusinessUnderstandingWithHistoryHandler : IRequestHandler<GetBusinessUnderstandingWithHistoryQuery, Result<GetBusinessUnderstandingWithHistoryResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetBusinessUnderstandingWithHistoryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<GetBusinessUnderstandingWithHistoryResult>> Handle(GetBusinessUnderstandingWithHistoryQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var spec = new GetBusinessUnderstandingWithHistorySpecification(request.Id);
        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding, string>();
        var getResult = await repository.SingleAsync(spec).ConfigureAwait(false);
        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<GetBusinessUnderstandingWithHistoryResult>();

        var bizUnderstanding = getResult.Get();

        // 評点取得
        var evaluation = new CalculationResult(
            bizUnderstanding.ManagementPlan!,
            bizUnderstanding.Management!,
            bizUnderstanding.FiveForceFramework!,
            bizUnderstanding.FiveStepFrameWork!,
            bizUnderstanding.ExternalEnvironment!,
            bizUnderstanding.ESGAndSDGs!,
            0,
            bizUnderstanding.RelationLevel!);

        // 事業性理解の取り組み方
        var approachTypeRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingApproach, string>();
        var query = new FindBusinessUnderstandingApproachQuery() { CustomerIdentificationId = bizUnderstanding.BusinessCustomer.CustomerIdentificationId };
        var approachSpec = new FindBusinessUnderstandingApproachSpecification(query);
        var result = await approachTypeRepository.FindAsync(approachSpec);
        if (result.IsError) return result.PreserveErrorAs<GetBusinessUnderstandingWithHistoryResult>();
        var approachType = result.Get().FirstOrDefault();

        return getResult.Map(v => new GetBusinessUnderstandingWithHistoryResult(
                    v.BusinessUnderstandingMaterializedView is not null ? v.BusinessUnderstandingMaterializedView.TransactionPolicy : string.Empty,
                    approachType?.ApproachType,
                    evaluation.TotalScore,
                    evaluation.ManagementPlanScore,
                    evaluation.ManagementScore,
                    evaluation.FiveForceScore,
                    evaluation.FiveStepScore,
                    evaluation.ExternalEnvironmentScore,
                    evaluation.ESGAndSDGsScore,
                    evaluation.MinimumRelationLevel,
                    evaluation.AuthorityScore,
                    evaluation.RelationScore,
                    evaluation.DisclosureScore,
                    v.BusinessUnderstandingMaterializedView is not null ? v.BusinessUnderstandingMaterializedView.UpdatedDateTime : null,
                    v.BusinessUnderstandingMaterializedView is not null ? v.BusinessUnderstandingMaterializedView.UpdaterName : string.Empty,
                    v.BusinessUnderstandingMaterializedView is not null ? v.BusinessUnderstandingMaterializedView.UpdaterId : string.Empty,
                    v.Histories));
    }
}
