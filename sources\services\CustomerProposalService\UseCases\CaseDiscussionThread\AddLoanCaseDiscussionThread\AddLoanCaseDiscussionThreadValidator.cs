using FluentValidation;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.AddLoanCaseDiscussionThread;

public class AddLoanCaseDiscussionThreadValidator : AbstractValidator<AddLoanCaseDiscussionThreadCommand>
{
    public AddLoanCaseDiscussionThreadValidator()
    {
        RuleFor(v => v.CaseId).NotEmpty();
        RuleFor(v => v.ThreadName).NotEmpty().MaximumLength(50);
        RuleFor(v => v.CustomerIdentificationId).NotEmpty();
        RuleFor(v => v.CustomerName).NotEmpty();
        RuleFor(v => v.RegistrantName).NotEmpty();
        RuleFor(v => v.RegistrantId).NotEmpty();
        RuleFor(v => v.Purpose).IsInEnum().NotEqual(Domain.Enums.CaseDiscussionPurpose.Undefined);
        RuleFor(v => v.Person).MaximumLength(20);
        RuleFor(v => v.ReasonForGuaranty).NotEmpty().MaximumLength(500);
        RuleFor(v => v.HowToImproveForGuaranty).NotEmpty().MaximumLength(500);
        RuleFor(v => v.Description).NotEmpty().MaximumLength(10000);
    }
}
