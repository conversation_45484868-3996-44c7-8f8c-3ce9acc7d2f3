using BusinessCustomerUnderstandingService.UseCases.UserBusinessCustomerIdentificationSummary.FindUserBusinessCustomerIdentificationSummary;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.Services.Queries;

public interface IFindUserBusinessCustomerIdentificationSummaryQueryService
{
    Task<Result<FindUserBusinessCustomerIdentificationSummaryResult>> Handle(
        FindUserBusinessCustomerIdentificationSummaryQuery request);
}
