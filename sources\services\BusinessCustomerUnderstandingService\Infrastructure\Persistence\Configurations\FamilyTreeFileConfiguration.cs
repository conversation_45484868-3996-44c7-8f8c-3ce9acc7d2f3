using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;

public class FamilyTreeFileConfiguration : IEntityTypeConfiguration<FamilyTreeFile>
{
    public void Configure(EntityTypeBuilder<FamilyTreeFile> builder)
    {
        builder.HasOne<BusinessUnderstanding>()
            .WithMany(l => l.FamilyTreeFile)
            .HasForeignKey(j => j.BusinessUnderstandingId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);
    }
}
