using CustomerProposalService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.UpdateLeaseCaseDiscussionThread;

[WithDefaultBehaviors]
public record UpdateLeaseCaseDiscussionThreadCommand(
    string Id,
    Guid CustomerIdentificationId,
    string CustomerName,
    string CustomerStaffId,
    string RegistrantId,
    IEnumerable<ThreadNameType> ThreadNameTypes,
    string? ThreadNameForOther,
    CaseDiscussionPurpose Purpose,
    string? Person,
    bool? IsPersonOfPower,
    string? MentionTargetsHtml,
    IEnumerable<string>? MentionTargetUserIds,
    IEnumerable<string>? MentionTargetTeamIds,
    IEnumerable<string>? MentionTargetTeamMemberUserIds,
    string Description,
    IEnumerable<IFormFile>? UploadFiles,
    IEnumerable<string>? FilesToRemove,
    string Version
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
