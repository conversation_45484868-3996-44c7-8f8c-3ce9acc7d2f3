using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionReply.GetCaseDiscussionReply;

public class GetCaseDiscussionReplyHandler : IRequestHandler<GetCaseDiscussionReplyQuery, Result<GetCaseDiscussionReplyResult>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICaseDiscussionReplyUtility _utility;

    public GetCaseDiscussionReplyHandler(
        IUnitOfWork unitOfWork,
        ICaseDiscussionReplyUtility utility)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _utility = utility ?? throw new ArgumentNullException(nameof(utility));
    }

    public async Task<Result<GetCaseDiscussionReplyResult>> Handle(GetCaseDiscussionReplyQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CaseDiscussionReply, string>();
        var repositoryResult = await repository.SingleAsync(new GetCaseDiscussionReplySpecification(request));
        if (repositoryResult.IsError) return Result.Error<GetCaseDiscussionReplyResult>(repositoryResult.GetError());

        var data = repositoryResult.Get();

        // 協議返信コメントを再取得して返却
        var newDelta = await _utility.GetArrangedDeltaWhenPublish(data.Description);
        if(newDelta.IsError) return Result.Error<GetCaseDiscussionReplyResult>(newDelta.GetError());

        return new GetCaseDiscussionReplyResult(
                data.Id,
                data.RegisteredAt,
                data.RegistrantId,
                data.RegistrantName,
                data.MentionTargetUserIds,
                data.MentionTargetTeamIds,
                newDelta.Get(),
                data.Files,
                data.Version
               );
    }
}
