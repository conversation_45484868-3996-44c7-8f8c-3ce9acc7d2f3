using Shared.Spec;

namespace CustomerProposalService.UseCases.Case.GetOtherLoanCase;

public class GetOtherLoanCaseSpecification : BaseSpecification<Domain.Entities.OtherLoanCase>
{
    public GetOtherLoanCaseSpecification(GetOtherLoanCaseQuery request)
    {
        Query
            .Where(x => x.Id == request.Id)
            .Include(x => x.CaseUpdateInformation)
            .Include(x => x.CaseFiles)
            .Include(x => x.CaseLinks)
            .AsNoTracking();
    }
}
