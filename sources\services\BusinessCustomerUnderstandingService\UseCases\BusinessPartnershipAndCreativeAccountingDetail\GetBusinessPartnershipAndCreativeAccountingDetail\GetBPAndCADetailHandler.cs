using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;
using Shared.Results.Errors;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartnershipAndCreativeAccountingDetail.GetBusinessPartnershipAndCreativeAccountingDetail;

public class GetBPAndCADetailHandler : IRequestHandler<GetBPAndCADetailQuery, Result<GetBPAndCADetailResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetBPAndCADetailHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<GetBPAndCADetailResult>> Handle(GetBPAndCADetailQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessPartnershipAndCreativeAccountingDetail>();
        var spec = new GetBPAndCADetailSpecification(request.Id, request.BusinessUnderstandingId);
        var findResult = await repository.FindAsync(spec).ConfigureAwait(false);
        if (findResult.IsError) return findResult.PreserveErrorAs<GetBPAndCADetailResult>();

        var item = findResult.Get().FirstOrDefault();
        if (item is null) return Result.Error<GetBPAndCADetailResult>(new DataNotFoundException());

        var result = new GetBPAndCADetailResult(
            Id: item!.Id,
            BusinessUnderstandingId: item.BusinessUnderstandingId,
            HasBusinessPartnershipWithOurCompany: item.HasBusinessPartnershipWithOurCompany,
            FeatureOfBusinessPartnership: item.FeatureOfBusinessPartnership,
            HasCreativeAccountingIncident: item.HasCreativeAccountingIncident,
            DescriptionOfCreativeAccountingIncident: item.DescriptionOfCreativeAccountingIncident,
            Version: item.Version);

        return Result.Ok(result);
    }
}
