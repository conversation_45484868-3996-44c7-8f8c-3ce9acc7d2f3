using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;

public class SharingOfFinanceConfiguration : IEntityTypeConfiguration<SharingOfFinance>
{
    public void Configure(EntityTypeBuilder<SharingOfFinance> builder)
    {
        builder.HasOne<BusinessUnderstanding>()
            .WithMany(l => l.SharingOfFinance)
            .HasForeignKey(j => j.BusinessUnderstandingId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(entity => entity.Id)
                .HasMaxLength(26);

        builder.Property(entity => entity.Title)
                .HasMaxLength(64);

        builder.Property(entity => entity.Content)
                .HasMaxLength(10000);

        builder.Property(entity => entity.StaffId)
                .HasMaxLength(100);

        builder.Property(e => e.RegistrantId)
                .IsRequired();

        builder.Property(e => e.RegistrantName)
                .IsRequired();

        builder.Property(entity => entity.TargetPerson)
                .HasMaxLength(100);

        builder.Property(entity => entity.Note)
                .HasMaxLength(10000);
    }
}
