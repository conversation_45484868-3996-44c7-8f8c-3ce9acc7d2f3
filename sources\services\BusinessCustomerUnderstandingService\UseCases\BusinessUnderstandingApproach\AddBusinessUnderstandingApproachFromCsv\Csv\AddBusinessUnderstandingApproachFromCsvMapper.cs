using System.Text;
using CsvHelper;
using CsvHelper.Configuration;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.AddBusinessUnderstandingApproachFromCsv.Csv;

public class AddBusinessUnderstandingApproachFromCsvMapper : IAddBusinessUnderstandingApproachFromCsvMapper
{
    public IEnumerable<AddBusinessUnderstandingApproachFromCsvDto> Map(MemoryStream memoryStream)
    {
        using var memory = memoryStream;
        memory.Position = 0;
        using var sr = new StreamReader(memory, Encoding.GetEncoding("Shift-JIS"));
        using var csv = new CsvReader(sr, new CsvConfiguration(new System.Globalization.CultureInfo("ja-JP", false)) { HasHeaderRecord = true });
        csv.Context.RegisterClassMap<AddBusinessUnderstandingApproachFromCsvMap>();

        var records = csv.GetRecords<AddBusinessUnderstandingApproachFromCsvDto>().ToList();

        return records;
    }
}
