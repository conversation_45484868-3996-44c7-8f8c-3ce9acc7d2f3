namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingSummary.GetBusinessUnderstandingSummary;

public record GetBusinessUnderstandingSummaryResult
{
    public string Id { get; init; } = default!;
    public string? TransactionPolicy { get; init; } = default;
    public string? Allowance { get; init; } = default;
    public string? FinancialsAlreadyShared { get; init; } = default;
    public DateTime? FinanceSharedAt { get; init; } = default;
    public string? ManagementPlan { get; init; } = default;
    public DateTime? CommercialDistributionUpdatedAt { get; init; } = default;
    public IEnumerable<CommunicationPlanCount> CommunicationPlanCounts { get; init; } = default!;
}

public record CommunicationPlanCount
{
    public string Category { get; init; } = default!;
    public int LessThan1MonthCount { get; init; } = default!;
    public int LessThan6MonthCount { get; init; } = default!;
    public int GraterThanEqual6MonthCount { get; init; } = default!;
}
