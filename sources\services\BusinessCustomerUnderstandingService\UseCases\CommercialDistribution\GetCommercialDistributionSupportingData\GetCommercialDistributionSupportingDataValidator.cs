using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistribution.GetCommercialDistributionSupportingData;

public class GetCommercialDistributionSupportingDataValidator : AbstractValidator<GetCommercialDistributionSupportingDataQuery>
{
    public GetCommercialDistributionSupportingDataValidator()
    {
        RuleFor(v => v.BusinessUnderstandingId).NotEmpty();
        RuleFor(v => v.BranchNumber).NotEmpty().MaximumLength(3);
        RuleFor(v => v.CifNumber).NotEmpty().MaximumLength(8);
    }
}
