using Dto = BusinessCustomerUnderstandingService.Domain.Dto.BusinessUnderstanding;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessUnderstandingByBusinessCustomerId;

public record GetBusinessUnderstandingByBusinessCustomerIdResult(
    string Id,
    Dto.ManagementPlan ManagementPlan,
    Dto.Management Management,
    Dto.FiveForceFramework FiveForceFramework,
    Dto.FiveStepFrameWork FiveStepFrameWork,
    Dto.ESGAndSDGs ESGAndSDGs,
    Dto.ExternalEnvironment ExternalEnvironment,
    Dto.RelationLevel RelationLevel,
    Dto.ThreeCAnalysis ThreCAnalysis,
    Dto.SWOTAnalysis SWOTAnalysis,
    Dto.CommercialDistribution CommercialDistribution,
    Dto.FamilyTree FamilyTree,
    string TransactionPolicy,
    string? TransactionPolicyConfirmerId,
    DateTimeOffset? TransactionPolicyConfirmedDateTime,
    string BusinessCustomerId,
    string Version
);
