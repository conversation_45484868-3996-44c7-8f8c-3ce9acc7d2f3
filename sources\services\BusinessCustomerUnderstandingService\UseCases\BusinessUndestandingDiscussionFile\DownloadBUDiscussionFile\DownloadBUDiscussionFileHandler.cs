using BusinessCustomerUnderstandingService.Infrastructure.Storage;
using MediatR;
using Nut.Results;
using Shared.ObjectStorage;
using Shared.Results.Errors;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUndestandingDiscussionFile.DownloadBUDiscussionFile;

public class DownloadBUDiscussionFileHandler : IRequestHandler<DownloadBUDiscussionFileQuery, Result<DownloadBUDiscussionFileResult>>
{
    private readonly IBusinessUnderstandingStorageClientProvider _objectStorageClientProvider;

    private readonly string _folderName = "discussion";

    public DownloadBUDiscussionFileHandler(IBusinessUnderstandingStorageClientProvider objectStorageClientProvider)
    {
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
    }

    public async Task<Result<DownloadBUDiscussionFileResult>> Handle(DownloadBUDiscussionFileQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // ObjectStorageのクライアントを作成します。
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        // 保存されているオブジェクトの一覧を取得
        var objects = await storageClient.GetObjectInformationsAsync($"{_folderName}/{request.DiscussionId}/").ConfigureAwait(false);
        if (objects.IsError) return objects.PreserveErrorAs<DownloadBUDiscussionFileResult>();

        // 指定されたファイルを取得
        var target = objects.Get().Where(e => e.Name == $"{_folderName}/{request.DiscussionId}/{request.FileName}").FirstOrDefault();
        if (target is null) return Result.Error<DownloadBUDiscussionFileResult>(new DataNotFoundException());

        var result = await storageClient.GetAsync(target.Name);
        return result.Map(s => new DownloadBUDiscussionFileResult(s));
    }
}
