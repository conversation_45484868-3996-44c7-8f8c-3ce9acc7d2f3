using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Infrastructure.Storage;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThreadFile.UploadBUThreadFile;

public class UploadBUThreadFileHandler : IRequestHandler<UploadBUThreadFileCommand, Result<List<Domain.Entities.BusinessUnderstandingThreadFile>>>
{
    private readonly IBusinessUnderstandingStorageClientProvider _objectStorageClientProvider;
    private readonly IUnitOfWork _unitOfWork;

    private readonly string _folderName = "thread";

    public UploadBUThreadFileHandler(
        IBusinessUnderstandingStorageClientProvider objectStorageClientProvider,
        IUnitOfWork unitOfWork
        )
    {
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<List<Domain.Entities.BusinessUnderstandingThreadFile>>> Handle(UploadBUThreadFileCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var uploadFiles = (request.UploadFiles) ?? new List<Microsoft.AspNetCore.Http.IFormFile>();
        var selectedFilesNames = (request.SelectedFileNames) ?? new List<string>();

        // Blobとの接続を確立
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        // Blobに登録
        foreach (var file in uploadFiles)
        {
            var stream = file.OpenReadStream();
            var addResult = await storageClient.PostAsync($"{_folderName}/{request.ThreadId}/{file.FileName}", stream).ConfigureAwait(false);
            if (addResult.IsError) return addResult.PreserveErrorAs<List<Domain.Entities.BusinessUnderstandingThreadFile>>();
        }

        // 添付ファイル情報を取得
        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingThreadFile>();
        var spec = new UploadBUThreadFileSpecification(request.ThreadId);
        var result = await repository.FindAsync(spec);
        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (result.IsError) return result.PreserveErrorAs<List<Domain.Entities.BusinessUnderstandingThreadFile>>();

        var currentFiles = result.Get();

        // テーブルに存在しないファイル情報を追加
        foreach (var fileName in selectedFilesNames)
        {
            var currentFile = currentFiles.FirstOrDefault(x => x.FileName == fileName);
            if (currentFile != null) continue;

            var newFile = new Domain.Entities.BusinessUnderstandingThreadFile()
            {
                Id = Ulid.NewUlid().ToString(),
                FileName = fileName,
                ThreadId = request.ThreadId,
                UpdatedDateTime = DateTimeOffset.Now,
                UpdaterId = request.UpdaterId,
                UpdaterName = request.UpdaterName,
            };

            var addResult = await repository.AddAsync(newFile).ConfigureAwait(false);
            if (addResult.IsError) return addResult.PreserveErrorAs<List<Domain.Entities.BusinessUnderstandingThreadFile>>();
        }

        // 選択したファイルに存在しないファイルとファイル情報を削除
        foreach (var file in currentFiles)
        {
            var selectedFileName = selectedFilesNames.FirstOrDefault(x => x == file.FileName);
            if (selectedFileName != null) continue;

            var deleteResult = await repository.DeleteAsync(file).ConfigureAwait(false);
            if (deleteResult.IsError) return deleteResult.PreserveErrorAs<List<Domain.Entities.BusinessUnderstandingThreadFile>>();

            var deleteFileResult = await storageClient.DeleteAsync($"{_folderName}/{request.ThreadId}/{file.FileName}").ConfigureAwait(false);
            if (deleteFileResult.IsError) return deleteFileResult.PreserveErrorAs<List<Domain.Entities.BusinessUnderstandingThreadFile>>();
        }

        // 保存
        var saveResult = await _unitOfWork.SaveEntitiesAsync().ConfigureAwait(false);
        if (saveResult.IsError) return saveResult.PreserveErrorAs<List<Domain.Entities.BusinessUnderstandingThreadFile>>();

        var updatedFiles = await repository.FindAsync(new UploadBUThreadFileSpecification(request.ThreadId));

        return updatedFiles;
    }
}
