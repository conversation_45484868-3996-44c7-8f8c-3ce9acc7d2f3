using System.Drawing;
using System.Drawing.Imaging;
using System.Text.RegularExpressions;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.Services.Domain;

public class QuillDeltaImageCompressService : IQuillDeltaImageCompressService
{

    // 画像の許容幅・高さ(500x500[px])
    private readonly uint _allowImageWidth = 500;
    private readonly uint _allowImageHeight = 500;
    // gif画像の許容サイズ(KB)
    private readonly uint _allowImageDataSize = 1024 * 500;
    private readonly string _imageStringPattern = @"""data:(?<mimeType>image/[a-zA-z/]+);base64,(?<base64string>[a-zA-Z0-9+/=]+)""";

    public Result<string> ConvertToImageCompressedDelta(string delta)
    {
        return ConvertToImageCompressedDelta(delta, _allowImageWidth, _allowImageHeight);
    }

    public Result<string> ConvertToImageCompressedDelta(string delta, uint allowImageWidth, uint allowImageHeight)
    {
        // base64形式の画像情報を抜き出す
        var extractResults = ExtractBase64StringImageFromDelta(delta);

        var convertedDelta = delta;
        foreach (var mimeAndImage in extractResults)
        {
            var convertResult = CompressBase64StringImage(mimeAndImage.MimeType, mimeAndImage.Base64String, allowImageWidth, allowImageHeight);
            if (convertResult.IsError)
            {
                return convertResult;
            }

            convertedDelta = convertedDelta.Replace(mimeAndImage.Base64String, convertResult.Get());
        }

        return Result.Ok(convertedDelta);
    }

    private IEnumerable<Base64StringImageInfo> ExtractBase64StringImageFromDelta(string originalDelta)
    {
        var extractBase64stringResults = new List<Base64StringImageInfo>();
        var matches = Regex.Matches(originalDelta, _imageStringPattern);

        foreach (var match in matches.AsEnumerable())
        {
            if (!match.Groups.ContainsKey("mimeType") || !match.Groups.ContainsKey("base64string"))
            {
                continue;
            }

            extractBase64stringResults.Add(new() { MimeType = match.Groups["mimeType"].Value, Base64String = match.Groups["base64string"].Value });
        }

        return extractBase64stringResults;
    }

    private Result<string> CompressBase64StringImage(string mimeType, string base64ImageString, uint allowWidth, uint allowHeight)
    {
        // base64から画像に変換
        var decodedBytes = Convert.FromBase64String(base64ImageString);
        using (var memoryStream = new MemoryStream(decodedBytes, 0, decodedBytes.Length))
        {
            memoryStream.Write(decodedBytes, 0, decodedBytes.Length);
            using (var decodedImage = Image.FromStream(memoryStream, true))
            {
                var originalSize = decodedImage.Size;
                var originalImageFormat = new ImageFormat(ImageCodecInfo.GetImageEncoders().Where(info => info.MimeType == mimeType).First().FormatID);

                // gif画像の場合は無変換で返す(リサイズ・Jpeg変換を実施するとアニメーションが失われるため)
                // ただしデータサイズが超過している場合は、エラーを返す
                if (ImageFormat.Gif.Equals(originalImageFormat))
                {
                    if (decodedBytes.Length > _allowImageDataSize)
                    {
                        return Result.Error<string>("Gif画像のデータサイズが大きいため保存できませんでした(上限500KB)");
                    }

                    return Result.Ok(base64ImageString);
                }

                // 幅・高さそれぞれで許容サイズに収めるための縮小率を計算
                var resizeRateWidth = allowWidth / (float)originalSize.Width;
                var resizeRateHeight = allowHeight / (float)originalSize.Height;

                // 縮小率がともに1以上の場合(許容サイズより画像サイズが小さい場合)
                // 縮小せずにJpeg変換のみ実施してreturn
                if (resizeRateWidth >= 1f && resizeRateHeight >= 1f)
                {
                    // jpeg画像は変換せずに返す
                    if (ImageFormat.Jpeg.Equals(originalImageFormat))
                    {
                        return Result.Ok(base64ImageString);
                    }

                    return Result.Ok(ConvertJpegImageString(decodedImage));
                }

                // より縮小率の高い方を採用する
                var resizeRatio = Math.Min(resizeRateWidth, resizeRateHeight);

                var resizedImage = new Bitmap(decodedImage, new Size((int)(originalSize.Width * resizeRatio), (int)(originalSize.Height * resizeRatio)));

                return Result.Ok(ConvertJpegImageString(resizedImage));
            }
        }
    }

    /// <summary>
    /// 画像をJpegに変換したうえでBase64文字列として情報を返す
    /// </summary>
    private string ConvertJpegImageString(Image image)
    {

        using (var memoryStream = new MemoryStream())
        {
            image.Save(memoryStream, ImageFormat.Jpeg);
            return Convert.ToBase64String(memoryStream.ToArray());
        }
    }

    private record Base64StringImageInfo
    {
        public string MimeType { get; init; } = default!;
        public string Base64String { get; init; } = default!;
    }
}
