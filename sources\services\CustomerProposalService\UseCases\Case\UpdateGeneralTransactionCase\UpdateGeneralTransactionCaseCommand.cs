using CustomerProposalService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.UpdateGeneralTransactionCase;

[WithDefaultBehaviors]
public record UpdateGeneralTransactionCaseCommand(
    string Id,
    string CaseName,
    CaseStatus CaseStatus,
    string? CaseOutline,
    DateTimeOffset? ExpiredAt,
    string StaffId,
    string StaffName,
    IEnumerable<IFormFile>? UploadFiles,
    IEnumerable<string>? CaseLinks,
    string? GeneralTransactionTypeId,
    string Version
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
