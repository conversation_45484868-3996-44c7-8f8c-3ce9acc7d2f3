using BusinessCustomerUnderstandingService.Domain.Enums;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.CustomerIdeasUnderstanding.AddCustomerIdeasUnderstanding;

[WithDefaultBehaviors]
public record AddCustomerIdeasUnderstandingCommand(
    string? Title,
    Status? Status,
    int? Order,
    DateTimeOffset ExpiredAt,
    string? StaffId,
    string? StaffName,
    string? TargetPerson,
    string? Description,
    string? Note,
    DateTimeOffset? UpdatedDateTime,
    string? UpdaterId,
    string? UpdaterName,
    string RegistrantId,
    string RegistrantName,
    string BusinessUnderstandingId
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
