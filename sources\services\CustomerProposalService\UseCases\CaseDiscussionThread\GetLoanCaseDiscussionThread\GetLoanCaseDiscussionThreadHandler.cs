using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.GetLoanCaseDiscussionThread;

public class GetLoanCaseDiscussionThreadHandler : IRequestHandler<GetLoanCaseDiscussionThreadQuery, Result<GetLoanCaseDiscussionThreadResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetLoanCaseDiscussionThreadHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetLoanCaseDiscussionThreadResult>> Handle(GetLoanCaseDiscussionThreadQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.LoanCaseDiscussionThread, string>();
        return repository.SingleAsync(new GetLoanCaseDiscussionThreadSpecification(request))
            .Map(v =>
                new GetLoanCaseDiscussionThreadResult
                (
                    v.Id,
                    v.RegisteredAt,
                    v.ThreadName,
                    v.RegistrantId,
                    v.RegistrantName,
                    v.Purpose,
                    v.Person,
                    v.IsPersonOfPower,
                    v.ReasonForGuaranty,
                    v.HowToImproveForGuaranty,
                    v.MentionTargetsHtml,
                    v.MentionTargetUserIds,
                    v.MentionTargetTeamIds,
                    v.Description,
                    v.Files,
                    v.Version
                    ));
    }
}
