using CustomerProposalService.Domain.Enums;
using FluentValidation;

namespace CustomerProposalService.UseCases.Case.UpdateNewLeaseCase;

public class UpdateNewLeaseCaseValidator : AbstractValidator<UpdateNewLeaseCaseCommand>
{
    public UpdateNewLeaseCaseValidator()
    {
        // Case Entity
        RuleFor(e => e.Id).NotEmpty();
        RuleFor(e => e.CaseName).NotEmpty().MaximumLength(50);
        RuleFor(e => e.CaseStatus).NotEmpty().IsInEnum().NotEqual(CaseStatus.Undefined).Equal(CaseStatus.CancelAfterProposal).When(e => e.CustomerProposalResult == ProposalResult.LostOrder, ApplyConditionTo.CurrentValidator);
        RuleFor(e => e.CaseOutline).CaseOutline();
        RuleFor(e => e.StaffId).NotEmpty().MaximumLength(50);
        RuleFor(e => e.StaffName).NotEmpty().MaximumLength(50);
        RuleFor(v => v.Version).NotEmpty();
        // NewLeaseCase Entity
        RuleFor(e => e.LeaseCaseType).IsInEnum().NotEqual(LeaseCaseType.Undefined);
        RuleFor(e => e.Amount).NotNull();
        RuleForEach(x => x.LeaseSituations).ChildRules(item =>
        {
            item.RuleFor(i => i).IsInEnum().NotEqual(LeaseSituation.Undefined);
        });
        RuleFor(e => e.LeaseStaffId).MaximumLength(50);
        RuleFor(e => e.LeaseStaffName).MaximumLength(50);
        RuleFor(e => e.CancelType).IsInEnum().NotEqual(CancelType.Undefined);
        RuleFor(e => e.CancelType).NotEmpty().When(e => _customerCancelledCaseStatuses.Contains(e.CaseStatus));
        RuleFor(e => e.CancelReason).CancelReason();
        RuleFor(e => e.PropertyCategory).IsInEnum().NotEqual(PropertyCategory.Undefined);
        RuleFor(e => e.PropertyStatus).IsInEnum().NotEqual(PropertyStatus.Undefined);
        RuleForEach(x => x.PropertyContractTypes).ChildRules(item =>
        {
            item.RuleFor(i => i).IsInEnum().NotEqual(PropertyContractType.Undefined);
        });
        RuleFor(e => e.BusinessMeetingNumber).MaximumLength(15);
        RuleFor(e => e.QuotationNumber).MaximumLength(15);
        RuleFor(e => e.PaymentCycle).IsInEnum().NotEqual(PaymentCycle.Undefined);
        RuleForEach(x => x.ResidualValueSettings).ChildRules(item =>
        {
            item.RuleFor(i => i).IsInEnum().NotEqual(ResidualValueSetting.Undefined);
        });
        RuleFor(e => e.QuotationProcurement).IsInEnum().NotEqual(QuotationProcurement.Undefined);
        RuleFor(e => e.QuotationProcurement).NotEmpty().When(e => e.PropertyCategory == PropertyCategory.Car && e.ContractScheduledAt is not null);
        RuleFor(e => e.InstallationLocation).IsInEnum().NotEqual(InstallationLocation.Undefined);
        RuleFor(e => e.InstallationLocation).NotEmpty().When(e => e.ContractScheduledAt is not null);
        RuleFor(e => e.InstallationLocationCustom).MaximumLength(50);
        RuleFor(e => e.InstallationLocationCustom).NotEmpty().When(e => e.InstallationLocation == InstallationLocation.Other);
        RuleFor(e => e.QuotationNote).QuotationNote();
        RuleFor(e => e.CaseInputStatus).IsInEnum().NotEqual(WorkStatus.Undefined);
        RuleFor(e => e.ConsultationStatus).IsInEnum().NotEqual(ConsultationStatus.Undefined);
        RuleFor(e => e.QuotationCreateStaffId).MaximumLength(50);
        RuleFor(e => e.QuotationCreateStaffName).MaximumLength(50);
        RuleFor(e => e.QuotationScrutinizeStaffId).MaximumLength(50).NotEmpty().When(e => e.QuotationCreateStatus == WorkStatus.Complete, ApplyConditionTo.CurrentValidator);
        RuleFor(e => e.QuotationScrutinizeStaffName).MaximumLength(50).NotEmpty().When(e => e.QuotationCreateStatus == WorkStatus.Complete, ApplyConditionTo.CurrentValidator);
        RuleFor(e => e.QuotationCreateStatus).IsInEnum().NotEqual(WorkStatus.Undefined);
        RuleFor(e => e.QuotationScrutinizeStaffId).MaximumLength(50);
        RuleFor(e => e.QuotationScrutinizeStaffName).MaximumLength(50);
        RuleFor(e => e.QuotationScrutinizeStatus).IsInEnum().NotEqual(WorkStatus.Undefined);
        RuleFor(e => e.CustomerProposalResult).IsInEnum().NotEqual(ProposalResult.Undefined);
        RuleFor(e => e.IndividualApplicationStaffId).MaximumLength(50);
        RuleFor(e => e.IndividualApplicationStaffName).MaximumLength(50);
        RuleFor(e => e.IndividualApplicationStatus).IsInEnum().NotEqual(WorkStatus.Undefined);
        RuleFor(e => e.TrafficSource).IsInEnum().NotEqual(TrafficSource.Undefined);
    }

    private readonly List<CaseStatus> _customerCancelledCaseStatuses = new() {
        CaseStatus.CancelBeforeProposal,
        CaseStatus.CancelAfterProposal
    };
}
