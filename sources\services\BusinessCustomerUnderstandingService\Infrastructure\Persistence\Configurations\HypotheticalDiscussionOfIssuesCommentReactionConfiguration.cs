using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class HypotheticalDiscussionOfIssuesCommentReactionConfiguration : IEntityTypeConfiguration<HypotheticalDiscussionOfIssuesCommentReaction>
{
    public void Configure(EntityTypeBuilder<HypotheticalDiscussionOfIssuesCommentReaction> builder)
    {
        builder.HasOne<HypotheticalDiscussionOfIssuesComment>()
            .WithMany(l => l.Reactions)
            .HasForeignKey(j => j.CommentId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.CommentId)
            .HasMaxLength(26)
            .IsRequired();

        builder.Property(u => u.ReactionType)
               .IsRequired();

        builder.Property(u => u.StaffId)
                .IsRequired();

        builder.Property(u => u.StaffName)
                .IsRequired();

        builder.Property(u => u.UpdatedDateTime)
                .IsRequired();
    }
}
