using BusinessCustomerUnderstandingService.Services.Queries;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindBusinessUnderstanding;

public class FindBusinessUnderstandingHandler : IRequestHandler<FindBusinessUnderstandingQuery, Result<List<FindBusinessUnderstandingResult>>>
{
    private readonly IFindBusinessUnderstandingQueryService _queryService;

    public FindBusinessUnderstandingHandler(IFindBusinessUnderstandingQueryService queryService)
    {
        _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
    }

    public async Task<Result<List<FindBusinessUnderstandingResult>>> Handle(FindBusinessUnderstandingQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var customerIdentificationIds = request.CustomerIdentificationIds?.Distinct().ToList();
        // 数が多い場合は全件検索で対応する。
        var mustSearchByCustomerIdentificationIds = customerIdentificationIds is not null && customerIdentificationIds.Count() <= 3000;

        if (mustSearchByCustomerIdentificationIds)
        {
            // CustomerIdentificaiotnIdsの件数が多い場合、ループの中でwithコピーするのはパフォーマンスに影響あるのでnullにしておく
            var requestWithNullIds = request with { CustomerIdentificationIds = null };

            var searchResultList = new List<FindBusinessUnderstandingResult>();
            foreach (var chunkIds in customerIdentificationIds!.Chunk(1000))
            {
                var chunkRequest = requestWithNullIds with { CustomerIdentificationIds = chunkIds };
                var result = await _queryService.Handle(chunkRequest);
                if (result.IsError) return result;
                searchResultList.AddRange(result.Get());
            }
            return searchResultList;
        }
        else
        {
            var requestWithNullIds = request with { CustomerIdentificationIds = null };
            return await _queryService.Handle(requestWithNullIds);
        }
    }
}
