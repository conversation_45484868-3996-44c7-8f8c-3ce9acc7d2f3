
using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.GetExternallyCompletedLeaseCase;

public class GetExternallyCompletedLeaseCaseHandler : IRequestHandler<GetExternallyCompletedLeaseCaseQuery, Result<GetExternallyCompletedLeaseCaseResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetExternallyCompletedLeaseCaseHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetExternallyCompletedLeaseCaseResult>> Handle(GetExternallyCompletedLeaseCaseQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.ExternallyCompletedLeaseCase, string>();
        return repository.SingleAsync(new GetExternallyCompletedLeaseCaseSpecification(request))
            .Map(v =>
                new GetExternallyCompletedLeaseCaseResult
                (
                    v.Id,
                    v.CustomerIdentificationId,
                    v.CaseCategory,
                    v.CaseName,
                    v.CaseStatus,
                    v.CaseOutline,
                    v.ExpiredAt,
                    v.StaffId,
                    v.StaffName,
                    v.RegisteredAt,
                    v.CaseUpdatedAt,
                    v.CaseUpdateInformation.LastUpdatedAt,
                    v.CaseFiles,
                    v.CaseLinks,
                    v.IsEarthquakeRelated,
                    v.Version
                    ));
    }
}
