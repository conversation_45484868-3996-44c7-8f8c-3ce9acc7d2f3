using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThread.FindBusinessUnderstandingThread;

public class FindBusinessUnderstandingThreadSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingThread>
{
    public FindBusinessUnderstandingThreadSpecification(string? businessUnderstandingId, DateTimeOffset? registeredDateTimeFrom, DateTimeOffset? registeredDateTimeTo)
    {
        Query
        .WhereIfNotEmpty(businessUnderstandingId, e => e.BusinessUnderstandingId.StartsWith(businessUnderstandingId!))
            .WhereIf(registeredDateTimeFrom is not null, l => registeredDateTimeFrom <= l.RegisteredDateTime)
            .WhereIf(registeredDateTimeTo is not null, l => l.RegisteredDateTime <= registeredDateTimeTo)
            .Include(x => x.Discussions, x => x.ThenInclude(x => x.Files))
            .Include(x => x.Discussions, x => x.ThenInclude(x => x.Reactions))
            .Include(x => x.Files)
            .Include(x => x.Reactions)
            .AsNoTracking();
    }
}
