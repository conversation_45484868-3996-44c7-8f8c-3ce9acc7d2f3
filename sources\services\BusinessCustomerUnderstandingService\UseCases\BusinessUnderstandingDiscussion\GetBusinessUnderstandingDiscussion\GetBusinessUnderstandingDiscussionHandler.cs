using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Services.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussion.GetBusinessUnderstandingDiscussion;

public class GetBusinessUnderstandingDiscussionHandler : IRequestHandler<GetBusinessUnderstandingDiscussionQuery, Result<GetBusinessUnderstandingDiscussionResult>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IQuillImageService _quillImageService;

    public GetBusinessUnderstandingDiscussionHandler(IUnitOfWork unitOfWork, IQuillImageService quillImageService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _quillImageService = quillImageService ?? throw new ArgumentNullException(nameof(quillImageService));
    }

    public async Task<Result<GetBusinessUnderstandingDiscussionResult>> Handle(GetBusinessUnderstandingDiscussionQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingDiscussion, string>();
        var repositoryResult = await repository.SingleAsync(new GetBusinessUnderstandingDiscussionSpecification(request.Id));
        if (repositoryResult.IsError) return Result.Error<GetBusinessUnderstandingDiscussionResult>(repositoryResult.GetError());

        var data = repositoryResult.Get();

        var newDelta = await _quillImageService.GetArrangedDeltaWhenPublish(data.Description);

        return newDelta.IsError
            ? Result.Error<GetBusinessUnderstandingDiscussionResult>(newDelta.GetError())
            : (Result<GetBusinessUnderstandingDiscussionResult>)new GetBusinessUnderstandingDiscussionResult(
                data.Id,
                data.RegisteredDateTime,
                data.Registrant,
                data.RegistrantId,
                newDelta.Get(),
                data.IsCorporateDepositTheme,
                data.IsFundSettlementTheme,
                data.Reactions,
                data.Files,
                data.ThreadId,
                data.Purpose,
                data.Person,
                data.IsPersonOfPower,
                data.Version);
    }
}
