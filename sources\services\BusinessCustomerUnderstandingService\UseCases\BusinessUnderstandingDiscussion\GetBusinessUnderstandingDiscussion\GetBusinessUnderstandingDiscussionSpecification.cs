using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussion.GetBusinessUnderstandingDiscussion;
public class GetBusinessUnderstandingDiscussionSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingDiscussion>
{
    public GetBusinessUnderstandingDiscussionSpecification(string id)
    {
        Query
            .Where(e => e.Id == id)
            .Include(e => e.Files)
            .Include(e => e.Reactions)
            .AsNoTracking();
    }
}
