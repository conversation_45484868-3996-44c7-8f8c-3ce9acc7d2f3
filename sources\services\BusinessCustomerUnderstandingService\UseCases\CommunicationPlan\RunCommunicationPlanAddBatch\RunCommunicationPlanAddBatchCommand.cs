using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.RunCommunicationPlanAddBatch;

[WithDefaultBehaviors]
public record RunCommunicationPlanAddBatchCommand : IRequest<Result<string>>
{
    public string FolderName { get; init; } = default!;
    public string UpdaterId { get; init; } = default!;
    public string UpdaterName { get; init; } = default!;
}
