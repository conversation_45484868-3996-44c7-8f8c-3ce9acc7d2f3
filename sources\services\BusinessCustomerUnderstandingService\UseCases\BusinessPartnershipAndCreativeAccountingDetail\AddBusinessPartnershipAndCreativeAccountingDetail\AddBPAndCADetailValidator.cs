using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartnershipAndCreativeAccountingDetail.AddBusinessPartnershipAndCreativeAccountingDetail;

public class AddBPAndCADetailValidator : AbstractValidator<AddBPAndCADetailCommand>
{
    public AddBPAndCADetailValidator()
    {
        RuleFor(v => v.BusinessUnderstandingId).NotEmpty();
        RuleFor(v => v.FeatureOfBusinessPartnership).MaximumLength(500);
        RuleFor(v => v.DescriptionOfCreativeAccountingIncident).MaximumLength(500);
        RuleFor(v => v.StaffId).NotEmpty();
        RuleFor(v => v.StaffName).NotEmpty();
    }
}
