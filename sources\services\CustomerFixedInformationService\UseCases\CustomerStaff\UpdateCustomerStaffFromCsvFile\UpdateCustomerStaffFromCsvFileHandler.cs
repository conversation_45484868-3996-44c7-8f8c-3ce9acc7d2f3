using System.Globalization;
using System.Text;
using CsvHelper;
using CsvHelper.Configuration;
using CustomerFixedInformationService.Domain;
using CustomerFixedInformationService.Infrastructure.Persistence;
using CustomerFixedInformationService.Infrastructure.Storage;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Nut.Results;
using Shared.Messaging;
using Shared.ObjectStorage;
using Shared.Results.Errors;
using SharedKernel.ExternalApi.MessageContract.CustomerIdentifying;
using SharedKernel.ExternalApi.MessageContract.CustomerUnderstanding;
using SharedKernel.ExternalApi.MessageContract.MyCareerUser;
using SharedKernel.ExternalApi.Services.ApiClient;

namespace CustomerFixedInformationService.UseCases.CustomerStaff.UpdateCustomerStaffFromCsvFile;

public class UpdateCustomerStaffFromCsvFileHandler : IRequestHandler<UpdateCustomerStaffFromCsvFileCommand, Result>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICustomerStaffStorageClientProvider _objectStorageClientProvider;
    private readonly IMessageSender<FindCustomerByCifsQuery, IEnumerable<FindCustomerByCifsResult>> _findCustomerByCifsSender;
    private readonly IMessageSender<SearchUsers, SearchUsersResult> _searchUsersSender;
    private readonly IGeneralPostApiClient<PostBusinessUnderstandingMaterializedViewQueueQuery, PostBusinessUnderstandingMaterializedViewQueueResult> _addQueueApiClient;
    private readonly ApplicationDbContext _dbContext; // 本来はここでは利用してはダメだが、Trancateをする方法が他にないため。

    // 取り込みファイル名
    private readonly string _customerStaffCsvFileName = "customerStaff.csv";

    public UpdateCustomerStaffFromCsvFileHandler(IUnitOfWork unitOfWork,
        ICustomerStaffStorageClientProvider objectStorageClientProvider,
        IMessageSender<FindCustomerByCifsQuery, IEnumerable<FindCustomerByCifsResult>> findCustomerByCifsSender,
        IMessageSender<SearchUsers, SearchUsersResult> searchUsersSender,
        IGeneralPostApiClient<PostBusinessUnderstandingMaterializedViewQueueQuery, PostBusinessUnderstandingMaterializedViewQueueResult> addQueueApiClient,
        ApplicationDbContext dbContext)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
        _findCustomerByCifsSender = findCustomerByCifsSender ?? throw new ArgumentNullException(nameof(findCustomerByCifsSender));
        _searchUsersSender = searchUsersSender ?? throw new ArgumentNullException(nameof(searchUsersSender));
        _addQueueApiClient = addQueueApiClient ?? throw new ArgumentNullException(nameof(addQueueApiClient));
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    public async Task<Result> Handle(UpdateCustomerStaffFromCsvFileCommand request, CancellationToken cancellationToken)
    {
        var log = new StringBuilder();
        log.AppendLine($"{DateTimeOffset.Now.ToString("yyyy/MM/dd HH:mm:ss")}:顧客担当者ファイル取込開始");
        await CreateLogFile(log, request.FolderName, "Info");

        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 顧客担当者ファイル取込が既に実行中か確認
        var progressRepo = _unitOfWork.GetRepository<Domain.Entities.CustomerStaffUpdateProgress, string>();
        var activeProgressFindResult = await progressRepo.FindAsync(new FindActiveUpdateProgressSpecification()).ConfigureAwait(false);
        if (activeProgressFindResult.Get().Any())
        {
            log.AppendLine($"{DateTimeOffset.Now.ToString("yyyy/MM/dd HH:mm:ss")}:顧客担当者ファイル取込キャンセル");
            log.AppendLine($"既に顧客担当者ファイルの取込が実行されています。{request.UpdaterName}さんが実行した取込はキャンセルされました。");
            await CreateLogFile(log, request.FolderName, "Cancel");
            return Result.Ok();
        }

        // 顧客担当者取込の進捗を実行中として登録
        var progress = new Domain.Entities.CustomerStaffUpdateProgress()
        {
            Id = Ulid.NewUlid().ToString(),
            IsCompleted = false,
            ResultMessage = null,
            LatestRunDateTimeFrom = DateTime.Now,
            LatestRunDateTimeTo = null
        };
        await progressRepo.AddAsync(progress).ConfigureAwait(false);
        await _unitOfWork.SaveEntitiesAsync();

        try
        {
            // マイキャリア情報
            var mycareerUsersResult = await _searchUsersSender.SendAsync(new SearchUsers { });
            if (mycareerUsersResult.IsError) return mycareerUsersResult.PreserveErrorAs();
            var mycareerUsers = mycareerUsersResult.Get().ReturnSearchUsersResults.ToDictionary(x => x.UserId!);

            // 顧客担当者のファイルをBlobから取得
            var file = await GetFileFromBlob(request.FolderName);
            if (file.IsError)
            {
                // 進捗更新
                progress.IsCompleted = true;
                progress.ResultMessage = "取込失敗。システム開発者にお問い合わせください。";
                progress.LatestRunDateTimeTo = DateTimeOffset.Now;
                await progressRepo.UpdateAsync(progress).ConfigureAwait(false);
                await _unitOfWork.SaveEntitiesAsync();

                // ログファイル出力
                log.AppendLine($"{DateTimeOffset.Now.ToString("yyyy/MM/dd HH:mm:ss")}:顧客担当者ファイル取込失敗");
                log.AppendLine($"取込ファイルが存在しません。({request.FolderName}/{_customerStaffCsvFileName})");
                await CreateLogFile(log, request.FolderName, "Fail");
                return file.PreserveErrorAs();
            }

            // ファイルを読み込む
            using (var sr = new StreamReader(file.Get(), Encoding.UTF8))
            {
                using (var csv = new CsvReader(sr, GetCsvConfiguration()))
                {
                    var records = csv.GetRecords<CustomerStaffData>().ToList();

                    // 重複チェック
                    var duplicateItems = records.GroupBy(
                        item => new { item.BranchNumber, item.CifNumber },
                        (key, group) => new { Key = key, Count = group.Count() }
                    ).Where(group => group.Count > 1);

                    if (duplicateItems.Any())
                    {
                        // 進捗更新
                        var duplicateItem = duplicateItems.First(); // 代表して1レコードを抽出
                        var message = $"取込中止。重複する顧客が存在します。(店番：{duplicateItem.Key.BranchNumber}, CIF番号：{duplicateItem.Key.CifNumber})";
                        progress.IsCompleted = true;
                        progress.ResultMessage = message;
                        progress.LatestRunDateTimeTo = DateTimeOffset.Now;
                        await progressRepo.UpdateAsync(progress).ConfigureAwait(false);
                        await _unitOfWork.SaveEntitiesAsync();

                        log.AppendLine($"{DateTimeOffset.Now.ToString("yyyy/MM/dd HH:mm:ss")}:{message}");
                        await CreateLogFile(log, request.FolderName, "Fail");
                        return Result.Error("");
                    }

                    var cifs = records.Select(x => new Cif { BranchNumber = x.BranchNumber, CifNumber = x.CifNumber });
                    var customerList = new List<FindCustomerByCifsResult>();
                    // 5000件ずつAPIを叩く
                    foreach (var cifsChunk in cifs.Chunk(5000))
                    {
                        var findCustomersQuery = new FindCustomerByCifsQuery() { Cifs = cifsChunk };
                        var findCustomerResult = await _findCustomerByCifsSender.SendAsync(findCustomersQuery);
                        if (findCustomerResult.IsError)
                        {
                            progress.IsCompleted = true;
                            progress.ResultMessage = "取込失敗。システム開発者にお問い合わせください。";
                            progress.LatestRunDateTimeTo = DateTimeOffset.Now;
                            await progressRepo.UpdateAsync(progress).ConfigureAwait(false);
                            await _unitOfWork.SaveEntitiesAsync();

                            // ログファイル出力
                            log.AppendLine($"{DateTimeOffset.Now.ToString("yyyy/MM/dd HH:mm:ss")}:顧客担当者ファイル取込失敗");
                            log.AppendLine($"CustomerIdentificationの取得に失敗しました。");
                            await CreateLogFile(log, request.FolderName, "Fail");
                            return findCustomerResult.PreserveErrorAs();
                        }

                        var customers = findCustomerResult.Get();
                        customerList.AddRange(customers);
                    }

                    var customerIdsForQueue = new List<Guid>();
                    var newStaffs = new List<Domain.Entities.CustomerStaff>();
                    foreach (var staff in records)
                    {
                        var customer = customerList.SingleOrDefault(x => x.BranchNumber == staff.BranchNumber && x.CifNumber == staff.CifNumber);
                        if (customer is not null)
                        {
                            var staffId = staff.StaffId.PadLeft(7, '0');
                            if (!mycareerUsers.ContainsKey(staffId)) continue;

                            var newData = new Domain.Entities.CustomerStaff
                            {
                                // キーの値をUlidで生成します。
                                Id = Ulid.NewUlid().ToString(),
                                CustomerIdentificationId = customer.Id,
                                StaffId = staffId,
                            };
                            newStaffs.Add(newData);
                            customerIdsForQueue.Add(customer.Id);
                        }
                    }

                    // 物理削除
                    await _dbContext.Database.ExecuteSqlRawAsync($"TRUNCATE TABLE [customer_staffs];");
                    // 追加
                    var repository = _unitOfWork.GetRepository<Domain.Entities.CustomerStaff, string>();
                    await repository.AddRangeAsync(newStaffs).ConfigureAwait(false);

                    // 進捗更新
                    progress.IsCompleted = true;
                    progress.ResultMessage = $"取込完了";
                    progress.LatestRunDateTimeTo = DateTimeOffset.Now;
                    await progressRepo.UpdateAsync(progress).ConfigureAwait(false);

                    // DB保存
                    await _unitOfWork.SaveEntitiesAsync().ConfigureAwait(false);

                    if (customerIdsForQueue.Any())
                    {
                        // キューに登録
                        var queueResult = await _addQueueApiClient.SendAsync(
                            new PostBusinessUnderstandingMaterializedViewQueueQuery()
                            {
                                CustomerIdentificationIds = customerIdsForQueue,
                                UpdaterId = request.UpdaterId,
                                UpdaterName = request.UpdaterName
                            });

                        if (queueResult.IsError)
                        {
                            progress.IsCompleted = true;
                            progress.ResultMessage = "顧客担当者の取込は完了しましたが、事業性理解の更新に失敗しました";
                            progress.LatestRunDateTimeTo = DateTimeOffset.Now;
                            await progressRepo.UpdateAsync(progress).ConfigureAwait(false);
                            await _unitOfWork.SaveEntitiesAsync();

                            // ログファイル出力
                            log.AppendLine($"{DateTimeOffset.Now.ToString("yyyy/MM/dd HH:mm:ss")}:顧客担当者の取込は完了しましたが、事業性理解の更新に失敗しました");
                            log.AppendLine($"顧客担当者の取込は完了しましたが、事業性理解の更新に失敗しました");
                            await CreateLogFile(log, request.FolderName, "Fail");
                            return queueResult.PreserveErrorAs();
                        }
                    }

                    // 正常終了ログ出力
                    log.AppendLine($"{DateTimeOffset.Now.ToString("yyyy/MM/dd HH:mm:ss")}:顧客担当者ファイル取込完了");
                    await CreateLogFile(log, request.FolderName, "Info");
                }
            }
        }
        catch (Exception ex)
        {
            // 進捗更新
            progress.IsCompleted = true;
            progress.ResultMessage = "取込失敗。システム開発者にお問い合わせください。";
            progress.LatestRunDateTimeTo = DateTimeOffset.Now;
            await progressRepo.UpdateAsync(progress).ConfigureAwait(false);
            await _unitOfWork.SaveEntitiesAsync().ConfigureAwait(false);

            // ログ出力
            log.AppendLine($"{DateTimeOffset.Now.ToString("yyyy/MM/dd HH:mm:ss")}:顧客担当者ファイル取込失敗");
            log.AppendLine(ex.ToString());
            await CreateLogFile(log, request.FolderName, "Exception");
        }
        return Result.Ok();
    }

    /// <summary>
    /// CSVファイルの読み取りに関する設定を取得
    /// </summary>
    /// <returns></returns>
    private CsvConfiguration GetCsvConfiguration()
    {
        return new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true,
            Delimiter = ",",
            IgnoreBlankLines = true,
            Encoding = Encoding.UTF8,
            AllowComments = false,
            DetectColumnCountChanges = true,
            TrimOptions = TrimOptions.Trim,
        };
    }

    /// <summary>
    /// Blobからファイルを取得
    /// </summary>
    private async Task<Result<Stream>> GetFileFromBlob(string folderName)
    {
        // エンコード設定
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

        // ObjectStorageのクライアントを作成します。
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        // 保存されているオブジェクトの一覧を取得
        var objects = await storageClient.GetObjectInformationsAsync(folderName).ConfigureAwait(false);
        if (objects.IsError) return objects.PreserveErrorAs<Stream>();

        // 指定されたファイルを取得
        var target = objects.Get().Where(e => e.Name == $"{folderName}/{_customerStaffCsvFileName}").FirstOrDefault();
        if (target is null) return Result.Error<Stream>(new DataNotFoundException());

        return await storageClient.GetAsync(target.Name);
    }

    /// <summary>
    /// ログファイルをBlobに作成
    /// </summary>
    private async Task CreateLogFile(StringBuilder log, string folderName, string type)
    {
        // ObjectStorageのクライアントを作成。
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        var encoding = Encoding.UTF8;
        var stream = new MemoryStream(encoding.GetBytes(log.ToString()));

        await storageClient.PostAsync($"{folderName}/{type}_log.txt", stream).ConfigureAwait(false);
    }
}
