using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussionFile.UploadBUDiscussionFile;

public class UploadBUDiscussionFileValidator : AbstractValidator<UploadBUDiscussionFileCommand>
{
    public UploadBUDiscussionFileValidator()
    {
        RuleFor(v => v.DiscussionId).NotEmpty();
        RuleFor(v => v.UpdaterId).NotEmpty();
        RuleFor(v => v.UpdaterName).NotEmpty();
    }
}
