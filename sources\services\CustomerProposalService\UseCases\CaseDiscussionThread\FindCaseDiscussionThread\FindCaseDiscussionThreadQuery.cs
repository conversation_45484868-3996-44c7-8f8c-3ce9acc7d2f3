using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.FindCaseDiscussionThread;

[WithDefaultBehaviors]
public record FindCaseDiscussionThreadQuery : IRequest<Result<IEnumerable<FindCaseDiscussionThreadResult>>>
{
    public string CaseId { get; init; } = default!;
    public DateTimeOffset? FromDate { get; set; } = default!;
    public DateTimeOffset? ToDate { get; set; } = default!;
}
