using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessCustomer.BatchAddBusinessCustomer;

public class BatchAddBusinessCustomerHandler : IRequestHandler<BatchAddBusinessCustomerCommand, Result<List<BatchAddBusinessCustomerResult>>>
{
    private readonly IUnitOfWork _unitOfWork;

    public BatchAddBusinessCustomerHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<List<BatchAddBusinessCustomerResult>>> Handle(BatchAddBusinessCustomerCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 更新する値を作成します。
        var addTargetList = new List<Domain.Entities.BusinessCustomer>();
        var resultList = new List<BatchAddBusinessCustomerResult>();
        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessCustomer>();
        foreach (var customerIdentificationId in request.CustomerIdentificationIdList)
        {
            var newData = new Domain.Entities.BusinessCustomer()
            {
                // キーの値をUlidで生成します。
                Id = Ulid.NewUlid().ToString(),
                CustomerIdentificationId = customerIdentificationId,
            };

            addTargetList.Add(newData);
            resultList.Add(new BatchAddBusinessCustomerResult(newData.Id, newData.CustomerIdentificationId));
        }

        return await repository
            .AddRangeAsync(addTargetList) // データを追加します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(resultList))
            .ConfigureAwait(false);
    }
}
