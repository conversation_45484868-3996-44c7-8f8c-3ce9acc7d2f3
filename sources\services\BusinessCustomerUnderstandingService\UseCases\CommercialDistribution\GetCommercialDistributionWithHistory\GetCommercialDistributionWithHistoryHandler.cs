using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistribution.GetCommercialDistributionWithHistory;

public class GetCommercialDistributionWithHistoryHandler : IR<PERSON><PERSON><PERSON>andler<GetCommercialDistributionWithHistoryQuery, Result<GetCommercialDistributionWithHistoryResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetCommercialDistributionWithHistoryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetCommercialDistributionWithHistoryResult>> Handle(GetCommercialDistributionWithHistoryQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CommercialDistribution, string>();

        return repository.SingleAsync(new GetCommercialDistributionWithHistorySpecification(request.Id))
            .Map(v => new GetCommercialDistributionWithHistoryResult(
                    v.Id,
                    v.CanvasColor,
                    v.UpdaterId,
                    v.UpdaterName,
                    v.UpdatedDateTime,
                    v.Nodes,
                    v.Edges,                    
                    v.BusinessUnderstandingId,
                    v.Version,
                    v.Histories));
    }
}
