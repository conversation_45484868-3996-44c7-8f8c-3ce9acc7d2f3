﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "business_customers",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    customer_identification_id = table.Column<Guid>(type: "uniqueidentifier", maxLength: 36, nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_customers", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "business_understanding_approaches",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    customer_identification_id = table.Column<Guid>(type: "uniqueidentifier", maxLength: 36, nullable: false),
                    approach_type = table.Column<int>(type: "int", nullable: false),
                    comment = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_understanding_approaches", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "business_understanding_materialized_view_queues",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    business_understanding_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    customer_identification_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    number_of_retries = table.Column<int>(type: "int", nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_understanding_materialized_view_queues", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "commercial_distribution_templates",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    template_name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    canvas_color = table.Column<string>(type: "nvarchar(9)", maxLength: 9, nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_commercial_distribution_templates", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "external_environment_master_upload_progresses",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    is_completed = table.Column<bool>(type: "bit", nullable: false),
                    result_message = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    latest_run_date_time_from = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    latest_run_date_time_to = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_external_environment_master_upload_progresses", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "external_environment_masters",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    score = table.Column<int>(type: "int", nullable: false),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0")
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_external_environment_master", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "issue_type_masters",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_issue_type_masters", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "business_understandings",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    business_customer_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    transaction_policy_confirmer_id = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    transaction_policy_confirmed_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    communication_plan_count = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    transaction_policy = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: false),
                    customer_staff_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_understandings", x => x.id);
                    table.ForeignKey(
                        name: "fk_business_understandings_business_customers_business_customer_id",
                        column: x => x.business_customer_id,
                        principalTable: "business_customers",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "customer_links",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    display_name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    url = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    staff_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    business_customer_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_customer_links", x => x.id);
                    table.ForeignKey(
                        name: "fk_customer_links_business_customers_business_customer_id",
                        column: x => x.business_customer_id,
                        principalTable: "business_customers",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "commercial_distribution_template_edges",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    template_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    source_node = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    source = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    target_node = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    target = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_commercial_distribution_template_edges", x => x.id);
                    table.ForeignKey(
                        name: "fk_commercial_distribution_template_edges_commercial_distribution_templates_commercial_distribution_template_id",
                        column: x => x.template_id,
                        principalTable: "commercial_distribution_templates",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "commercial_distribution_template_nodes",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    template_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    node_type = table.Column<int>(type: "int", nullable: false),
                    left = table.Column<double>(type: "float", nullable: false),
                    top = table.Column<double>(type: "float", nullable: false),
                    width = table.Column<double>(type: "float", nullable: false),
                    height = table.Column<double>(type: "float", nullable: false),
                    title = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: true),
                    text = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    grouping_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: true),
                    color = table.Column<string>(type: "nvarchar(9)", maxLength: 9, nullable: true),
                    customer_name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    person_type = table.Column<int>(type: "int", nullable: true),
                    branch_number = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    cif_number = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: true),
                    merchandise = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    material = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    amount = table.Column<double>(type: "float", nullable: true),
                    industry = table.Column<string>(type: "nvarchar(24)", maxLength: 24, nullable: true),
                    area = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_international_business_partners = table.Column<bool>(type: "bit", nullable: true),
                    country_code = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    city_name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    note = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_commercial_distribution_template_nodes", x => x.id);
                    table.ForeignKey(
                        name: "fk_commercial_distribution_template_nodes_commercial_distribution_templates_commercial_distribution_template_id",
                        column: x => x.template_id,
                        principalTable: "commercial_distribution_templates",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "business_partners",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    business_partner_customer_identification_id = table.Column<Guid>(type: "uniqueidentifier", maxLength: 36, nullable: true),
                    business_partner_customer_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    note = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_partners", x => x.id);
                    table.ForeignKey(
                        name: "fk_business_partners_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "business_partnership_and_creative_accounting_details",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    has_business_partnership_with_our_company = table.Column<bool>(type: "bit", nullable: true),
                    feature_of_business_partnership = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    has_creative_accounting_incident = table.Column<bool>(type: "bit", nullable: true),
                    description_of_creative_accounting_incident = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_partnership_and_creative_accounting_details", x => x.id);
                    table.ForeignKey(
                        name: "fk_business_partnership_and_creative_accounting_details_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "business_understanding_files",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    file_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_understanding_files", x => x.id);
                    table.ForeignKey(
                        name: "fk_business_understanding_files_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "business_understanding_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    transaction_policy = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    business_evaluation = table.Column<int>(type: "int", nullable: false),
                    management_plan_score = table.Column<int>(type: "int", nullable: false),
                    management_score = table.Column<int>(type: "int", nullable: false),
                    five_force_score = table.Column<int>(type: "int", nullable: false),
                    five_step_score = table.Column<int>(type: "int", nullable: false),
                    external_environment_score = table.Column<int>(type: "int", nullable: false),
                    esg_and_sd_gs_score = table.Column<int>(type: "int", nullable: false),
                    relation_level_evaluation = table.Column<double>(type: "float", nullable: false),
                    authority_score = table.Column<double>(type: "float", nullable: false),
                    relation_score = table.Column<double>(type: "float", nullable: false),
                    disclosure_score = table.Column<double>(type: "float", nullable: false),
                    approach_type = table.Column<int>(type: "int", nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_display_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    original_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_understanding_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_business_understanding_histories_business_understandings_business_understanding_id",
                        column: x => x.original_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "business_understanding_materialized_views",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    customer_identification_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    communication_plan_count = table.Column<int>(type: "int", nullable: false),
                    transaction_policy = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    business_understanding_approach_type = table.Column<int>(type: "int", nullable: true),
                    customer_staff_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    transaction_policy_confirmer_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    transaction_policy_confirmed_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_understanding_materialized_views", x => x.id);
                    table.ForeignKey(
                        name: "fk_business_understanding_materialized_views_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "business_understanding_threads",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    title = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    is_corporate_deposit_theme = table.Column<bool>(type: "bit", nullable: true),
                    is_fund_settlement_theme = table.Column<bool>(type: "bit", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    purpose = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "Internal"),
                    person = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    is_person_of_power = table.Column<bool>(type: "bit", nullable: true),
                    correspondence_date = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    mention_targets_html = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_user_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_team_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_understanding_threads", x => x.id);
                    table.ForeignKey(
                        name: "fk_business_understanding_threads_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "commercial_distributions",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    canvas_color = table.Column<string>(type: "nvarchar(9)", maxLength: 9, nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_commercial_distributions", x => x.id);
                    table.ForeignKey(
                        name: "fk_commercial_distributions_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "customer_ideas_understandings",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    title = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: false),
                    status = table.Column<int>(type: "int", nullable: true),
                    order = table.Column<int>(type: "int", nullable: true),
                    expired_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    staff_id = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    target_person = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    description = table.Column<string>(type: "nvarchar(max)", maxLength: 10000, nullable: true),
                    note = table.Column<string>(type: "nvarchar(max)", maxLength: 10000, nullable: true),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    completed_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_customer_ideas_understandings", x => x.id);
                    table.ForeignKey(
                        name: "fk_customer_ideas_understandings_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "esg_and_sd_gs",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    concept_of_esg_and_sd_gs = table.Column<int>(type: "int", nullable: true),
                    is_certification_and_declaration = table.Column<bool>(type: "bit", nullable: true),
                    ideal = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    issue = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    esg_and_sd_gs_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    sd_gs_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    sd_gs_rating = table.Column<int>(type: "int", nullable: true),
                    environment_and_carbon_neutral_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    environment_and_carbon_neutral_rating = table.Column<int>(type: "int", nullable: true),
                    department_of_environment_and_management_rating = table.Column<int>(type: "int", nullable: true),
                    efforts_for_greenhouse_gas_emissions_rating = table.Column<int>(type: "int", nullable: true),
                    subjects_of_certification_and_declaration = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    subjects_of_certification_and_declaration_comment = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_esg_and_sd_gs", x => x.id);
                    table.ForeignKey(
                        name: "fk_esg_and_sd_gs_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "external_environments",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    external_environment_master_id = table.Column<string>(type: "nvarchar(26)", nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_external_environments", x => x.id);
                    table.ForeignKey(
                        name: "fk_external_environments_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_external_environments_external_environment_master_external_environment_master_id",
                        column: x => x.external_environment_master_id,
                        principalTable: "external_environment_masters",
                        principalColumn: "id");
                });

            migrationBuilder.CreateTable(
                name: "family_tree_files",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    file_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_family_tree_files", x => x.id);
                    table.ForeignKey(
                        name: "fk_family_tree_files_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "family_trees",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_family_trees", x => x.id);
                    table.ForeignKey(
                        name: "fk_family_trees_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "five_force_frameworks",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    ideal = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    issue = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    new_comer_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    new_comer_score = table.Column<int>(type: "int", nullable: true),
                    ability_to_negotiate_with_sales_partners_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    ability_to_negotiate_with_sales_partners_score = table.Column<int>(type: "int", nullable: true),
                    ability_to_negotiate_with_suppliers_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    ability_to_negotiate_with_suppliers_score = table.Column<int>(type: "int", nullable: true),
                    competitor_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    competitor_score = table.Column<int>(type: "int", nullable: true),
                    substitute_article_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    substitute_article_score = table.Column<int>(type: "int", nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0")
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_five_force_frameworks", x => x.id);
                    table.ForeignKey(
                        name: "fk_five_force_frameworks_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "five_step_frame_works",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    ideal = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    issue = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    cost_reduction_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    cost_reduction_rating = table.Column<int>(type: "int", nullable: true),
                    management_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    management_rating = table.Column<int>(type: "int", nullable: true),
                    ict_and_bpr_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    ict_and_bpr_rating = table.Column<int>(type: "int", nullable: true),
                    human_resources_and_evaluation_and_development_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    human_resources_and_evaluation_and_development_rating = table.Column<int>(type: "int", nullable: true),
                    marketing_thinking_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    marketing_thinking_rating = table.Column<int>(type: "int", nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_five_step_frame_works", x => x.id);
                    table.ForeignKey(
                        name: "fk_five_step_frame_works_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "hypothetical_discussion_of_issues",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    expired_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    staff_id = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    current_situation = table.Column<string>(type: "nvarchar(max)", maxLength: 10000, nullable: true),
                    ideal = table.Column<string>(type: "nvarchar(max)", maxLength: 10000, nullable: true),
                    issue = table.Column<string>(type: "nvarchar(max)", maxLength: 10000, nullable: true),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    completed_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    title = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: true),
                    status = table.Column<int>(type: "int", maxLength: 1, nullable: true),
                    order = table.Column<int>(type: "int", maxLength: 1, nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_hypothetical_discussion_of_issues", x => x.id);
                    table.ForeignKey(
                        name: "fk_hypothetical_discussion_of_issues_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "management_plans",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    management_plan_overview = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    management_plan_current_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    status_of_achievement_of_management_plan = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    planning_date = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    business_plan_rating = table.Column<int>(type: "int", nullable: true),
                    ideal = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    issue = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_management_plans", x => x.id);
                    table.ForeignKey(
                        name: "fk_management_plans_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "managements",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    ideal = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    issue = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    medium_to_long_term_vision_rating = table.Column<int>(type: "int", nullable: true),
                    medium_to_long_term_vision_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    experience_rating = table.Column<int>(type: "int", nullable: true),
                    experience_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    expertise_rating = table.Column<int>(type: "int", nullable: true),
                    expertise_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    centripetal_force_rating = table.Column<int>(type: "int", nullable: true),
                    centripetal_force_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    successor_rating = table.Column<int>(type: "int", nullable: true),
                    successor_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_managements", x => x.id);
                    table.ForeignKey(
                        name: "fk_managements_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "our_policy_understandings",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    title = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: false),
                    status = table.Column<int>(type: "int", nullable: true),
                    order = table.Column<int>(type: "int", nullable: true),
                    expired_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    staff_id = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    target_person = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    description = table.Column<string>(type: "nvarchar(max)", maxLength: 10000, nullable: true),
                    note = table.Column<string>(type: "nvarchar(max)", maxLength: 10000, nullable: true),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    completed_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0")
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_our_policy_understandings", x => x.id);
                    table.ForeignKey(
                        name: "fk_our_policy_understandings_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "relation_levels",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    policy = table.Column<int>(type: "int", nullable: true),
                    relation_level_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    authority_level = table.Column<double>(type: "float", nullable: true),
                    already_identified_real_authority = table.Column<bool>(type: "bit", nullable: true),
                    visitors_in_the_last_one_year_staff = table.Column<bool>(type: "bit", nullable: true),
                    visitors_in_the_last_one_year_administrator = table.Column<bool>(type: "bit", nullable: true),
                    visitors_in_the_last_one_year_officer = table.Column<bool>(type: "bit", nullable: true),
                    quality_of_interviews = table.Column<int>(type: "int", nullable: true),
                    interview_comments = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    relational_level = table.Column<double>(type: "float", nullable: true),
                    financials_already_shared = table.Column<int>(type: "int", nullable: true),
                    interview_date = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    interviewer = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    interviewee = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    interview_detail = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    concept_of_business_growth = table.Column<int>(type: "int", nullable: true),
                    fundraising_concept = table.Column<int>(type: "int", nullable: true),
                    collateral_approach = table.Column<int>(type: "int", nullable: true),
                    concept_of_banks_number = table.Column<int>(type: "int", nullable: true),
                    concept_of_consulting = table.Column<int>(type: "int", nullable: true),
                    concept_of_lease_use_no_use = table.Column<bool>(type: "bit", nullable: true),
                    concept_of_lease_use_other_use = table.Column<bool>(type: "bit", nullable: true),
                    concept_of_lease_use_main_use = table.Column<bool>(type: "bit", nullable: true),
                    concept_of_esg_and_sd_gs = table.Column<int>(type: "int", nullable: true),
                    alumni_dispatch = table.Column<bool>(type: "bit", nullable: true),
                    understanding_of_improvement = table.Column<int>(type: "int", nullable: true),
                    understanding_of_our_policy = table.Column<int>(type: "int", nullable: true),
                    relationship_comments = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    disclosure_level = table.Column<double>(type: "float", nullable: true),
                    has_financial_statements = table.Column<bool>(type: "bit", nullable: true),
                    has_appraisal_details = table.Column<bool>(type: "bit", nullable: true),
                    has_trial_balance_and_cash_management = table.Column<bool>(type: "bit", nullable: true),
                    management_information = table.Column<int>(type: "int", nullable: true),
                    owner_disclosure = table.Column<int>(type: "int", nullable: true),
                    disclosure_comments = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0")
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_relation_levels", x => x.id);
                    table.ForeignKey(
                        name: "fk_relation_levels_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "sharing_of_finances",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    title = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: true),
                    content = table.Column<string>(type: "nvarchar(max)", maxLength: 10000, nullable: true),
                    note = table.Column<string>(type: "nvarchar(max)", maxLength: 10000, nullable: true),
                    staff_id = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    target_person = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    expired_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    status = table.Column<int>(type: "int", nullable: true),
                    order = table.Column<int>(type: "int", nullable: true),
                    financials_already_shared = table.Column<int>(type: "int", nullable: true),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    completed_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sharing_of_finances", x => x.id);
                    table.ForeignKey(
                        name: "fk_sharing_of_finances_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "swot_analyses",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    strengths = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    weaknesses = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    opportunities = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    threats = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_swot_analyses", x => x.id);
                    table.ForeignKey(
                        name: "fk_swot_analyses_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "three_c_analyses",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    customer = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    company = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    competitor = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_three_c_analyses", x => x.id);
                    table.ForeignKey(
                        name: "fk_three_c_analyses_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "to_dos",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    title = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: true),
                    content = table.Column<string>(type: "nvarchar(max)", maxLength: 10000, nullable: true),
                    staff_id = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    expired_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    status = table.Column<int>(type: "int", nullable: true),
                    order = table.Column<int>(type: "int", nullable: true),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    completed_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    business_understanding_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0")
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_to_dos", x => x.id);
                    table.ForeignKey(
                        name: "fk_to_dos_business_understandings_business_understanding_id",
                        column: x => x.business_understanding_id,
                        principalTable: "business_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "business_partner_industries",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    business_partner_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    sub_industry_code = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    detail_industry_code = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    industry_code = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_partner_industries", x => x.id);
                    table.ForeignKey(
                        name: "fk_business_partner_industries_business_partners_business_partner_id",
                        column: x => x.business_partner_id,
                        principalTable: "business_partners",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "business_understanding_discussions",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    is_corporate_deposit_theme = table.Column<bool>(type: "bit", nullable: true),
                    is_fund_settlement_theme = table.Column<bool>(type: "bit", nullable: true),
                    thread_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    purpose = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "Internal"),
                    person = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    is_person_of_power = table.Column<bool>(type: "bit", nullable: true),
                    mention_target_user_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_team_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_understanding_discussions", x => x.id);
                    table.ForeignKey(
                        name: "fk_business_understanding_comment_business_understanding_thread_id",
                        column: x => x.thread_id,
                        principalTable: "business_understanding_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "business_understanding_thread_files",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    file_name = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false),
                    thread_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_understanding_thread_files", x => x.id);
                    table.ForeignKey(
                        name: "fk_business_understanding_thread_business_understanding_thread_file_id",
                        column: x => x.thread_id,
                        principalTable: "business_understanding_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "business_understanding_thread_reactions",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    thread_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    reaction_type = table.Column<int>(type: "int", nullable: false),
                    staff_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_understanding_thread_reactions", x => x.id);
                    table.ForeignKey(
                        name: "fk_business_understanding_thread_reactions_business_understanding_threads_business_understanding_thread_id",
                        column: x => x.thread_id,
                        principalTable: "business_understanding_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "commercial_distribution_edges",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    commercial_distribution_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    source_node = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    source = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    target_node = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    target = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_commercial_distribution_edges", x => x.id);
                    table.ForeignKey(
                        name: "fk_commercial_distribution_edges_commercial_distributions_commercial_distribution_id",
                        column: x => x.commercial_distribution_id,
                        principalTable: "commercial_distributions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "commercial_distribution_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    canvas_color = table.Column<string>(type: "nvarchar(9)", maxLength: 9, nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    original_id = table.Column<string>(type: "nvarchar(26)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_commercial_distribution_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_commercial_distribution_histories_commercial_distributions_commercial_distribution_id",
                        column: x => x.original_id,
                        principalTable: "commercial_distributions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "commercial_distribution_nodes",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    commercial_distribution_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    node_type = table.Column<int>(type: "int", nullable: false),
                    left = table.Column<double>(type: "float", nullable: false),
                    top = table.Column<double>(type: "float", nullable: false),
                    width = table.Column<double>(type: "float", nullable: false),
                    height = table.Column<double>(type: "float", nullable: false),
                    title = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: true),
                    text = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    grouping_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: true),
                    color = table.Column<string>(type: "nvarchar(9)", maxLength: 9, nullable: true),
                    customer_name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    person_type = table.Column<int>(type: "int", nullable: true),
                    branch_number = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    cif_number = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: true),
                    merchandise = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    material = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    amount = table.Column<double>(type: "float", nullable: true),
                    industry = table.Column<string>(type: "nvarchar(24)", maxLength: 24, nullable: true),
                    area = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_international_business_partners = table.Column<bool>(type: "bit", nullable: true),
                    country_code = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    city_name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    note = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_commercial_distribution_nodes", x => x.id);
                    table.ForeignKey(
                        name: "fk_commercial_distribution_nodes_commercial_distributions_commercial_distribution_id",
                        column: x => x.commercial_distribution_id,
                        principalTable: "commercial_distributions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "customer_ideas_understanding_threads",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    title = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    customer_ideas_understanding_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    contain_customer_reaction = table.Column<bool>(type: "bit", nullable: false),
                    purpose = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "Internal"),
                    person = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    is_person_of_power = table.Column<bool>(type: "bit", nullable: true),
                    correspondence_date = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    mention_targets_html = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_user_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_team_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_customer_ideas_understanding_threads", x => x.id);
                    table.ForeignKey(
                        name: "fk_customer_ideas_understanding_threads_customer_ideas_understanding_id",
                        column: x => x.customer_ideas_understanding_id,
                        principalTable: "customer_ideas_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "esg_and_sd_gs_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    concept_of_esg_and_sd_gs = table.Column<int>(type: "int", nullable: true),
                    is_certification_and_declaration = table.Column<bool>(type: "bit", nullable: true),
                    ideal = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    issue = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    esg_and_sd_gs_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    sd_gs_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    sd_gs_rating = table.Column<int>(type: "int", nullable: true),
                    environment_and_carbon_neutral_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    environment_and_carbon_neutral_rating = table.Column<int>(type: "int", nullable: true),
                    department_of_environment_and_management_rating = table.Column<int>(type: "int", nullable: true),
                    efforts_for_greenhouse_gas_emissions_rating = table.Column<int>(type: "int", nullable: true),
                    subjects_of_certification_and_declaration = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    subjects_of_certification_and_declaration_comment = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    original_id = table.Column<string>(type: "nvarchar(26)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_esg_and_sd_gs_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_esg_and_sd_gs_histories_esg_and_sd_gs_esg_and_sd_gs_id",
                        column: x => x.original_id,
                        principalTable: "esg_and_sd_gs",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "external_environment_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    external_environment_master_id = table.Column<string>(type: "nvarchar(26)", nullable: true),
                    score = table.Column<int>(type: "int", nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    original_id = table.Column<string>(type: "nvarchar(26)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_external_environment_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_external_environment_histories_external_environment_master_external_environment_master_id",
                        column: x => x.external_environment_master_id,
                        principalTable: "external_environment_masters",
                        principalColumn: "id");
                    table.ForeignKey(
                        name: "fk_external_environment_histories_external_environments_external_environment_id",
                        column: x => x.original_id,
                        principalTable: "external_environments",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "family_tree_edges",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    family_tree_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    source_node = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    source = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    target_node = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    target = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_family_tree_edges", x => x.id);
                    table.ForeignKey(
                        name: "fk_family_tree_edges_family_trees_family_tree_id",
                        column: x => x.family_tree_id,
                        principalTable: "family_trees",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "family_tree_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    original_id = table.Column<string>(type: "nvarchar(26)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_family_tree_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_family_tree_histories_family_trees_family_tree_id",
                        column: x => x.original_id,
                        principalTable: "family_trees",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "family_tree_nodes",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    family_tree_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    is_group = table.Column<bool>(type: "bit", nullable: false),
                    left = table.Column<int>(type: "int", nullable: false),
                    top = table.Column<int>(type: "int", nullable: false),
                    width = table.Column<int>(type: "int", nullable: false),
                    height = table.Column<int>(type: "int", nullable: false),
                    name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    relationship = table.Column<string>(type: "nvarchar(16)", maxLength: 16, nullable: true),
                    is_alive = table.Column<bool>(type: "bit", nullable: false),
                    note = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_family_tree_nodes", x => x.id);
                    table.ForeignKey(
                        name: "fk_family_tree_nodes_family_trees_family_tree_id",
                        column: x => x.family_tree_id,
                        principalTable: "family_trees",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "five_force_framework_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    ideal = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    issue = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    new_comer_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    new_comer_score = table.Column<int>(type: "int", nullable: true),
                    ability_to_negotiate_with_sales_partners_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    ability_to_negotiate_with_sales_partners_score = table.Column<int>(type: "int", nullable: true),
                    ability_to_negotiate_with_suppliers_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    ability_to_negotiate_with_suppliers_score = table.Column<int>(type: "int", nullable: true),
                    competitor_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    competitor_score = table.Column<int>(type: "int", nullable: true),
                    substitute_article_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    substitute_article_score = table.Column<int>(type: "int", nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    original_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_five_force_framework_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_five_force_framework_histories_five_force_frameworks_five_force_framework_id",
                        column: x => x.original_id,
                        principalTable: "five_force_frameworks",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "five_step_frame_work_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    ideal = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    issue = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    cost_reduction_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    cost_reduction_rating = table.Column<int>(type: "int", nullable: true),
                    management_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    management_rating = table.Column<int>(type: "int", nullable: true),
                    ict_and_bpr_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    ict_and_bpr_rating = table.Column<int>(type: "int", nullable: true),
                    human_resources_and_evaluation_and_development_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    human_resources_and_evaluation_and_development_rating = table.Column<int>(type: "int", nullable: true),
                    marketing_thinking_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    marketing_thinking_rating = table.Column<int>(type: "int", nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    original_id = table.Column<string>(type: "nvarchar(26)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_five_step_frame_work_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_five_step_frame_work_histories_five_step_frame_works_five_step_frame_work_id",
                        column: x => x.original_id,
                        principalTable: "five_step_frame_works",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "customer_reactions",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    content = table.Column<string>(type: "nvarchar(4000)", maxLength: 4000, nullable: false),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    register = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    hypothetical_discussion_of_issues_id = table.Column<string>(type: "nvarchar(26)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_customer_reactions", x => x.id);
                    table.ForeignKey(
                        name: "fk_customer_reactions_hypothetical_discussion_of_issues_hypothetical_discussion_of_issues_id",
                        column: x => x.hypothetical_discussion_of_issues_id,
                        principalTable: "hypothetical_discussion_of_issues",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "hypothetical_discussion_of_issues_threads",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    title = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    hypothetical_discussion_of_issues_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    contain_customer_reaction = table.Column<bool>(type: "bit", nullable: false),
                    purpose = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "Internal"),
                    person = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    is_person_of_power = table.Column<bool>(type: "bit", nullable: true),
                    correspondence_date = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    mention_targets_html = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_user_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_team_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_hypothetical_discussion_of_issues_threads", x => x.id);
                    table.ForeignKey(
                        name: "fk_hdoi_threads_hdoi_id",
                        column: x => x.hypothetical_discussion_of_issues_id,
                        principalTable: "hypothetical_discussion_of_issues",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "management_plan_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    management_plan_overview = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    management_plan_current_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    status_of_achievement_of_management_plan = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    planning_date = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    business_plan_rating = table.Column<int>(type: "int", nullable: true),
                    ideal = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    issue = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    original_id = table.Column<string>(type: "nvarchar(26)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_management_plan_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_management_plan_histories_management_plans_management_plan_id",
                        column: x => x.original_id,
                        principalTable: "management_plans",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "management_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    ideal = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    issue = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    medium_to_long_term_vision_rating = table.Column<int>(type: "int", nullable: true),
                    medium_to_long_term_vision_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    experience_rating = table.Column<int>(type: "int", nullable: true),
                    experience_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    expertise_rating = table.Column<int>(type: "int", nullable: true),
                    expertise_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    centripetal_force_rating = table.Column<int>(type: "int", nullable: true),
                    centripetal_force_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    successor_rating = table.Column<int>(type: "int", nullable: true),
                    successor_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    original_id = table.Column<string>(type: "nvarchar(26)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_management_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_management_histories_managements_management_id",
                        column: x => x.original_id,
                        principalTable: "managements",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "our_policy_understanding_threads",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    title = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    our_policy_understanding_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    contain_customer_reaction = table.Column<bool>(type: "bit", nullable: false),
                    purpose = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "Internal"),
                    person = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    is_person_of_power = table.Column<bool>(type: "bit", nullable: true),
                    correspondence_date = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    mention_targets_html = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_user_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_team_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_our_policy_understanding_threads", x => x.id);
                    table.ForeignKey(
                        name: "fk_our_policy_understanding_threads_our_policy_understanding_id",
                        column: x => x.our_policy_understanding_id,
                        principalTable: "our_policy_understandings",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "relation_level_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    already_identified_real_authority = table.Column<bool>(type: "bit", nullable: true),
                    visitors_in_the_last_one_year_staff = table.Column<bool>(type: "bit", nullable: true),
                    visitors_in_the_last_one_year_administrator = table.Column<bool>(type: "bit", nullable: true),
                    visitors_in_the_last_one_year_officer = table.Column<bool>(type: "bit", nullable: true),
                    quality_of_interviews = table.Column<int>(type: "int", nullable: true),
                    interview_comments = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    financials_already_shared = table.Column<int>(type: "int", nullable: true),
                    interview_date = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    interviewer = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    interviewee = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    interview_detail = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    concept_of_business_growth = table.Column<int>(type: "int", nullable: true),
                    fundraising_concept = table.Column<int>(type: "int", nullable: true),
                    collateral_approach = table.Column<int>(type: "int", nullable: true),
                    concept_of_banks_number = table.Column<int>(type: "int", nullable: true),
                    concept_of_consulting = table.Column<int>(type: "int", nullable: true),
                    concept_of_lease_use_no_use = table.Column<bool>(type: "bit", nullable: true),
                    concept_of_lease_use_other_use = table.Column<bool>(type: "bit", nullable: true),
                    concept_of_lease_use_main_use = table.Column<bool>(type: "bit", nullable: true),
                    concept_of_esg_and_sd_gs = table.Column<int>(type: "int", nullable: true),
                    alumni_dispatch = table.Column<bool>(type: "bit", nullable: true),
                    understanding_of_improvement = table.Column<int>(type: "int", nullable: true),
                    understanding_of_our_policy = table.Column<int>(type: "int", nullable: true),
                    relationship_comments = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    has_financial_statements = table.Column<bool>(type: "bit", nullable: true),
                    has_appraisal_details = table.Column<bool>(type: "bit", nullable: true),
                    has_trial_balance_and_cash_management = table.Column<bool>(type: "bit", nullable: true),
                    management_information = table.Column<int>(type: "int", nullable: true),
                    owner_disclosure = table.Column<int>(type: "int", nullable: true),
                    disclosure_comments = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    policy = table.Column<int>(type: "int", nullable: true),
                    authority_level = table.Column<double>(type: "float", nullable: true),
                    relational_level = table.Column<double>(type: "float", nullable: true),
                    disclosure_level = table.Column<double>(type: "float", nullable: true),
                    relation_level_comment = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    original_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    relation_level_id = table.Column<string>(type: "nvarchar(26)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_relation_level_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_relation_level_histories_relation_levels_relation_level_id",
                        column: x => x.relation_level_id,
                        principalTable: "relation_levels",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "sharing_of_finance_threads",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    title = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    sharing_of_finance_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    contain_customer_reaction = table.Column<bool>(type: "bit", nullable: false),
                    purpose = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "Internal"),
                    person = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    is_person_of_power = table.Column<bool>(type: "bit", nullable: true),
                    correspondence_date = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    mention_targets_html = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_user_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_team_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sharing_of_finance_threads", x => x.id);
                    table.ForeignKey(
                        name: "fk_sharing_of_finance_threads_sharing_of_finance_id",
                        column: x => x.sharing_of_finance_id,
                        principalTable: "sharing_of_finances",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "swot_analyses_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    strengths = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    weaknesses = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    opportunities = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    threats = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    original_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_swot_analyses_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_swot_analyses_histories_swot_analyses_swot_analysis_id",
                        column: x => x.original_id,
                        principalTable: "swot_analyses",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "three_c_analysis_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    customer = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    company = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    competitor = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    original_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    three_c_analysis_id = table.Column<string>(type: "nvarchar(26)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_three_c_analysis_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_three_c_analysis_histories_three_c_analyses_three_c_analysis_id",
                        column: x => x.three_c_analysis_id,
                        principalTable: "three_c_analyses",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "to_do_threads",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    title = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    to_do_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    contain_customer_reaction = table.Column<bool>(type: "bit", nullable: false),
                    purpose = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "Internal"),
                    person = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    is_person_of_power = table.Column<bool>(type: "bit", nullable: true),
                    correspondence_date = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    mention_targets_html = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_user_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_team_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_to_do_threads", x => x.id);
                    table.ForeignKey(
                        name: "fk_todo_threads_todo_id",
                        column: x => x.to_do_id,
                        principalTable: "to_dos",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "business_understanding_discussion_files",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    file_name = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false),
                    discussion_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_understanding_discussion_files", x => x.id);
                    table.ForeignKey(
                        name: "fk_business_understanding_discussion_business_understanding_discussion_file_id",
                        column: x => x.discussion_id,
                        principalTable: "business_understanding_discussions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "business_understanding_discussion_reactions",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    comment_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    reaction_type = table.Column<int>(type: "int", nullable: false),
                    staff_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_business_understanding_discussion_reactions", x => x.id);
                    table.ForeignKey(
                        name: "fk_business_understanding_discussion_reactions_business_understanding_discussions_business_understanding_discussion_id",
                        column: x => x.comment_id,
                        principalTable: "business_understanding_discussions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "commercial_distribution_edge_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    commercial_distribution_edge_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    commercial_distribution_history_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    source_node = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    source = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    target_node = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    target = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_commercial_distribution_edge_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_commercial_distribution_edge_histories_commercial_distribution_histories_commercial_distribution_history_id",
                        column: x => x.commercial_distribution_history_id,
                        principalTable: "commercial_distribution_histories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "commercial_distribution_node_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    commercial_distribution_node_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    commercial_distribution_history_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    node_type = table.Column<int>(type: "int", nullable: false),
                    left = table.Column<double>(type: "float", nullable: false),
                    top = table.Column<double>(type: "float", nullable: false),
                    width = table.Column<double>(type: "float", nullable: false),
                    height = table.Column<double>(type: "float", nullable: false),
                    title = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: true),
                    text = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    grouping_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: true),
                    color = table.Column<string>(type: "nvarchar(9)", maxLength: 9, nullable: true),
                    customer_name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    person_type = table.Column<int>(type: "int", nullable: true),
                    branch_number = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    cif_number = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: true),
                    merchandise = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    material = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    amount = table.Column<double>(type: "float", nullable: true),
                    industry = table.Column<string>(type: "nvarchar(24)", maxLength: 24, nullable: true),
                    area = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_international_business_partners = table.Column<bool>(type: "bit", nullable: true),
                    country_code = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    city_name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    note = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_commercial_distribution_node_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_commercial_distribution_node_histories_commercial_distribution_histories_commercial_distribution_history_id",
                        column: x => x.commercial_distribution_history_id,
                        principalTable: "commercial_distribution_histories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "customer_ideas_understanding_comments",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    thread_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    contain_customer_reaction = table.Column<bool>(type: "bit", nullable: false),
                    purpose = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "Internal"),
                    person = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    is_person_of_power = table.Column<bool>(type: "bit", nullable: true),
                    mention_target_user_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_team_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_customer_ideas_understanding_comments", x => x.id);
                    table.ForeignKey(
                        name: "fk_customer_ideas_understanding_comment_customer_ideas_understanding_thread_id",
                        column: x => x.thread_id,
                        principalTable: "customer_ideas_understanding_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "customer_ideas_understanding_thread_files",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    file_name = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false),
                    thread_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_customer_ideas_understanding_thread_files", x => x.id);
                    table.ForeignKey(
                        name: "fk_customer_ideas_understanding_thread_customer_ideas_understanding_thread_file_id",
                        column: x => x.thread_id,
                        principalTable: "customer_ideas_understanding_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "customer_ideas_understanding_thread_reactions",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    thread_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    reaction_type = table.Column<int>(type: "int", nullable: false),
                    staff_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_customer_ideas_understanding_thread_reactions", x => x.id);
                    table.ForeignKey(
                        name: "fk_customer_ideas_understanding_thread_reactions_customer_ideas_understanding_threads_customer_ideas_understanding_thread_id",
                        column: x => x.thread_id,
                        principalTable: "customer_ideas_understanding_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "family_tree_edge_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    family_tree_edge_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    family_tree_history_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    source_node = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    source = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    target_node = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    target = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_family_tree_edge_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_family_tree_edge_histories_family_tree_histories_family_tree_history_id",
                        column: x => x.family_tree_history_id,
                        principalTable: "family_tree_histories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "family_tree_node_histories",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    family_tree_node_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    family_tree_history_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    is_group = table.Column<bool>(type: "bit", nullable: false),
                    left = table.Column<int>(type: "int", nullable: false),
                    top = table.Column<int>(type: "int", nullable: false),
                    width = table.Column<int>(type: "int", nullable: false),
                    height = table.Column<int>(type: "int", nullable: false),
                    name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    relationship = table.Column<string>(type: "nvarchar(16)", maxLength: 16, nullable: true),
                    is_alive = table.Column<bool>(type: "bit", nullable: false),
                    note = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_family_tree_node_histories", x => x.id);
                    table.ForeignKey(
                        name: "fk_family_tree_node_histories_family_tree_histories_family_tree_history_id",
                        column: x => x.family_tree_history_id,
                        principalTable: "family_tree_histories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "hypothetical_discussion_of_issues_comments",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    contain_customer_reaction = table.Column<bool>(type: "bit", nullable: false),
                    purpose = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "Internal"),
                    person = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    is_person_of_power = table.Column<bool>(type: "bit", nullable: true),
                    thread_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    mention_target_user_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_team_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_hypothetical_discussion_of_issues_comments", x => x.id);
                    table.ForeignKey(
                        name: "fk_hdoi_comment_hdoi_thread_id",
                        column: x => x.thread_id,
                        principalTable: "hypothetical_discussion_of_issues_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "hypothetical_discussion_of_issues_thread_files",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    file_name = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false),
                    thread_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_hypothetical_discussion_of_issues_thread_files", x => x.id);
                    table.ForeignKey(
                        name: "fk_hdoi_thread_hdoi_file_thread_id",
                        column: x => x.thread_id,
                        principalTable: "hypothetical_discussion_of_issues_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "hypothetical_discussion_of_issues_thread_reactions",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    thread_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    reaction_type = table.Column<int>(type: "int", nullable: false),
                    staff_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_hypothetical_discussion_of_issues_thread_reactions", x => x.id);
                    table.ForeignKey(
                        name: "fk_hypothetical_discussion_of_issues_thread_reactions_hypothetical_discussion_of_issues_threads_hypothetical_discussion_of_issu~",
                        column: x => x.thread_id,
                        principalTable: "hypothetical_discussion_of_issues_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "our_policy_understanding_comments",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    thread_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    contain_customer_reaction = table.Column<bool>(type: "bit", nullable: false),
                    purpose = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "Internal"),
                    person = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    is_person_of_power = table.Column<bool>(type: "bit", nullable: true),
                    mention_target_user_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_team_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_our_policy_understanding_comments", x => x.id);
                    table.ForeignKey(
                        name: "fk_our_policy_understanding_comment_our_policy_understanding_thread_id",
                        column: x => x.thread_id,
                        principalTable: "our_policy_understanding_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "our_policy_understanding_thread_files",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    file_name = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false),
                    thread_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_our_policy_understanding_thread_files", x => x.id);
                    table.ForeignKey(
                        name: "fk_our_policy_understanding_thread_our_policy_understanding_thread_file_id",
                        column: x => x.thread_id,
                        principalTable: "our_policy_understanding_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "our_policy_understanding_thread_reactions",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    thread_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    reaction_type = table.Column<int>(type: "int", nullable: false),
                    staff_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_our_policy_understanding_thread_reactions", x => x.id);
                    table.ForeignKey(
                        name: "fk_our_policy_understanding_thread_reactions_our_policy_understanding_threads_our_policy_understanding_thread_id",
                        column: x => x.thread_id,
                        principalTable: "our_policy_understanding_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "sharing_of_finance_comments",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    thread_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    contain_customer_reaction = table.Column<bool>(type: "bit", nullable: false),
                    purpose = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "Internal"),
                    person = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    is_person_of_power = table.Column<bool>(type: "bit", nullable: true),
                    mention_target_user_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_team_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sharing_of_finance_comments", x => x.id);
                    table.ForeignKey(
                        name: "fk_sharing_of_finance_comment_sharing_of_finance_thread_id",
                        column: x => x.thread_id,
                        principalTable: "sharing_of_finance_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "sharing_of_finance_thread_files",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    file_name = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false),
                    thread_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sharing_of_finance_thread_files", x => x.id);
                    table.ForeignKey(
                        name: "fk_sharing_of_finance_thread_sharing_of_finance_thread_file_id",
                        column: x => x.thread_id,
                        principalTable: "sharing_of_finance_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "sharing_of_finance_thread_reactions",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    thread_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    reaction_type = table.Column<int>(type: "int", nullable: false),
                    staff_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sharing_of_finance_thread_reactions", x => x.id);
                    table.ForeignKey(
                        name: "fk_sharing_of_finance_thread_reactions_sharing_of_finance_threads_sharing_of_finance_thread_id",
                        column: x => x.thread_id,
                        principalTable: "sharing_of_finance_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "to_do_comments",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    registered_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    registrant = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    registrant_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    thread_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    contain_customer_reaction = table.Column<bool>(type: "bit", nullable: false),
                    purpose = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "Internal"),
                    person = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    is_person_of_power = table.Column<bool>(type: "bit", nullable: true),
                    mention_target_user_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    mention_target_team_ids = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_to_do_comments", x => x.id);
                    table.ForeignKey(
                        name: "fk_todo_comment_todo_thread_id",
                        column: x => x.thread_id,
                        principalTable: "to_do_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "to_do_thread_files",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    file_name = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false),
                    thread_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_to_do_thread_files", x => x.id);
                    table.ForeignKey(
                        name: "fk_todo_thread_todo_thread_file_id",
                        column: x => x.thread_id,
                        principalTable: "to_do_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "to_do_thread_reactions",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    thread_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    reaction_type = table.Column<int>(type: "int", nullable: false),
                    staff_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_to_do_thread_reactions", x => x.id);
                    table.ForeignKey(
                        name: "fk_to_do_thread_reactions_to_do_threads_to_do_thread_id",
                        column: x => x.thread_id,
                        principalTable: "to_do_threads",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "customer_ideas_understanding_comment_files",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    file_name = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false),
                    comment_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_customer_ideas_understanding_comment_files", x => x.id);
                    table.ForeignKey(
                        name: "fk_customer_ideas_understanding_comment_customer_ideas_understanding_comment_file_id",
                        column: x => x.comment_id,
                        principalTable: "customer_ideas_understanding_comments",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "customer_ideas_understanding_comment_reactions",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    comment_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    reaction_type = table.Column<int>(type: "int", nullable: false),
                    staff_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_customer_ideas_understanding_comment_reactions", x => x.id);
                    table.ForeignKey(
                        name: "fk_customer_ideas_understanding_comment_reactions_customer_ideas_understanding_comments_customer_ideas_understanding_comment_id",
                        column: x => x.comment_id,
                        principalTable: "customer_ideas_understanding_comments",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "hypothetical_discussion_of_issues_comment_files",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    file_name = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false),
                    comment_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_hypothetical_discussion_of_issues_comment_files", x => x.id);
                    table.ForeignKey(
                        name: "fk_hdoi_comment_hdoi_file_comment_id",
                        column: x => x.comment_id,
                        principalTable: "hypothetical_discussion_of_issues_comments",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "hypothetical_discussion_of_issues_comment_reactions",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    comment_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    reaction_type = table.Column<int>(type: "int", nullable: false),
                    staff_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_hypothetical_discussion_of_issues_comment_reactions", x => x.id);
                    table.ForeignKey(
                        name: "fk_hypothetical_discussion_of_issues_comment_reactions_hypothetical_discussion_of_issues_comments_hypothetical_discussion_of_is~",
                        column: x => x.comment_id,
                        principalTable: "hypothetical_discussion_of_issues_comments",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "our_policy_understanding_comment_files",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    file_name = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false),
                    comment_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_our_policy_understanding_comment_files", x => x.id);
                    table.ForeignKey(
                        name: "fk_our_policy_understanding_comment_our_policy_understanding_comment_file_id",
                        column: x => x.comment_id,
                        principalTable: "our_policy_understanding_comments",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "our_policy_understanding_comment_reactions",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    comment_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    reaction_type = table.Column<int>(type: "int", nullable: false),
                    staff_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_our_policy_understanding_comment_reactions", x => x.id);
                    table.ForeignKey(
                        name: "fk_our_policy_understanding_comment_reactions_our_policy_understanding_comments_our_policy_understanding_comment_id",
                        column: x => x.comment_id,
                        principalTable: "our_policy_understanding_comments",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "sharing_of_finance_comment_files",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    file_name = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false),
                    comment_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sharing_of_finance_comment_files", x => x.id);
                    table.ForeignKey(
                        name: "fk_sharing_of_finance_comment_sharing_of_finance_comment_file_id",
                        column: x => x.comment_id,
                        principalTable: "sharing_of_finance_comments",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "sharing_of_finance_comment_reactions",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    comment_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    reaction_type = table.Column<int>(type: "int", nullable: false),
                    staff_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sharing_of_finance_comment_reactions", x => x.id);
                    table.ForeignKey(
                        name: "fk_sharing_of_finance_comment_reactions_sharing_of_finance_comments_sharing_of_finance_comment_id",
                        column: x => x.comment_id,
                        principalTable: "sharing_of_finance_comments",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "to_do_comment_files",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    file_name = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false),
                    comment_id = table.Column<string>(type: "nvarchar(26)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updater_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updater_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_to_do_comment_files", x => x.id);
                    table.ForeignKey(
                        name: "fk_todo_comment_todo_comment_file_id",
                        column: x => x.comment_id,
                        principalTable: "to_do_comments",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "to_do_comment_reactions",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    comment_id = table.Column<string>(type: "nvarchar(26)", maxLength: 26, nullable: false),
                    reaction_type = table.Column<int>(type: "int", nullable: false),
                    staff_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    staff_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    updated_date_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    version = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false, defaultValue: "0"),
                    updated_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    updated_program = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_to_do_comment_reactions", x => x.id);
                    table.ForeignKey(
                        name: "fk_to_do_comment_reactions_to_do_comments_to_do_comment_id",
                        column: x => x.comment_id,
                        principalTable: "to_do_comments",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_business_customers_customer_identification_id",
                table: "business_customers",
                column: "customer_identification_id")
                .Annotation("SqlServer:Clustered", false);

            migrationBuilder.CreateIndex(
                name: "ix_business_partner_industries_business_partner_id",
                table: "business_partner_industries",
                column: "business_partner_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_business_partners_business_understanding_id",
                table: "business_partners",
                column: "business_understanding_id");

            migrationBuilder.CreateIndex(
                name: "ix_business_partnership_and_creative_accounting_details_business_understanding_id",
                table: "business_partnership_and_creative_accounting_details",
                column: "business_understanding_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_business_understanding_discussion_files_discussion_id",
                table: "business_understanding_discussion_files",
                column: "discussion_id");

            migrationBuilder.CreateIndex(
                name: "IX_business_understanding_discussion_reactions_comment_id",
                table: "business_understanding_discussion_reactions",
                column: "comment_id");

            migrationBuilder.CreateIndex(
                name: "IX_business_understanding_discussions_thread_id",
                table: "business_understanding_discussions",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "ix_business_understanding_files_business_understanding_id",
                table: "business_understanding_files",
                column: "business_understanding_id");

            migrationBuilder.CreateIndex(
                name: "IX_business_understanding_histories_original_id",
                table: "business_understanding_histories",
                column: "original_id");

            migrationBuilder.CreateIndex(
                name: "ix_business_understanding_materialized_views_business_understanding_id",
                table: "business_understanding_materialized_views",
                column: "business_understanding_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_business_understanding_thread_files_thread_id",
                table: "business_understanding_thread_files",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "IX_business_understanding_thread_reactions_thread_id",
                table: "business_understanding_thread_reactions",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "ix_business_understanding_threads_business_understanding_id",
                table: "business_understanding_threads",
                column: "business_understanding_id");

            migrationBuilder.CreateIndex(
                name: "ix_business_understandings_business_customer_id",
                table: "business_understandings",
                column: "business_customer_id")
                .Annotation("SqlServer:Clustered", false);

            migrationBuilder.CreateIndex(
                name: "ix_commercial_distribution_edge_histories_commercial_distribution_history_id",
                table: "commercial_distribution_edge_histories",
                column: "commercial_distribution_history_id");

            migrationBuilder.CreateIndex(
                name: "ix_commercial_distribution_edges_commercial_distribution_id",
                table: "commercial_distribution_edges",
                column: "commercial_distribution_id");

            migrationBuilder.CreateIndex(
                name: "IX_commercial_distribution_histories_original_id",
                table: "commercial_distribution_histories",
                column: "original_id");

            migrationBuilder.CreateIndex(
                name: "ix_commercial_distribution_node_histories_commercial_distribution_history_id",
                table: "commercial_distribution_node_histories",
                column: "commercial_distribution_history_id");

            migrationBuilder.CreateIndex(
                name: "ix_commercial_distribution_nodes_commercial_distribution_id",
                table: "commercial_distribution_nodes",
                column: "commercial_distribution_id");

            migrationBuilder.CreateIndex(
                name: "IX_commercial_distribution_template_edges_template_id",
                table: "commercial_distribution_template_edges",
                column: "template_id");

            migrationBuilder.CreateIndex(
                name: "IX_commercial_distribution_template_nodes_template_id",
                table: "commercial_distribution_template_nodes",
                column: "template_id");

            migrationBuilder.CreateIndex(
                name: "ix_commercial_distributions_business_understanding_id",
                table: "commercial_distributions",
                column: "business_understanding_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_customer_ideas_understanding_comment_files_comment_id",
                table: "customer_ideas_understanding_comment_files",
                column: "comment_id");

            migrationBuilder.CreateIndex(
                name: "IX_customer_ideas_understanding_comment_reactions_comment_id",
                table: "customer_ideas_understanding_comment_reactions",
                column: "comment_id");

            migrationBuilder.CreateIndex(
                name: "IX_customer_ideas_understanding_comments_thread_id",
                table: "customer_ideas_understanding_comments",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "IX_customer_ideas_understanding_thread_files_thread_id",
                table: "customer_ideas_understanding_thread_files",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "IX_customer_ideas_understanding_thread_reactions_thread_id",
                table: "customer_ideas_understanding_thread_reactions",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "ix_customer_ideas_understanding_threads_customer_ideas_understanding_id",
                table: "customer_ideas_understanding_threads",
                column: "customer_ideas_understanding_id");

            migrationBuilder.CreateIndex(
                name: "ix_customer_ideas_understandings_business_understanding_id",
                table: "customer_ideas_understandings",
                column: "business_understanding_id");

            migrationBuilder.CreateIndex(
                name: "ix_customer_links_business_customer_id",
                table: "customer_links",
                column: "business_customer_id");

            migrationBuilder.CreateIndex(
                name: "ix_customer_reactions_hypothetical_discussion_of_issues_id",
                table: "customer_reactions",
                column: "hypothetical_discussion_of_issues_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_esg_and_sd_gs_business_understanding_id",
                table: "esg_and_sd_gs",
                column: "business_understanding_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_esg_and_sd_gs_histories_original_id",
                table: "esg_and_sd_gs_histories",
                column: "original_id");

            migrationBuilder.CreateIndex(
                name: "ix_external_environment_histories_external_environment_master_id",
                table: "external_environment_histories",
                column: "external_environment_master_id");

            migrationBuilder.CreateIndex(
                name: "IX_external_environment_histories_original_id",
                table: "external_environment_histories",
                column: "original_id");

            migrationBuilder.CreateIndex(
                name: "ix_external_environments_business_understanding_id",
                table: "external_environments",
                column: "business_understanding_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_external_environments_external_environment_master_id",
                table: "external_environments",
                column: "external_environment_master_id");

            migrationBuilder.CreateIndex(
                name: "ix_family_tree_edge_histories_family_tree_history_id",
                table: "family_tree_edge_histories",
                column: "family_tree_history_id");

            migrationBuilder.CreateIndex(
                name: "ix_family_tree_edges_family_tree_id",
                table: "family_tree_edges",
                column: "family_tree_id");

            migrationBuilder.CreateIndex(
                name: "ix_family_tree_files_business_understanding_id",
                table: "family_tree_files",
                column: "business_understanding_id");

            migrationBuilder.CreateIndex(
                name: "IX_family_tree_histories_original_id",
                table: "family_tree_histories",
                column: "original_id");

            migrationBuilder.CreateIndex(
                name: "ix_family_tree_node_histories_family_tree_history_id",
                table: "family_tree_node_histories",
                column: "family_tree_history_id");

            migrationBuilder.CreateIndex(
                name: "ix_family_tree_nodes_family_tree_id",
                table: "family_tree_nodes",
                column: "family_tree_id");

            migrationBuilder.CreateIndex(
                name: "ix_family_trees_business_understanding_id",
                table: "family_trees",
                column: "business_understanding_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_five_force_framework_histories_original_id",
                table: "five_force_framework_histories",
                column: "original_id");

            migrationBuilder.CreateIndex(
                name: "ix_five_force_frameworks_business_understanding_id",
                table: "five_force_frameworks",
                column: "business_understanding_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_five_step_frame_work_histories_original_id",
                table: "five_step_frame_work_histories",
                column: "original_id");

            migrationBuilder.CreateIndex(
                name: "ix_five_step_frame_works_business_understanding_id",
                table: "five_step_frame_works",
                column: "business_understanding_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_hypothetical_discussion_of_issues_business_understanding_id",
                table: "hypothetical_discussion_of_issues",
                column: "business_understanding_id");

            migrationBuilder.CreateIndex(
                name: "IX_hypothetical_discussion_of_issues_comment_files_comment_id",
                table: "hypothetical_discussion_of_issues_comment_files",
                column: "comment_id");

            migrationBuilder.CreateIndex(
                name: "IX_hypothetical_discussion_of_issues_comment_reactions_comment_id",
                table: "hypothetical_discussion_of_issues_comment_reactions",
                column: "comment_id");

            migrationBuilder.CreateIndex(
                name: "IX_hypothetical_discussion_of_issues_comments_thread_id",
                table: "hypothetical_discussion_of_issues_comments",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "IX_hypothetical_discussion_of_issues_thread_files_thread_id",
                table: "hypothetical_discussion_of_issues_thread_files",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "IX_hypothetical_discussion_of_issues_thread_reactions_thread_id",
                table: "hypothetical_discussion_of_issues_thread_reactions",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "ix_hypothetical_discussion_of_issues_threads_hypothetical_discussion_of_issues_id",
                table: "hypothetical_discussion_of_issues_threads",
                column: "hypothetical_discussion_of_issues_id");

            migrationBuilder.CreateIndex(
                name: "IX_management_histories_original_id",
                table: "management_histories",
                column: "original_id");

            migrationBuilder.CreateIndex(
                name: "IX_management_plan_histories_original_id",
                table: "management_plan_histories",
                column: "original_id");

            migrationBuilder.CreateIndex(
                name: "ix_management_plans_business_understanding_id",
                table: "management_plans",
                column: "business_understanding_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_managements_business_understanding_id",
                table: "managements",
                column: "business_understanding_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_our_policy_understanding_comment_files_comment_id",
                table: "our_policy_understanding_comment_files",
                column: "comment_id");

            migrationBuilder.CreateIndex(
                name: "IX_our_policy_understanding_comment_reactions_comment_id",
                table: "our_policy_understanding_comment_reactions",
                column: "comment_id");

            migrationBuilder.CreateIndex(
                name: "IX_our_policy_understanding_comments_thread_id",
                table: "our_policy_understanding_comments",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "IX_our_policy_understanding_thread_files_thread_id",
                table: "our_policy_understanding_thread_files",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "IX_our_policy_understanding_thread_reactions_thread_id",
                table: "our_policy_understanding_thread_reactions",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "ix_our_policy_understanding_threads_our_policy_understanding_id",
                table: "our_policy_understanding_threads",
                column: "our_policy_understanding_id");

            migrationBuilder.CreateIndex(
                name: "ix_our_policy_understandings_business_understanding_id",
                table: "our_policy_understandings",
                column: "business_understanding_id");

            migrationBuilder.CreateIndex(
                name: "ix_relation_level_histories_relation_level_id",
                table: "relation_level_histories",
                column: "relation_level_id");

            migrationBuilder.CreateIndex(
                name: "ix_relation_levels_business_understanding_id",
                table: "relation_levels",
                column: "business_understanding_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_sharing_of_finance_comment_files_comment_id",
                table: "sharing_of_finance_comment_files",
                column: "comment_id");

            migrationBuilder.CreateIndex(
                name: "IX_sharing_of_finance_comment_reactions_comment_id",
                table: "sharing_of_finance_comment_reactions",
                column: "comment_id");

            migrationBuilder.CreateIndex(
                name: "IX_sharing_of_finance_comments_thread_id",
                table: "sharing_of_finance_comments",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "IX_sharing_of_finance_thread_files_thread_id",
                table: "sharing_of_finance_thread_files",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "IX_sharing_of_finance_thread_reactions_thread_id",
                table: "sharing_of_finance_thread_reactions",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "ix_sharing_of_finance_threads_sharing_of_finance_id",
                table: "sharing_of_finance_threads",
                column: "sharing_of_finance_id");

            migrationBuilder.CreateIndex(
                name: "ix_sharing_of_finances_business_understanding_id",
                table: "sharing_of_finances",
                column: "business_understanding_id");

            migrationBuilder.CreateIndex(
                name: "ix_swot_analyses_business_understanding_id",
                table: "swot_analyses",
                column: "business_understanding_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_swot_analyses_histories_original_id",
                table: "swot_analyses_histories",
                column: "original_id");

            migrationBuilder.CreateIndex(
                name: "ix_three_c_analyses_business_understanding_id",
                table: "three_c_analyses",
                column: "business_understanding_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_three_c_analysis_histories_three_c_analysis_id",
                table: "three_c_analysis_histories",
                column: "three_c_analysis_id");

            migrationBuilder.CreateIndex(
                name: "IX_to_do_comment_files_comment_id",
                table: "to_do_comment_files",
                column: "comment_id");

            migrationBuilder.CreateIndex(
                name: "IX_to_do_comment_reactions_comment_id",
                table: "to_do_comment_reactions",
                column: "comment_id");

            migrationBuilder.CreateIndex(
                name: "IX_to_do_comments_thread_id",
                table: "to_do_comments",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "IX_to_do_thread_files_thread_id",
                table: "to_do_thread_files",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "IX_to_do_thread_reactions_thread_id",
                table: "to_do_thread_reactions",
                column: "thread_id");

            migrationBuilder.CreateIndex(
                name: "ix_to_do_threads_to_do_id",
                table: "to_do_threads",
                column: "to_do_id");

            migrationBuilder.CreateIndex(
                name: "ix_to_dos_business_understanding_id",
                table: "to_dos",
                column: "business_understanding_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "business_partner_industries");

            migrationBuilder.DropTable(
                name: "business_partnership_and_creative_accounting_details");

            migrationBuilder.DropTable(
                name: "business_understanding_approaches");

            migrationBuilder.DropTable(
                name: "business_understanding_discussion_files");

            migrationBuilder.DropTable(
                name: "business_understanding_discussion_reactions");

            migrationBuilder.DropTable(
                name: "business_understanding_files");

            migrationBuilder.DropTable(
                name: "business_understanding_histories");

            migrationBuilder.DropTable(
                name: "business_understanding_materialized_view_queues");

            migrationBuilder.DropTable(
                name: "business_understanding_materialized_views");

            migrationBuilder.DropTable(
                name: "business_understanding_thread_files");

            migrationBuilder.DropTable(
                name: "business_understanding_thread_reactions");

            migrationBuilder.DropTable(
                name: "commercial_distribution_edge_histories");

            migrationBuilder.DropTable(
                name: "commercial_distribution_edges");

            migrationBuilder.DropTable(
                name: "commercial_distribution_node_histories");

            migrationBuilder.DropTable(
                name: "commercial_distribution_nodes");

            migrationBuilder.DropTable(
                name: "commercial_distribution_template_edges");

            migrationBuilder.DropTable(
                name: "commercial_distribution_template_nodes");

            migrationBuilder.DropTable(
                name: "customer_ideas_understanding_comment_files");

            migrationBuilder.DropTable(
                name: "customer_ideas_understanding_comment_reactions");

            migrationBuilder.DropTable(
                name: "customer_ideas_understanding_thread_files");

            migrationBuilder.DropTable(
                name: "customer_ideas_understanding_thread_reactions");

            migrationBuilder.DropTable(
                name: "customer_links");

            migrationBuilder.DropTable(
                name: "customer_reactions");

            migrationBuilder.DropTable(
                name: "esg_and_sd_gs_histories");

            migrationBuilder.DropTable(
                name: "external_environment_histories");

            migrationBuilder.DropTable(
                name: "external_environment_master_upload_progresses");

            migrationBuilder.DropTable(
                name: "family_tree_edge_histories");

            migrationBuilder.DropTable(
                name: "family_tree_edges");

            migrationBuilder.DropTable(
                name: "family_tree_files");

            migrationBuilder.DropTable(
                name: "family_tree_node_histories");

            migrationBuilder.DropTable(
                name: "family_tree_nodes");

            migrationBuilder.DropTable(
                name: "five_force_framework_histories");

            migrationBuilder.DropTable(
                name: "five_step_frame_work_histories");

            migrationBuilder.DropTable(
                name: "hypothetical_discussion_of_issues_comment_files");

            migrationBuilder.DropTable(
                name: "hypothetical_discussion_of_issues_comment_reactions");

            migrationBuilder.DropTable(
                name: "hypothetical_discussion_of_issues_thread_files");

            migrationBuilder.DropTable(
                name: "hypothetical_discussion_of_issues_thread_reactions");

            migrationBuilder.DropTable(
                name: "issue_type_masters");

            migrationBuilder.DropTable(
                name: "management_histories");

            migrationBuilder.DropTable(
                name: "management_plan_histories");

            migrationBuilder.DropTable(
                name: "our_policy_understanding_comment_files");

            migrationBuilder.DropTable(
                name: "our_policy_understanding_comment_reactions");

            migrationBuilder.DropTable(
                name: "our_policy_understanding_thread_files");

            migrationBuilder.DropTable(
                name: "our_policy_understanding_thread_reactions");

            migrationBuilder.DropTable(
                name: "relation_level_histories");

            migrationBuilder.DropTable(
                name: "sharing_of_finance_comment_files");

            migrationBuilder.DropTable(
                name: "sharing_of_finance_comment_reactions");

            migrationBuilder.DropTable(
                name: "sharing_of_finance_thread_files");

            migrationBuilder.DropTable(
                name: "sharing_of_finance_thread_reactions");

            migrationBuilder.DropTable(
                name: "swot_analyses_histories");

            migrationBuilder.DropTable(
                name: "three_c_analysis_histories");

            migrationBuilder.DropTable(
                name: "to_do_comment_files");

            migrationBuilder.DropTable(
                name: "to_do_comment_reactions");

            migrationBuilder.DropTable(
                name: "to_do_thread_files");

            migrationBuilder.DropTable(
                name: "to_do_thread_reactions");

            migrationBuilder.DropTable(
                name: "business_partners");

            migrationBuilder.DropTable(
                name: "business_understanding_discussions");

            migrationBuilder.DropTable(
                name: "commercial_distribution_histories");

            migrationBuilder.DropTable(
                name: "commercial_distribution_templates");

            migrationBuilder.DropTable(
                name: "customer_ideas_understanding_comments");

            migrationBuilder.DropTable(
                name: "esg_and_sd_gs");

            migrationBuilder.DropTable(
                name: "external_environments");

            migrationBuilder.DropTable(
                name: "family_tree_histories");

            migrationBuilder.DropTable(
                name: "five_force_frameworks");

            migrationBuilder.DropTable(
                name: "five_step_frame_works");

            migrationBuilder.DropTable(
                name: "hypothetical_discussion_of_issues_comments");

            migrationBuilder.DropTable(
                name: "managements");

            migrationBuilder.DropTable(
                name: "management_plans");

            migrationBuilder.DropTable(
                name: "our_policy_understanding_comments");

            migrationBuilder.DropTable(
                name: "relation_levels");

            migrationBuilder.DropTable(
                name: "sharing_of_finance_comments");

            migrationBuilder.DropTable(
                name: "swot_analyses");

            migrationBuilder.DropTable(
                name: "three_c_analyses");

            migrationBuilder.DropTable(
                name: "to_do_comments");

            migrationBuilder.DropTable(
                name: "business_understanding_threads");

            migrationBuilder.DropTable(
                name: "commercial_distributions");

            migrationBuilder.DropTable(
                name: "customer_ideas_understanding_threads");

            migrationBuilder.DropTable(
                name: "external_environment_masters");

            migrationBuilder.DropTable(
                name: "family_trees");

            migrationBuilder.DropTable(
                name: "hypothetical_discussion_of_issues_threads");

            migrationBuilder.DropTable(
                name: "our_policy_understanding_threads");

            migrationBuilder.DropTable(
                name: "sharing_of_finance_threads");

            migrationBuilder.DropTable(
                name: "to_do_threads");

            migrationBuilder.DropTable(
                name: "customer_ideas_understandings");

            migrationBuilder.DropTable(
                name: "hypothetical_discussion_of_issues");

            migrationBuilder.DropTable(
                name: "our_policy_understandings");

            migrationBuilder.DropTable(
                name: "sharing_of_finances");

            migrationBuilder.DropTable(
                name: "to_dos");

            migrationBuilder.DropTable(
                name: "business_understandings");

            migrationBuilder.DropTable(
                name: "business_customers");
        }
    }
}
