using System.Text.Json;
using BusinessCustomerUnderstandingService.Domain.Entities;
using BusinessCustomerUnderstandingService.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class ESGAndSDGsConfigration : IEntityTypeConfiguration<ESGAndSDGs>
{
    private JsonSerializerOptions? _jsonSerializerOptions;
    public void Configure(EntityTypeBuilder<ESGAndSDGs> builder)
    {
        _jsonSerializerOptions = new JsonSerializerOptions()
        {
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.
            Create(System.Text.Unicode.UnicodeRanges.All)
        };

        builder.Property(u => u.Id)
            .HasMaxLength(26);

        builder.Property(u => u.Ideal)
            .HasMaxLength(1000);

        builder.Property(u => u.Issue)
            .HasMaxLength(1000);

        builder.Property(u => u.SDGsComment)
            .HasMaxLength(1000);

        builder.Property(u => u.ESGAndSDGsComment)
            .HasMaxLength(1000);

        builder.Property(u => u.EnvironmentAndCarbonNeutralComment)
            .HasMaxLength(1000);

        builder.Property(c => c.SubjectsOfCertificationAndDeclaration)
            .HasColumnName("subjects_of_certification_and_declaration")
            .HasConversion(
                v => JsonSerializer.Serialize(v, _jsonSerializerOptions),
                v => JsonSerializer.Deserialize<IEnumerable<SubjectsOfCertificationAndDeclarationType>>(v, _jsonSerializerOptions)!);

        builder.Property(u => u.SubjectsOfCertificationAndDeclarationComment)
            .HasMaxLength(50);
    }
}
