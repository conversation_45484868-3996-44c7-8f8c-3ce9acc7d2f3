using BusinessCustomerUnderstandingService.Domain.Enums;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThread.GetBusinessUnderstandingThread;

public record GetBusinessUnderstandingThreadResult(
    string Id,
    DateTimeOffset? RegisteredDateTime,
    string Title,
    string? Registrant,
    string? RegistrantId,
    string Description,
    bool? IsCorporateDepositTheme,
    bool? IsFundSettlementTheme,
    string? BusinessUnderstandingId,
    IEnumerable<Domain.Entities.BusinessUnderstandingDiscussion>? Comments,
    IEnumerable<Domain.Entities.BusinessUnderstandingThreadReaction>? Reactions,
    IEnumerable<Domain.Entities.BusinessUnderstandingThreadFile>? Files,
    string? MentionTargetsHtml,
    IEnumerable<string>? MentionTargetUserIds,
    DiscussionPurpose Purpose,
    string? Person,
    bool? IsPersonOfPower,
    DateTimeOffset? CorrespondenceDate,
    string Version
);
