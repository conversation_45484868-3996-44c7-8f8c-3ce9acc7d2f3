using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Domain.Specification;
using MediatR;
using Nut.Results;
using Dto = BusinessCustomerUnderstandingService.Domain.Dto.BusinessUnderstanding;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessUnderstanding;

public class GetBusinessUnderstandingHandler : IRequestHandler<GetBusinessUnderstandingQuery, Result<GetBusinessUnderstandingResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetBusinessUnderstandingHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<GetBusinessUnderstandingResult>> Handle(GetBusinessUnderstandingQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 外部環境マスターに登録されているScoreの最大値を取得
        var maxScoreOfEEM = 0;
        try
        {
            var eemRepository = _unitOfWork.GetRepository<Domain.Entities.ExternalEnvironmentMaster, string>();
            var getResult = await eemRepository.AllAsync();
            var eemEntity = getResult.Get().OrderByDescending(s => s.Score).FirstOrDefault();
            maxScoreOfEEM = (eemEntity != null) ? eemEntity.Score : 0;
        }
        catch
        {
            maxScoreOfEEM = 0;
        }

        // データを取得
        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding, string>();
        return await repository.SingleAsync(new GetBusinessUnderstandingSpecification(request.Id))
            .Map(v => new GetBusinessUnderstandingResult(
                v.Id,
                new Dto.ManagementPlan(v.ManagementPlan!),
                new Dto.Management(v.Management!),
                new Dto.FiveForceFramework(v.FiveForceFramework!),
                new Dto.FiveStepFrameWork(v.FiveStepFrameWork!),
                new Dto.ESGAndSDGs(v.ESGAndSDGs!),
                new Dto.ExternalEnvironment(v.ExternalEnvironment!),
                new Dto.RelationLevel(v.RelationLevel!),
                new Dto.ThreeCAnalysis(v.ThreeCAnalysis!),
                new Dto.SWOTAnalysis(v.SWOTAnalysis!),
                new Dto.CommercialDistribution(v.CommercialDistribution!),
                new Dto.FamilyTree(v.FamilyTree!),
                new Dto.CalculationResult(v.ManagementPlan!, v.Management!, v.FiveForceFramework!, v.FiveStepFrameWork!, v.ExternalEnvironment!, v.ESGAndSDGs!, maxScoreOfEEM, v.RelationLevel!),
                v.BusinessUnderstandingMaterializedView is not null ? v.BusinessUnderstandingMaterializedView.TransactionPolicy : string.Empty,
                v.TransactionPolicyConfirmerId,
                v.TransactionPolicyConfirmedDateTime,
                v.BusinessCustomerId,
                v.Version));
    }
}
