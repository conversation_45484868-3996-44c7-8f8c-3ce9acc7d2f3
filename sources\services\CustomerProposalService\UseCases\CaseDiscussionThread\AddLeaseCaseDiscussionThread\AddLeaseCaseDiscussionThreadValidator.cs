using FluentValidation;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.AddLeaseCaseDiscussionThread;

public class AddLeaseCaseDiscussionThreadValidator : AbstractValidator<AddLeaseCaseDiscussionThreadCommand>
{
    public AddLeaseCaseDiscussionThreadValidator()
    {
        RuleFor(v => v.CaseId).NotEmpty();
        RuleFor(v => v.CustomerIdentificationId).NotEmpty();
        RuleFor(v => v.CustomerName).NotEmpty();
        RuleFor(v => v.RegistrantName).NotEmpty();
        RuleFor(v => v.RegistrantId).NotEmpty();
        RuleFor(v => v.Purpose).IsInEnum().NotEqual(Domain.Enums.CaseDiscussionPurpose.Undefined);
        RuleFor(v => v.Person).MaximumLength(20);
        RuleFor(v => v.Description).NotEmpty().MaximumLength(10000);
        RuleForEach(v => v.ThreadNameTypes).IsInEnum().NotEqual(Domain.Enums.ThreadNameType.Undefined);
        When(v => v.ThreadNameTypes.Any(x => x == Domain.Enums.ThreadNameType.Other) == true, () => {
            RuleFor(v => v.ThreadNameForOther).NotEmpty().MaximumLength(50);
        });
    }
}
