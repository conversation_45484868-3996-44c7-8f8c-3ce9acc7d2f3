using Shared.Spec;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.GetLoanCaseDiscussionThread;

public class GetLoanCaseDiscussionThreadSpecification : BaseSpecification<Domain.Entities.LoanCaseDiscussionThread>
{
    public GetLoanCaseDiscussionThreadSpecification(GetLoanCaseDiscussionThreadQuery request)
    {
        Query
            .Where(x => x.Id == request.Id)
            .Include(x => x.Files)
            .AsNoTracking();
    }
}
