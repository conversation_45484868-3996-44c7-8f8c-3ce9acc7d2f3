using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class HypotheticalDiscussionOfIssuesConfiguration : IEntityTypeConfiguration<HypotheticalDiscussionOfIssues>
{
    public void Configure(EntityTypeBuilder<HypotheticalDiscussionOfIssues> builder)
    {
        builder.HasOne<BusinessUnderstanding>()
            .WithMany(l => l.HypotheticalDiscussionOfIssues)
            .HasForeignKey(j => j.BusinessUnderstandingId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(i => i.CustomerReaction)
            .WithOne(i => i.HypotheticalDiscussionOfIssues)
            .HasForeignKey<CustomerReaction>(l => l.HypotheticalDiscussionOfIssuesId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(entity => entity.StaffId)
            .HasMaxLength(100);

        builder.Property(e => e.RegistrantId)
                .IsRequired();

        builder.Property(e => e.RegistrantName)
                .IsRequired();

        builder.Property(u => u.CurrentSituation)
            .HasMaxLength(10000);

        builder.Property(u => u.Ideal)
            .HasMaxLength(10000);

        builder.Property(u => u.Issue)
            .HasMaxLength(10000);

        builder.Property(u => u.Title)
            .HasMaxLength(64);

        builder.Property(u => u.Status)
            .HasMaxLength(1);

        builder.Property(u => u.Order)
            .HasMaxLength(1);

        builder.Property(entity => entity.ExpiredAt)
            .IsRequired();
    }
}
