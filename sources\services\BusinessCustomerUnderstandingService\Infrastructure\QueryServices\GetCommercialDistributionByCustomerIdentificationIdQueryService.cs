using BusinessCustomerUnderstandingService.Domain.Entities;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.UseCases.CustomerIdentification.GetCommercialDistributionByCustomerIdentificationId;
using Microsoft.EntityFrameworkCore;
using Nut.Results;
using Shared.Results.Errors;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;

internal class GetCommercialDistributionByCustomerIdentificationIdQueryService : IGetCommercialDistributionByCustomerIdentificationIdQueryService
{
    private readonly ApplicationDbContext _dbContext;

    public GetCommercialDistributionByCustomerIdentificationIdQueryService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    public async Task<Result<CommercialDistribution>> Handle(GetCommercialDistributionByCustomerIdentificationIdQuery request)
    {
        var customerIdentificationId = request.CustomerIdentificationId;

        // 商流図取得
        var query = _dbContext.BusinessCustomers.Where(ci => ci.CustomerIdentificationId == customerIdentificationId && ci.IsDeleted == false)
            .Join(
                _dbContext.BusinessUnderstandings
                .Include(x=>x.CommercialDistribution).ThenInclude(x=>x!.Nodes)
                .Include(x=>x.CommercialDistribution).ThenInclude(x=>x!.Edges),
                bc => bc.Id,
                bu => bu.BusinessCustomerId,
                (bc, bu) => new
                {
                    CommercialDistribution = bu.CommercialDistribution
                });

        var result = await query.SingleOrDefaultAsync();

        // 結合条件に該当なし
        if (result == null)
            return Result.Error<CommercialDistribution>(new DataNotFoundException());

        // 結合はできたが商流図をもっていない
        if (result.CommercialDistribution == null)
            return Result.Error<CommercialDistribution>(new DataNotFoundException());

        // 商流図は持っているがNodeが登録されていない
        if (result.CommercialDistribution.Nodes.Count == 0)
            return Result.Error<CommercialDistribution>(new DataNotFoundException());


        return Result.Ok(result.CommercialDistribution);
    }
}
