`Infrastructure/Persistence/Migrations`ディレクトリには、データベースマイグレーションを行うためのファイルを配置します。
多くの場合、EntityFrameworkCoreのマイグレーションファイルになります。

ここにマイグレーションを追加するには、ソリューションがあるディレクトリから次のコマンドを利用してください。

```
# EmployeeService
$env:DOTNET_ENVIRONMENT='Local'
dotnet ef migrations add "initial" --project services/BusinessCustomerUnderstandingService --startup-project frontends/CustomerUnderstandingApi --output-dir Infrastructure/Persistence/Migrations --context BusinessCustomerUnderstandingService.Infrastructure.Persistence.ApplicationDbContext
```

このファイルは開発初期以降は削除してください。
