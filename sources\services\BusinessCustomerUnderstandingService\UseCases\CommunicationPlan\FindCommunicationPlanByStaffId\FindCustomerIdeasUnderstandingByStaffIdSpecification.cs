using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.FindCommunicationPlanByStaffIds;

public class FindCustomerIdeasUnderstandingByStaffIdSpecification : BaseSpecification<Domain.Entities.CustomerIdeasUnderstanding>
{
    public FindCustomerIdeasUnderstandingByStaffIdSpecification(string staffId)
    {
        Query
            .Where(e => e.StaffId == staffId)
            .Where(e => e.Status != Domain.Enums.Status.Completed)
            .AsNoTracking();
    }
}
