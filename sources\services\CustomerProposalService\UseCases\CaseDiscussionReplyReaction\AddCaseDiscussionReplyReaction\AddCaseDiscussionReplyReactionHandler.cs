using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionReplyReaction.AddCaseDiscussionReplyReaction;

public class AddCaseDiscussionReplyReactionHandler : IRequestHandler<AddCaseDiscussionReplyReactionCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;

    public AddCaseDiscussionReplyReactionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<string>> Handle(AddCaseDiscussionReplyReactionCommand request, CancellationToken cancellationToken)
    {
        if (request is null) throw new ArgumentNullException(nameof(request));

        var newData = new Domain.Entities.CaseDiscussionReplyReaction()
        {
            Id = Ulid.NewUlid().ToString(),
            CaseDiscussionReplyId = request.CaseDiscussionReplyId,
            ReactionType = request.ReactionType,
            UpdaterId = request.UpdaterId,
            UpdaterName = request.UpdaterName,
            UpdatedDateTime = request.UpdatedDateTime,
        };

        var repository = _unitOfWork.GetRepository<Domain.Entities.CaseDiscussionReplyReaction>();

        return await repository
            .AddAsync(newData)
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync())
            .FlatMap(() => Result.Ok(newData.Id))
            .ConfigureAwait(false);
    }
}
