namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessEvaluationAtTheTime;

public class GetBusinessEvaluationAtTheTimeResult
{
    public string Id { get; set; } = default!;
    public Domain.Entities.BusinessUnderstanding Current { get; set; } = default!;
    public Domain.Entities.ManagementPlanHistory ManagementPlan { get; set; } = default!;
    public Domain.Entities.ManagementHistory Management { get; set; } = default!;
    public Domain.Entities.FiveForceFrameworkHistory FiveForceFramework { get; set; } = default!;
    public Domain.Entities.FiveStepFrameWorkHistory FiveStepFrameWork { get; set; } = default!;
    public Domain.Entities.ESGAndSDGsHistory ESGAndSDGs { get; set; } = default!;
    public Domain.Entities.ExternalEnvironmentHistory ExternalEnvironment {  get; set; } = default!;
};
