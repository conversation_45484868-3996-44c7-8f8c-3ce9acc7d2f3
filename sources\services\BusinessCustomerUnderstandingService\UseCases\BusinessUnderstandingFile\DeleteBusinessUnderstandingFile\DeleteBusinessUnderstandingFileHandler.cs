using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Infrastructure.Storage;
using BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingFile.FindBusinessUnderstandingFile;
using MediatR;
using Nut.Results;
using Shared.ObjectStorage;
using Shared.Results.Errors;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingFile.DeleteBusinessUnderstandingFile;

public class DeleteBusinessUnderstandingFileHandler : IRequestHandler<DeleteBusinessUnderstandingFileCommand, Result<DeleteBusinessUnderstandingFileResult>>
{
    private readonly IBusinessUnderstandingStorageClientProvider _objectStorageClientProvider;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteBusinessUnderstandingFileHandler(
        IBusinessUnderstandingStorageClientProvider objectStorageClientProvider,
        IUnitOfWork unitOfWork)
    {
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<DeleteBusinessUnderstandingFileResult>> Handle(DeleteBusinessUnderstandingFileCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // Blobとの接続を確立
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);


        var findRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingFile>();
        var spec = new DeleteBusinessUnderstandingFileSpecification(request);
        var result = await findRepository.FindAsync(spec);

        if (result.IsError)
        {
            throw new Exception(nameof(FindBusinessUnderstandingFileHandler));
        }

        var businessUnderstandingFile = result.Get().FirstOrDefault();
        if (businessUnderstandingFile is null)
        {
            return Result.Ok(new DeleteBusinessUnderstandingFileResult());
        }

        // リポジトリで事業性理解ファイル情報を削除します
        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingFile>();
        await repository.DeleteAsync(businessUnderstandingFile).ConfigureAwait(false);
        await _unitOfWork.SaveEntitiesAsync().ConfigureAwait(false);

        // Blobに保存されているオブジェクトの一覧を取得
        var objects = await storageClient.GetObjectInformationsAsync($"business-understanding/{request.BusinessUnderstandingId}/").ConfigureAwait(false);
        if (objects.IsError) return objects.PreserveErrorAs<DeleteBusinessUnderstandingFileResult>();

        // Blobから指定されたファイルを取得
        var target = objects.Get().Where(e => e.Name == $"business-understanding/{request.BusinessUnderstandingId}/{request.FileName}").FirstOrDefault();
        if (target is null) return Result.Error<DeleteBusinessUnderstandingFileResult>(new DataNotFoundException());

        await storageClient.DeleteAsync($"business-understanding/{request.BusinessUnderstandingId}/{request.FileName}");

        return Result.Ok(new DeleteBusinessUnderstandingFileResult());
    }
}
