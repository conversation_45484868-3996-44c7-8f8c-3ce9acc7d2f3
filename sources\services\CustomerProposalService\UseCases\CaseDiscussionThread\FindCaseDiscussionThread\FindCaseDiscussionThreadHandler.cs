using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;
using Shared.AzureBlob.QuillContents;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.FindCaseDiscussionThread;

public class FindCaseDiscussionThreadHandler : IRequestHandler<FindCaseDiscussionThreadQuery, Result<IEnumerable<FindCaseDiscussionThreadResult>>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IQuillContentsUtility _quillContentsUtility;

    public FindCaseDiscussionThreadHandler(IUnitOfWork unitOfWork, IQuillContentsUtility quillContentsUtility)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _quillContentsUtility = quillContentsUtility ?? throw new ArgumentNullException(nameof(quillContentsUtility));
    }

    public async Task<Result<IEnumerable<FindCaseDiscussionThreadResult>>> Handle(FindCaseDiscussionThreadQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CaseDiscussionThread, string>();
        // スレッドの更新日時の降順でソート
        var result = await repository.FindAsync(new FindCaseDiscussionThreadSpecification(
            request.CaseId,
            request.FromDate,
            request.ToDate))
            .ConfigureAwait(false);
        if (result.IsError) return result.PreserveErrorAs<IEnumerable<FindCaseDiscussionThreadResult>>();

        var data = result.Get();
        foreach (var d in data.Where(x => x.Replies != null))
        {
            foreach (var r in d.Replies!)
            {
                r.Description = await _quillContentsUtility.GetArrangedDeltaWhenPublish(r.Description).Get();
            }
        }

        // コメントが登録されている場合は最新のコメント登録時間が比較対象になる
        return result.Map(cd =>
            cd.Select(x => new FindCaseDiscussionThreadResult(
                x.Id,
                x.CaseId,
                x.RegisteredAt,
                x.RegistrantName,
                x.RegistrantId,
                x.ThreadType,
                x.ThreadName,
                x.DisplayText,
                x.Replies,
                x.Files,
                x.Reactions,
                x.MentionTargetsHtml,
                x.MentionTargetUserIds,
                x.Version)));
    }
}
