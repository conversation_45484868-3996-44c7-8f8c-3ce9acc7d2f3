using FluentValidation;

namespace CustomerProposalService.UseCases.Case.UpdateAcceptanceAndGuaranteeCase;

public class UpdateAcceptanceAndGuaranteeCaseValidator : AbstractValidator<UpdateAcceptanceAndGuaranteeCaseCommand>
{
    public UpdateAcceptanceAndGuaranteeCaseValidator()
    {
        // Case Entity
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.CaseName).NotEmpty().MaximumLength(50);
        RuleFor(v => v.CaseStatus).NotEmpty().IsInEnum();
        RuleFor(e => e.CaseOutline).CaseOutline();
        RuleFor(v => v.StaffId).NotEmpty().MaximumLength(50);
        RuleFor(v => v.StaffName).NotEmpty().MaximumLength(50);
        // AcceptanceAndGuaranteeCase Entity
        RuleFor(e => e.InterestRateCustom).MaximumLength(50);
        RuleFor(e => e.RepaymentType).MaximumLength(50);
        RuleFor(e => e.CollateralType).MaximumLength(50);
        RuleFor(e => e.CollateralOrGuaranteeCustom).MaximumLength(50);
        RuleFor(e => e.GuaranteeType).MaximumLength(50);
        RuleFor(e => e.CancelTypeOfLoan).IsInEnum();
        RuleFor(e => e.CancelReason).CancelReason();
        RuleFor(e => e.TrafficSource).IsInEnum();
        RuleFor(v => v.Version).NotEmpty();
    }
}
