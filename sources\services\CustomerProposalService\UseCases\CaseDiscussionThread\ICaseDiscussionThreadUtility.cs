using CustomerProposalService.Domain.Enums;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionThread;

public interface ICaseDiscussionThreadUtility
{
    Task<Result<IEnumerable<string>>> SendNotifyAsync<T>(
        T caseDiscussionThread,
        IEnumerable<string>? mentionTargetTeamMemberUserIds,
        string customerName,
        string? customerStaffId,
        NotificationType Type
        ) where T : Domain.Entities.CaseDiscussionThread;
    Task<Result<string>> UpdateCaseLastUpdatedAt(string caseId, DateTimeOffset updateTime);
}
