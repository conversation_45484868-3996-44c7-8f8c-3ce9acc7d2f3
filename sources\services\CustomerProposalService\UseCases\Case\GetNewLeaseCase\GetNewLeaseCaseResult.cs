using CustomerProposalService.Domain.Dto;
using CustomerProposalService.Domain.Enums;

namespace CustomerProposalService.UseCases.Case.GetNewLeaseCase;

public record GetNewLeaseCaseResult(
    // Case Entity
    string Id,
    Guid CustomerIdentificationId,
    CaseCategory CaseCategory,
    string CaseName,
    CaseStatus CaseStatus,
    string? CaseOutline,
    DateTimeOffset? ExpiredAt,
    string StaffId,
    string StaffName,
    DateTimeOffset RegisteredAt,
    DateTimeOffset CaseUpdatedAt,
    DateTimeOffset LastUpdatedAt,
    IEnumerable<Domain.Entities.CaseFile>? CaseFiles,
    IEnumerable<Domain.Entities.CaseLink>? CaseLinks,
    string Version,
    // NewLeaseCase Entity
    LeaseCaseType? LeaseCaseType,
    decimal Amount,
    IEnumerable<LeaseSituation>? LeaseSituations,
    string? LeaseStaffId,
    string? LeaseStaffName,
    CancelType? CancelType,
    string? CancelReason,
    PropertyCategory? PropertyCategory,
    PropertyStatus? PropertyStatus,
    IEnumerable<PropertyContractType>? PropertyContractTypes,
    DateTimeOffset? ContractScheduledAt,
    string? BusinessMeetingNumber,
    string? QuotationNumber,
    IEnumerable<LeaseDetailDto>? LeaseDetails,
    PaymentCycle? PaymentCycle,
    IEnumerable<ResidualValueSetting>? ResidualValueSettings,
    QuotationProcurement? QuotationProcurement,
    int? ScheduledMonthlyMileage,
    InstallationLocation? InstallationLocation,
    string? InstallationLocationCustom,
    string? QuotationNote,
    DateTimeOffset? ContractDate,
    DateTimeOffset? AcceptanceCertificateDate,
    WorkStatus? CaseInputStatus,
    DateTimeOffset? CaseInputCompletedAt,
    bool? IsConsultationTarget,
    ConsultationStatus? ConsultationStatus,
    DateTimeOffset? ConsultationCompletedAt,
    string? QuotationCreateStaffId,
    string? QuotationCreateStaffName,
    WorkStatus? QuotationCreateStatus,
    DateTimeOffset? QuotationCreateCompletedAt,
    string? QuotationScrutinizeStaffId,
    string? QuotationScrutinizeStaffName,
    WorkStatus? QuotationScrutinizeStatus,
    DateTimeOffset? QuotationScrutinizeCompletedAt,
    ProposalResult? CustomerProposalResult,
    DateTimeOffset? CustomerProposalCompletedAt,
    string? IndividualApplicationStaffId,
    string? IndividualApplicationStaffName,
    WorkStatus? IndividualApplicationStatus,
    DateTimeOffset? IndividualApplicationCompletedAt,
    bool IsEarthquakeRelated,
    TrafficSource? TrafficSource
);
