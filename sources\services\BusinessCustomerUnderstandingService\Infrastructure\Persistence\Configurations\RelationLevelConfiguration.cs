using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class RelationLevelConfiguration : IEntityTypeConfiguration<RelationLevel>
{
    public void Configure(EntityTypeBuilder<RelationLevel> builder)
    {
        builder.Property(entity => entity.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(entity => entity.BusinessUnderstandingId)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(entity => entity.InterviewComments)
            .HasMaxLength(1000);

        builder.Property(entity => entity.RelationshipComments)
            .HasMaxLength(1000);

        builder.Property(entity => entity.DisclosureComments)
            .HasMaxLength(1000);

        builder.Property(entity => entity.RelationLevelComment)
            .HasMaxLength(1000);

        builder.Property(entity => entity.Interviewer)
            .HasMaxLength(100);

        builder.Property(entity => entity.Interviewee)
            .HasMaxLength(100);

        builder.Property(entity => entity.InterviewDetail)
            .HasMaxLength(1000);

        builder.HasOne(entity => entity.BusinessUnderstanding)
            .WithOne(entity => entity.RelationLevel)
            .HasForeignKey<RelationLevel>(entity => entity.BusinessUnderstandingId);
    }
}
