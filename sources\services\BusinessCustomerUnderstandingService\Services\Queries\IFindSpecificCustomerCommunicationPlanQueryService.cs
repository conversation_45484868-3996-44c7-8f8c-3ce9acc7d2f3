using BusinessCustomerUnderstandingService.Domain.Dto.BusinessUnderstanding;
using BusinessCustomerUnderstandingService.Domain.Enums;
using Nut.Results;
using Shared.Application;

namespace BusinessCustomerUnderstandingService.Services.Queries;

public interface IFindSpecificCustomerCommunicationPlanQueryService
{
    Task<Result<PaginatedResult<CommunicationPlanBasicInfo>>> Handle(
        string id,
        string? title,
        string? staffName,
        IEnumerable<CommunicationPlanType> types,
        IEnumerable<Status?> statuses,
        DateTimeOffset? expiredAtFrom,
        DateTimeOffset? expiredAtTo,
        PaginationOption option);
}
