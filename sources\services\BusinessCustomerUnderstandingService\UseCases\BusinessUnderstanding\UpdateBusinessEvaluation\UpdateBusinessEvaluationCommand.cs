using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.UpdateBusinessEvaluation;

[WithDefaultBehaviors]
public record UpdateBusinessEvaluationCommand(
    string Id,
    string UpdaterId,
    string UpdaterDisplayName,
    string Version,
    Domain.Entities.ManagementPlan ManagementPlan,
    Domain.Entities.Management Management,
    Domain.Entities.FiveForceFramework FiveForceFramework,
    Domain.Entities.FiveStepFrameWork FiveStepFrameWork,
    Domain.Entities.ESGAndSDGs ESGAndSDGs,
    Domain.Entities.ExternalEnvironment ExternalEnvironment
    ) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
