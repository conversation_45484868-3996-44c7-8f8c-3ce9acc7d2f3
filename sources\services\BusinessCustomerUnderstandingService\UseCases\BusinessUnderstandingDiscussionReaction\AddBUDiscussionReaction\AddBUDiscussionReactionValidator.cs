using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussionReaction.AddBUDiscussionReaction;

public class AddBUDiscussionReactionValidator : AbstractValidator<AddBUDiscussionReactionCommand>
{
    public AddBUDiscussionReactionValidator()
    {
        RuleFor(v => v.CommentId).NotEmpty();
        RuleFor(v => v.StaffId).NotEmpty();
        RuleFor(v => v.StaffName).NotEmpty();
        RuleFor(v => v.ReactionType).NotEmpty();
        RuleFor(v => v.UpdatedDateTime).NotEmpty();
    }
}
