using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.BatchUpdateCommunicationPlanStaff;

public class FindBusinessCustomersByCustomerIdentificationIdsSpecification : BaseSpecification<Domain.Entities.BusinessCustomer>
{
    public FindBusinessCustomersByCustomerIdentificationIdsSpecification(List<Guid> customerIdentificationIdList)
    {
        Query
            .Where(x => customerIdentificationIdList.Contains(x.CustomerIdentificationId))
            .AsNoTracking();
    }
}
