using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Externals.BusinessCustomerUnderstanding;
using BusinessCustomerUnderstandingService.Externals.CustomerFixedInformation;
using BusinessCustomerUnderstandingService.Externals.CustomerProposal;
using BusinessCustomerUnderstandingService.Services.Domain;
using MediatR;
using Nut.Results;
using Shared.Domain;
using Shared.Messaging;
using Shared.Services;
using SharedKernel.ExternalApi.MessageContract.Notification;
using SharedKernel.ExternalApi.Domain.Notification;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThread.UpdateBusinessUnderstandingThread;

public class UpdateBusinessUnderstandingThreadHandler : IRequestHandler<UpdateBusinessUnderstandingThreadCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDatetimeService;
    private readonly ICurrentUserService _currentUserService;
    private readonly IMessageSender<FindCustomerStaff, IEnumerable<FindCustomerStaffResult>> _customerStaffMessageSender;
    private readonly IMessageSender<AddBusinessUnderstandingCommentReplyNotificationQuery, string> _sender;
    private readonly IMessageSender<UpdateBusinessUnderstandingUpdatedDateTime> _businessUnderstandingUpdateMessageSender;
    private readonly IFileProcessingService _fileProcessingService;
    private readonly IQuillImageService _quillImageService;
    private readonly IMessageSender<FindTeamMemberByTeamId, IEnumerable<FindTeamMemberByTeamIdResult>> _findTeamMemberByTeamIdSender;

    private readonly string _containerName = "business-understanding";
    private readonly string _folderName = "thread";

    public UpdateBusinessUnderstandingThreadHandler(
        IUnitOfWork unitOfWork,
        ICurrentDateTimeService currentDatetimeService,
        ICurrentUserService currentUserService,
        IMessageSender<FindCustomerStaff, IEnumerable<FindCustomerStaffResult>> customerStaffMessageSender,
        IMessageSender<AddBusinessUnderstandingCommentReplyNotificationQuery, string> sender,
        IMessageSender<UpdateBusinessUnderstandingUpdatedDateTime> businessUnderstandingUpdateMessageSender,
        IFileProcessingService fileProcessingService,
        IQuillImageService quillImageService,
        IMessageSender<FindTeamMemberByTeamId, IEnumerable<FindTeamMemberByTeamIdResult>> teamMemberByTeamId)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentDatetimeService = currentDatetimeService ?? throw new ArgumentNullException(nameof(currentDatetimeService));
        _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
        _customerStaffMessageSender = customerStaffMessageSender ?? throw new ArgumentNullException(nameof(customerStaffMessageSender));
        _sender = sender ?? throw new ArgumentNullException(nameof(sender));
        _businessUnderstandingUpdateMessageSender = businessUnderstandingUpdateMessageSender ?? throw new ArgumentNullException(nameof(businessUnderstandingUpdateMessageSender));
        _fileProcessingService = fileProcessingService ?? throw new ArgumentNullException(nameof(fileProcessingService));
        _quillImageService = quillImageService ?? throw new ArgumentNullException(nameof(quillImageService));
        _findTeamMemberByTeamIdSender = teamMemberByTeamId ?? throw new ArgumentNullException(nameof(teamMemberByTeamId));
    }

    public async Task<Result<string>> Handle(UpdateBusinessUnderstandingThreadCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingThread, string>();
        // 既存データを取得します。
        var getResult = await repository.SingleAsync(
            new FindByIdSpecification<Domain.Entities.BusinessUnderstandingThread, string>(request.Id)
            .Include(c => c.Files)
            ).ConfigureAwait(false);
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        // 本文添付ファイルの処理
        var newDelta = await _quillImageService.ArrangeContentByUrlWhenSaveAsync(_containerName, _folderName, request.Description, request.Id);
        if (newDelta.IsError) return newDelta;

        var currentData = getResult.Get();
        currentData.Title = request.Title;
        currentData.Description = newDelta.Get();
        currentData.MentionTargetsHtml = request.MentionTargetsHtml;
        currentData.MentionTargetUserIds = request.MentionTargetUserIds;
        currentData.MentionTargetTeamIds = request.MentionTargetTeamIds;
        currentData.Purpose = request.Purpose;
        currentData.Person = request.Person;
        currentData.IsPersonOfPower = request.IsPersonOfPower;
        currentData.CorrespondenceDate = request.CorrespondenceDate;
        currentData.IsCorporateDepositTheme = request.IsCorporateDepositTheme;
        currentData.IsFundSettlementTheme = request.IsFundSettlementTheme;

        // バージョン番号は、同時実行制御を有効にするために引数で受け取ったデータの値で必ず上書きします。
        currentData.Version = request.Version;

        var filesRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingThreadFile, string>();

        // BusinessUnderstandingIdを取得
        var businessUnderstandingRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding>();
        var getBusinessUnderstandingResult = await businessUnderstandingRepository.SingleAsync(new FindByIdSpecification<Domain.Entities.BusinessUnderstanding, string>(currentData.BusinessUnderstandingId));
        if (getBusinessUnderstandingResult.IsError) return getBusinessUnderstandingResult.PreserveErrorAs<string>();
        var businessUnderstanding = getBusinessUnderstandingResult.Get();
        var businessUnderstandingId = businessUnderstanding.Id;

        // 通知先
        var notifyTargetUserIds = new HashSet<string>();
        var notifyMentionTargetUserIds = new HashSet<string>();

        // 顧客担当者
        var businessCustomerRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessCustomer>();
        var getBusinessCustomerResult = await businessCustomerRepository.SingleAsync(new FindByIdSpecification<Domain.Entities.BusinessCustomer, string>(businessUnderstanding.BusinessCustomerId));
        if (getBusinessCustomerResult.IsError) return getBusinessCustomerResult.PreserveErrorAs<string>();
        var businessCustomer = getBusinessCustomerResult.Get();

        var findCustomerStaffResult = await _customerStaffMessageSender.SendAsync(new FindCustomerStaff { CustomerIdentificationIds = new List<Guid>() { businessCustomer.CustomerIdentificationId } });
        if (findCustomerStaffResult.IsError) return findCustomerStaffResult.PreserveErrorAs<string>();
        var staffId = findCustomerStaffResult.Get().FirstOrDefault()?.StaffId;

        if (!string.IsNullOrEmpty(staffId) && currentData.RegistrantId != staffId && !notifyTargetUserIds.Contains(staffId))
        {
            notifyTargetUserIds.Add(staffId);
        }

        // メンション
        if (request.MentionTargetUserIds != null)
        {
            var mentionTargetUserIds = request.MentionTargetUserIds.Where(m => m != null && m != string.Empty).ToList();
            if (mentionTargetUserIds.Count() > 0)
            {
                notifyMentionTargetUserIds.UnionWith(mentionTargetUserIds.Where(x => !x.Equals(currentData.RegistrantId)));
            }
        }

        if (request.MentionTargetTeamMemberUserIds is not null)
        {
            var mentionTargetUserIds = request.MentionTargetTeamMemberUserIds.Where(m => m != null && m != string.Empty);
            if (mentionTargetUserIds.Any())
            {
                notifyMentionTargetUserIds.UnionWith(mentionTargetUserIds.Where(x => !x.Equals(currentData.RegistrantId)));
            }
        }

        // メンション通知
        if (notifyMentionTargetUserIds.Any())
        {
            var notificationResult = await _sender.SendAsync(new AddBusinessUnderstandingCommentReplyNotificationQuery()
            {
                TriggerdUserId = currentData.RegistrantId,
                TriggeredUserName = currentData.Registrant,
                CommentId = currentData.Id, // スレッドが更新されたため、コメントIDはスレッドIDのことを指す
                CustomerName = request.CustomerName,
                ThreadTitle = request.Title,
                Items = notifyMentionTargetUserIds.Select(x => new AddBusinessUnderstandingCommentReplyNotificationCommandItem(businessUnderstandingId, x)),
                Type = NotificationType.Mention
            })
            .ConfigureAwait(false);
        }

        // メンション通知が優先のため、自動通知先から除外する
        notifyTargetUserIds = new HashSet<string>(notifyTargetUserIds.Except(notifyMentionTargetUserIds));

        // 自動通知
        if (notifyTargetUserIds.Any())
        {
            var notificationResult = await _sender.SendAsync(new AddBusinessUnderstandingCommentReplyNotificationQuery()
            {
                TriggerdUserId = currentData.RegistrantId,
                TriggeredUserName = currentData.Registrant,
                CommentId = currentData.Id, // スレッドが更新されたため、コメントIDはスレッドIDのことを指す
                CustomerName = request.CustomerName,
                ThreadTitle = request.Title,
                Items = notifyTargetUserIds.Select(x => new AddBusinessUnderstandingCommentReplyNotificationCommandItem(businessUnderstandingId, x)),
                Type = NotificationType.Update
            }).ConfigureAwait(false);
        }

        // 事業性理解最終更新日の更新
        var updateBusinessUnderstandingResult = await _businessUnderstandingUpdateMessageSender.SendAsync(new UpdateBusinessUnderstandingUpdatedDateTime()
        {
            BusinessUnderstandingId = businessUnderstandingId,
            UpdaterId = (await _currentUserService.GetAsync()).UserId!,
            UpdaterName = request.Registrant
        });
        if (updateBusinessUnderstandingResult.IsError) return updateBusinessUnderstandingResult.PreserveErrorAs<string>();

        // ファイル処理
        if (request.UploadFiles != null || request.FilesToRemove != null)
        {
            var uploadResult = await _fileProcessingService.UpdateFiles<Domain.Entities.BusinessUnderstandingThread, Domain.Entities.BusinessUnderstandingThreadFile>(currentData, _containerName, _folderName, request.UploadFiles, request.FilesToRemove);
            if (uploadResult.IsError) return uploadResult;
        }

        return await repository
            .UpdateAsync(currentData) // データを更新として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
