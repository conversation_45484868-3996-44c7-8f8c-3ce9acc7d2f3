using FluentValidation;

namespace CustomerProposalService.UseCases.CaseDiscussionThreadReaction.UpdateCaseDiscussionThreadReaction;

public class UpdateCaseDiscussionThreadReactionValidator : AbstractValidator<UpdateCaseDiscussionThreadReactionCommand>
{
    public UpdateCaseDiscussionThreadReactionValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.ReactionType).IsInEnum();
        RuleFor(v => v.UpdatedDateTime).NotEmpty();
        RuleFor(v => v.Version).NotEmpty();
    }
}
