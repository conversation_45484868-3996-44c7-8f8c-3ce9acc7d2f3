using BusinessCustomerUnderstandingService.Infrastructure.Storage;
using Microsoft.AspNetCore.Http;
using Nut.Results;
using Shared.Services;

namespace BusinessCustomerUnderstandingService.Services.Domain;


public class FileProcessingService : IFileProcessingService
{
    private readonly BusinessCustomerUnderstandingService.Domain.IUnitOfWork _unitOfWork;
    private readonly IStorageClientProvider _objectStorageClientProvider;
    private readonly ICurrentDateTimeService _currentDateTimeService;

    public FileProcessingService(
         BusinessCustomerUnderstandingService.Domain.IUnitOfWork unitOfWork,
         IStorageClientProvider objectStorageClientProvider,
         ICurrentDateTimeService currentDateTimeService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));

    }

    /// <summary>
    /// スレッド、またはコメントの添付ファイルをアップロード、ダウンロード、削除、上書きします
    /// </summary>
    /// <typeparam name="TData">スレッド、またはコメントのデータ（例：IssueProjectDiscussionThread）</typeparam>
    /// <typeparam name="TFileType">スレッド、またはコメントのファイルタイプ（例：IssueProjectDiscussionThreadFile)</typeparam>
    /// <param name="currentData">TDataのアイテム</param>
    /// <param name="containerName">ストレージクライアントのconnection-string（例："issue-project-discussion")</param>
    /// <param name="folderName">ディレクトリパス（例："issue-project-discussion-thread")</param>
    /// <param name="uploadFiles">アップロードするつもりのファイルリスト</param>
    /// <param name="filesToRemove">削除するつもりのファイル名のリスト</param>
    /// <returns></returns>emoveは削除するつもりのファイル名のリスト

    public async Task<Result<string>> UpdateFiles<TData, TFileType>(
        TData currentData,
        string containerName,
        string folderName,
        IEnumerable<IFormFile>? uploadFiles,
        IEnumerable<string>? filesToRemove)
        where TData : IFileProcessable<TFileType>
        where TFileType : class, new()
    {
        return await ProcessFiles<TData, TFileType>(currentData, containerName, folderName, uploadFiles, filesToRemove);
     }

    private async Task<Result<string>> ProcessFiles<TData, TFileType>(
        TData currentData,
        string containerName,
        string _filePathPrefix,
        IEnumerable<IFormFile>? uploadFiles,
        IEnumerable<string>? filesToRemove)
        where TData : IFileProcessable<TFileType>
        where TFileType : class, new()
    {

        //Blobとの接続を確立
        var storageClient = await _objectStorageClientProvider.CreateAsync(containerName).ConfigureAwait(false);

        var repository = _unitOfWork.GetRepository<TFileType>();

        // 存在しているファイルを取得
        // generic型の制約で表現できないので、ここでキャストする
        var existFiles = currentData.Files as IEnumerable<IFileType>;
        if (existFiles is null){
            existFiles = new List<IFileType>();   
        } 

        var currentId = currentData.Id;

        // 削除対象の内の既存ファイルを抽出する
        var filesToRemoveFromExistFiles = new List<IFileType>();

        // ファイルは削除すると同時に、同じファイル（名）はアップロードする用のリスト
        var filesToIgnoreAfterLoop = new List<IFileType>();

        if (filesToRemove != null && filesToRemove.Any())
        {
            filesToRemoveFromExistFiles = filesToRemove.Join(
                existFiles,
                fileName => fileName,
                existFile => existFile.FileName,
                (_, existFile) => existFile
            ).ToList();

            foreach (var fileToRemove in filesToRemoveFromExistFiles)
            {
                var fileAsTFileType = (TFileType) fileToRemove;
                var deleteResult = await repository.DeleteAsync(fileAsTFileType).ConfigureAwait(false);
                if (deleteResult.IsError) return deleteResult.PreserveErrorAs<string>();

                var fileName = fileToRemove.FileName;

                // Blobに保存されているファイルを削除
                var checkFileResult = await storageClient.ExistsAsync($"{_filePathPrefix}/{currentId}/{fileName}").ConfigureAwait(false);
                var deleteFileResult = await storageClient.DeleteAsync($"{_filePathPrefix}/{currentId}/{fileName}").ConfigureAwait(false);
                if (deleteFileResult.IsError) return deleteFileResult.PreserveErrorAs<string>();

                filesToIgnoreAfterLoop.Add(fileToRemove);
            }
        }

        // existFilesから、filesToIgnoreAfterLoopのファイルを抜きます
        existFiles = existFiles.Except(filesToIgnoreAfterLoop).ToList();
        
        if (uploadFiles == null || !uploadFiles.Any()) return Result.Ok("");

        // 受け取ったファイルを登録・更新
        foreach (var (file, idx) in uploadFiles.Select((file, idx) => (file, idx)))
        {
            if (existFiles.Any(existFile => existFile.FileName == file.FileName))
            {
                // 同じファイル名が存在しているので更新
                var updateFile = existFiles.Where(existFile => existFile.FileName == file.FileName).First();

                updateFile.FileName = file.FileName;
                updateFile.UpdatedAt = _currentDateTimeService.NowDateTimeOffset();
                updateFile.UpdatedDateTime = _currentDateTimeService.NowDateTimeOffset();
                updateFile.UpdatedBy = currentData.RegistrantId;
                updateFile.UpdaterId = currentData.RegistrantId;
                updateFile.UpdaterName = currentData.Registrant;

                var updateResult = await repository.UpdateAsync((TFileType)updateFile).ConfigureAwait(false);
                if (updateResult.IsError) return updateResult.PreserveErrorAs<string>();

            }
            else
            {
                var newFile = (IFileType)(new TFileType());


                newFile.Id = Ulid.NewUlid().ToString();
                newFile.FileName = file.FileName;
                newFile.TargetId = currentData.Id;
                newFile.UpdatedAt = _currentDateTimeService.NowDateTimeOffset();
                newFile.UpdatedDateTime = _currentDateTimeService.NowDateTimeOffset();
                newFile.UpdatedBy = currentData.RegistrantId;
                newFile.UpdaterId = currentData.RegistrantId;
                newFile.UpdaterName = currentData.Registrant;

                var addResult = await repository.AddAsync((TFileType)newFile).ConfigureAwait(false);
                if (addResult.IsError) return addResult.PreserveErrorAs<string>();
                
            }

            // Blobに登録
            var stream = file.OpenReadStream();
            var postResult = await storageClient.PostAsync($"{_filePathPrefix}/{currentId}/{file.FileName}", stream).ConfigureAwait(false);
            if (postResult.IsError) return postResult.PreserveErrorAs<string>();

        }
        return Result.Ok("");
    }

}


