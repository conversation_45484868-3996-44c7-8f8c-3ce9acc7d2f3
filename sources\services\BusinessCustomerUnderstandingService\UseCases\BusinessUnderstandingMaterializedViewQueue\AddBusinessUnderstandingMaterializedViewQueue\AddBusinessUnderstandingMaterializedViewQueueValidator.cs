using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingMaterializedViewQueue.AddBusinessUnderstandingMaterializedViewQueue;

public class AddBusinessUnderstandingMaterializedViewQueueValidator : AbstractValidator<AddBusinessUnderstandingMaterializedViewQueueCommand>
{
    public AddBusinessUnderstandingMaterializedViewQueueValidator()
    {
        RuleFor(v => v.CustomerIdentificationIds).NotEmpty();
        RuleFor(v => v.UpdaterId).NotEmpty();
        RuleFor(v => v.UpdaterName).NotEmpty();
    }
}
