using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingFile.GetBUFByBusinessUnderstandingId;

public class GetBUFByBusinessUnderstandingIdHandler : IRequestHandler<GetBUFByBusinessUnderstandingIdQuery, Result<List<GetBUFByBusinessUnderstandingIdResult>>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetBUFByBusinessUnderstandingIdHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<List<GetBUFByBusinessUnderstandingIdResult>>> Handle(GetBUFByBusinessUnderstandingIdQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingFile, string>();

        var result = repository.FindAsync(new GetBUFByBusinessUnderstandingIdSpecification(request.BusinessUnderstandingId));

        return result.Map(val => val.Select(v => new GetBUFByBusinessUnderstandingIdResult(
                v.Id,
                v.FileName,
                v.UpdatedDateTime,
                v.UpdaterId,
                v.UpdaterName,
                v.BusinessUnderstandingId,
                v.Version
                )).ToList());
    }
}
