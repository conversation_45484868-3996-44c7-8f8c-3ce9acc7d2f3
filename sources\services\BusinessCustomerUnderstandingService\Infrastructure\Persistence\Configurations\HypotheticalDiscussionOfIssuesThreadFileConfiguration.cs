using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;

public class HypotheticalDiscussionOfIssuesThreadFileConfiguration : IEntityTypeConfiguration<HypotheticalDiscussionOfIssuesThreadFile>
{
    public void Configure(EntityTypeBuilder<HypotheticalDiscussionOfIssuesThreadFile> builder)
    {
        builder.HasOne<HypotheticalDiscussionOfIssuesThread>()
            .WithMany(l => l.Files)
            .HasForeignKey(j => j.ThreadId)
            .HasConstraintName("fk_hdoi_thread_hdoi_file_thread_id")
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMax<PERSON>ength(26);

        builder.Property(u => u.FileName)
            .IsRequired()
            .HasMaxLength(300);
    }
}
