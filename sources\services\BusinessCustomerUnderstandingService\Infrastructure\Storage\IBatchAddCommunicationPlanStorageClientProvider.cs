using Azure.Storage.Blobs;
using Microsoft.Extensions.Azure;
using Shared.AzureBlob;
using Shared.ObjectStorage;

namespace BusinessCustomerUnderstandingService.Infrastructure.Storage;

/// <summary>
/// ファイルを利用するためのクライアントを提供するインターフェースです。
/// </summary>
public interface IBatchAddCommunicationPlanStorageClientProvider : IObjectStorageClientProvider
{
}

/// <summary>
/// コミュニケーションプラン一括追加用ストレージクライアントプロバイダーの実装です。
/// </summary>
public class BatchAddCommunicationPlanStorageClientProvider : BlobStorageClientProvider, IBatchAddCommunicationPlanStorageClientProvider
{
    /// <summary>
    /// 使用するAzure Blobのクライアント名です。
    /// </summary>
    public const string AzureClientName = "BatchAddCommunicationPlanBlob";

    /// <summary>
    /// ファイルを利用するためのクライアントを初期化します。
    /// </summary>
    /// <param name="clientFactory">Blobクライアントファクトリー</param>
    public BatchAddCommunicationPlanStorageClientProvider(IAzureClientFactory<BlobServiceClient> clientFactory)
        : base(clientFactory, AzureClientName, "communication-plan-batch")
    {
    }
}
