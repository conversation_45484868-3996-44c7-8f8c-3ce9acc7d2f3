using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;
using Shared.Domain;

namespace CustomerProposalService.UseCases.CaseDiscussionReply.DeleteCaseDiscussionReply;

public class DeleteCaseDiscussionReplyHandler : IRequestHandler<DeleteCaseDiscussionReplyCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICaseDiscussionReplyUtility _utility;

    public DeleteCaseDiscussionReplyHandler(
        IUnitOfWork unitOfWork,
        ICaseDiscussionReplyUtility utility)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _utility = utility ?? throw new ArgumentNullException(nameof(utility));
    }

    public async Task<Result<string>> Handle(DeleteCaseDiscussionReplyCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CaseDiscussionReply, string>();
        // 既存データを取得します。
        var getResult = await repository
            .SingleAsync(new FindByIdSpecification<Domain.Entities.CaseDiscussionReply, string>(request.Id)
            .Include(x => x.Files)
            .AsNoTracking()).ConfigureAwait(false);
        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();
        // バージョン番号は、同時実行制御を有効にするために引数で受け取ったデータの値で必ず上書きします。
        currentData.Version = request.Version;

        // Blobに保持する返信本文コンテンツをすべて削除
        var deleteContentsResult = await _utility.DeleteAllContentsAsync(currentData.Id);
        if (deleteContentsResult.IsError) return Result.Error<string>(deleteContentsResult.GetError());

        // DBおよびBlobからファイルを全て削除
        var deleteResult = await _utility.DeleteAllFiles(currentData);
        return deleteResult.IsError
            ? deleteResult
            : await repository
            .DeleteAsync(currentData) // データを削除として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
