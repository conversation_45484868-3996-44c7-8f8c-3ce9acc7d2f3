using BusinessCustomerUnderstandingService.Configurations;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using Microsoft.Extensions.Logging;
using Shared.ServiceModule;

namespace BusinessCustomerUnderstandingService.Infrastructure.Setup;

public class ServiceInitializer : IInitializer
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ServiceSettings _settings;
    private readonly ILoggerFactory _loggerFactory;

    public ServiceInitializer(ApplicationDbContext dbContext, ServiceSettings settings, ILoggerFactory loggerFactory)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        _settings = settings ?? throw new ArgumentNullException(nameof(settings));
        _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
    }

    public Task InitializeAsync()
        => new ApplicationDbContextSetup(_dbContext, _settings.Database, _loggerFactory).SetupAsync();
}
