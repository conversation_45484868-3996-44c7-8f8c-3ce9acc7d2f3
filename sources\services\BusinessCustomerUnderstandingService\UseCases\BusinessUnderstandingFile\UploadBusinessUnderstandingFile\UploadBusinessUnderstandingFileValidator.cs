using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingFile.UploadBusinessUnderstandingFile;
public class UploadBusinessUnderstandingFileValidator : AbstractValidator<UploadBusinessUnderstandingFileCommand>
{
    public UploadBusinessUnderstandingFileValidator()
    {
        RuleFor(v => v.BusinessUnderstandingId).NotEmpty();
        RuleFor(v => v.UpdaterId).NotEmpty();
        RuleFor(v => v.UpdaterName).NotEmpty();
    }
}
