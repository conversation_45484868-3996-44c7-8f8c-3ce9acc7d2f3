using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistribution.GetCommercialDistributionWithHistory;

public class GetCommercialDistributionWithHistorySpecification : BaseSpecification<Domain.Entities.CommercialDistribution>
{
    public GetCommercialDistributionWithHistorySpecification(string Id)
    {
        Query
            .Where(x => x.Id == Id)
            .Include(x => x.Histories, x => x.ThenInclude(x=>x.CommercialDistributionNodeHistories))
            .Include(x => x.Histories, x => x.ThenInclude(x => x.CommercialDistributionEdgeHistories))
            .Include(x => x.Nodes)
            .Include(x => x.Edges)
            .AsNoTracking();
    }
}
