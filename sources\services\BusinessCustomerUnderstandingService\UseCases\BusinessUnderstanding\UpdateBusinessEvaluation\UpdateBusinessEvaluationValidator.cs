using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.UpdateBusinessEvaluation;

public class UpdateBusinessEvaluationValidator : AbstractValidator<UpdateBusinessEvaluationCommand>
{
    public UpdateBusinessEvaluationValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.Version).NotEmpty();
        RuleFor(v => v.ManagementPlan).ChildRules(item =>
        {
            item.RuleFor(v => v.Id).NotEmpty();
            item.RuleFor(v => v.ManagementPlanOverview).MaximumLength(1000);
            item.RuleFor(v => v.ManagementPlanCurrentComment).MaximumLength(1000);
            item.RuleFor(v => v.StatusOfAchievementOfManagementPlan).MaximumLength(1000);
            item.RuleFor(v => v.Ideal).MaximumLength(1000);
            item.RuleFor(v => v.Issue).MaximumLength(1000);
            item.RuleFor(v => v.UpdaterId).NotEmpty();
            item.RuleFor(v => v.UpdaterName).NotEmpty();
            item.RuleFor(v => v.Version).NotEmpty();
        });
        RuleFor(v => v.Management).ChildRules(item =>
        {
            item.RuleFor(v => v.Id).NotEmpty();
            item.RuleFor(v => v.Ideal).MaximumLength(1000);
            item.RuleFor(v => v.Issue).MaximumLength(1000);
            item.RuleFor(v => v.MediumToLongTermVisionComment).MaximumLength(1000);
            item.RuleFor(v => v.ExperienceComment).MaximumLength(1000);
            item.RuleFor(v => v.ExpertiseComment).MaximumLength(1000);
            item.RuleFor(v => v.CentripetalForceComment).MaximumLength(1000);
            item.RuleFor(v => v.SuccessorComment).MaximumLength(1000);
            item.RuleFor(v => v.UpdaterId).NotEmpty();
            item.RuleFor(v => v.UpdaterName).NotEmpty();
            item.RuleFor(v => v.Version).NotEmpty();
        });
        RuleFor(v => v.FiveForceFramework).ChildRules(item =>
        {
            item.RuleFor(v => v.Id).NotEmpty();
            item.RuleFor(v => v.Ideal).MaximumLength(1000);
            item.RuleFor(v => v.Issue).MaximumLength(1000);
            item.RuleFor(v => v.NewComerComment).MaximumLength(1000);
            item.RuleFor(v => v.AbilityToNegotiateWithSalesPartnersComment).MaximumLength(1000);
            item.RuleFor(v => v.AbilityToNegotiateWithSuppliersComment).MaximumLength(1000);
            item.RuleFor(v => v.CompetitorComment).MaximumLength(1000);
            item.RuleFor(v => v.SubstituteArticleComment).MaximumLength(1000);
            item.RuleFor(v => v.UpdaterId).NotEmpty();
            item.RuleFor(v => v.UpdaterName).NotEmpty();
            item.RuleFor(v => v.Version).NotEmpty();
        });
        RuleFor(v => v.FiveStepFrameWork).ChildRules(item =>
        {
            item.RuleFor(v => v.Id).NotEmpty();
            item.RuleFor(v => v.Ideal).MaximumLength(1000);
            item.RuleFor(v => v.Issue).MaximumLength(1000);
            item.RuleFor(v => v.CostReductionComment).MaximumLength(1000);
            item.RuleFor(v => v.ManagementComment).MaximumLength(1000);
            item.RuleFor(v => v.ICTAndBPRComment).MaximumLength(1000);
            item.RuleFor(v => v.HumanResourcesAndEvaluationAndDevelopmentComment).MaximumLength(1000);
            item.RuleFor(v => v.MarketingThinkingComment).MaximumLength(1000);
            item.RuleFor(v => v.UpdaterId).NotEmpty();
            item.RuleFor(v => v.UpdaterName).NotEmpty();
            item.RuleFor(v => v.Version).NotEmpty();
        });
        RuleFor(v => v.ESGAndSDGs).ChildRules(item =>
        {
            item.RuleFor(v => v.Id).NotEmpty();
            item.RuleFor(v => v.Ideal).MaximumLength(1000);
            item.RuleFor(v => v.Issue).MaximumLength(1000);
            item.RuleFor(v => v.SDGsComment).MaximumLength(1000);
            item.RuleFor(v => v.EnvironmentAndCarbonNeutralComment).MaximumLength(1000);
            item.RuleFor(v => v.SubjectsOfCertificationAndDeclarationComment).MaximumLength(50);
            item.RuleFor(v => v.UpdaterId).NotEmpty();
            item.RuleFor(v => v.UpdaterName).NotEmpty();
            item.RuleFor(v => v.Version).NotEmpty();
        });
        RuleFor(v => v.ExternalEnvironment).ChildRules(item =>
        {
            item.RuleFor(v => v.Id).NotEmpty();
            item.RuleFor(v => v.UpdaterId).NotEmpty();
            item.RuleFor(v => v.UpdaterName).NotEmpty();
            item.RuleFor(v => v.Version).NotEmpty();
        });
    }
}
