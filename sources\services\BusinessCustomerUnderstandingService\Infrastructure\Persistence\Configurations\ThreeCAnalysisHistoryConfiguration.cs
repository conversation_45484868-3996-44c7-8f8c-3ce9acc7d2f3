using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class ThreeCAnalysisHistoryConfiguration : IEntityTypeConfiguration<ThreeCAnalysisHistory>
{
    public void Configure(EntityTypeBuilder<ThreeCAnalysisHistory> builder)
    {
        builder.HasOne<ThreeCAnalysis>()
            .WithMany(l => l.Histories)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .HasMaxLength(26);

        builder.Property(u => u.Customer)
            .HasMaxLength(1000);

        builder.Property(u => u.Company)
            .HasMaxLength(1000);

        builder.Property(u => u.Competitor)
            .HasMaxLength(1000);
    }
}
