using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Services.Queries;
using BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.FindCommunicationPlanStaff;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;

internal class FindCommunicationPlanStaffQueryService : IFindCommunicationPlanStaffQueryService
{
    private readonly ApplicationDbContext _dbContext;

    public FindCommunicationPlanStaffQueryService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    async Task<Result<IEnumerable<FindCommunicationPlanStaffResult>>> IFindCommunicationPlanStaffQueryService.Handle()
    {
        var list = new List<FindCommunicationPlanStaffResult>();

        // 当社の方針
        var ourPolicyList = _dbContext.OurPolicyUnderstandings
             .Where(x => x.IsDeleted == false)
             .Where(x => x.Status != Domain.Enums.Status.Completed)
             .GroupBy(x => new { x.StaffId, x.StaffName })
             .Select(y => new { y.Key.StaffId, y.Key.StaffName }).ToList();

        foreach (var ourPolicy in ourPolicyList)
        {
            var item = new FindCommunicationPlanStaffResult(ourPolicy.StaffId, ourPolicy.StaffName);
            list.Add(item);
        }

        // お客さまの考え方
        var customerIdeasList = _dbContext.CustomerIdeasUnderstandings
             .Where(x => x.IsDeleted == false)
             .Where(x => x.Status != Domain.Enums.Status.Completed)
             .GroupBy(x => new { x.StaffId, x.StaffName })
             .Select(y => new { y.Key.StaffId, y.Key.StaffName }).ToList();

        foreach (var customerIdeas in customerIdeasList)
        {
            var item = new FindCommunicationPlanStaffResult(customerIdeas.StaffId, customerIdeas.StaffName);
            list.Add(item);
        }

        // 財務の共有
        var sharingOfFinancesList = _dbContext.SharingOfFinances
             .Where(x => x.IsDeleted == false)
             .Where(x => x.Status != Domain.Enums.Status.Completed)
             .GroupBy(x => new { x.StaffId, x.StaffName })
             .Select(y => new { y.Key.StaffId, y.Key.StaffName }).ToList();

        foreach (var sharingOfFinances in sharingOfFinancesList)
        {
            var item = new FindCommunicationPlanStaffResult(sharingOfFinances.StaffId, sharingOfFinances.StaffName);
            list.Add(item);
        }

        // 課題の仮説協議
        var hypotheticalDiscussionList = _dbContext.HypotheticalDiscussionOfIssues
             .Where(x => x.IsDeleted == false)
             .Where(x => x.Status != Domain.Enums.Status.Completed)
             .GroupBy(x => new { x.StaffId, x.StaffName })
             .Select(y => new { y.Key.StaffId, y.Key.StaffName }).ToList();

        foreach (var hypotheticalDiscussion in hypotheticalDiscussionList)
        {
            var item = new FindCommunicationPlanStaffResult(hypotheticalDiscussion.StaffId, hypotheticalDiscussion.StaffName);
            list.Add(item);
        }

        // ToDo
        var toDoList = _dbContext.ToDo
             .Where(x => x.IsDeleted == false)
             .Where(x => x.Status != Domain.Enums.Status.Completed)
             .GroupBy(x => new { x.StaffId, x.StaffName })
             .Select(y => new { y.Key.StaffId, y.Key.StaffName }).ToList();

        foreach (var toDo in toDoList)
        {
            var item = new FindCommunicationPlanStaffResult(toDo.StaffId, toDo.StaffName);
            list.Add(item);
        }

        return await Task.FromResult(Result.Ok(list.Distinct()));
    }
}
