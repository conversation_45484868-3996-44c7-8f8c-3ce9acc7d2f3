using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Domain.Entities;
using MediatR;
using Nut.Results;
using Shared.Collections;
using Shared.Domain;
using Shared.Results.Errors;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistributionTemplate.UpdateCommercialDistributionTemplate;

public class UpdateCommercialDistributionTemplateHandler : IRequestHandler<UpdateCommercialDistributionTemplateCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;

    public UpdateCommercialDistributionTemplateHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<string>> Handle(UpdateCommercialDistributionTemplateCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CommercialDistributionTemplate, string>();
        // 既存データを取得します。
        var getResult = await repository.SingleAsync(
            new FindByIdSpecification<Domain.Entities.CommercialDistributionTemplate, string>(request.Id)
            .Include(c => c.Nodes)
            .Include(c => c.Edges));

        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();
        currentData.TemplateName = request.TemplateName;
        currentData.CanvasColor = request.CanvasColor;
        // バージョン番号は、同時実行制御を有効にするために引数で受け取ったデータの値で必ず上書きします。
        currentData.Version = request.Version;

        // Nodeの更新
        var updateCommercialDistributionTemplateNodeResult =
            await UpdateCommercialDistributionTemplateNodesAsync(request.Nodes, currentData.Nodes);
        if (updateCommercialDistributionTemplateNodeResult.IsError) return updateCommercialDistributionTemplateNodeResult.PreserveErrorAs<string>();

        // Edgeの更新
        var updateCommercialDistributionTemplateEdgeResult =
            await UpdateCommercialDistributionTemplateEdgesAsync(request.Edges, currentData.Edges);
        if (updateCommercialDistributionTemplateEdgeResult.IsError) return updateCommercialDistributionTemplateEdgeResult.PreserveErrorAs<string>();


        return await repository
            .UpdateAsync(currentData) // データを更新として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }

    private async Task<Result> UpdateCommercialDistributionTemplateNodesAsync(
    List<UpdateCommercialDistributionTemplateNodesCommand> requestItems,
    List<CommercialDistributionTemplateNode> currentItems)
    {
        // Idが設定されていて、Updatedがtrueのものは更新されている
        foreach (var requestDetail in requestItems.Where(r => r.Id is not null && r.Updated))
        {
            var item = currentItems.Where(i => i.Id == requestDetail.Id).FirstOrDefault();

            // 存在しない場合は、すでに更新されているとみなして同時更新エラーにする
            if (item is null) return Result.Error(new ChangeConflictException());

            item.NodeType = requestDetail.NodeType;
            item.Left = requestDetail.Left;
            item.Top = requestDetail.Top;
            item.Width = requestDetail.Width;
            item.Height = requestDetail.Height;
            item.Title = requestDetail.Title;
            item.Text = requestDetail.Text;
            item.GroupingId = requestDetail.GroupingId;
            item.Color= requestDetail.Color;
            item.CustomerName= requestDetail.CustomerName;
            item.PersonType = requestDetail.PersonType;
            item.BranchNumber = requestDetail.BranchNumber;
            item.CifNumber = requestDetail.CifNumber;
            item.Merchandise = requestDetail.Merchandise;
            item.Material = requestDetail.Material;
            item.Amount = requestDetail.Amount;
            item.Industry = requestDetail.Industry;
            item.Area = requestDetail.Area;
            item.IsInternationalBusinessPartners = requestDetail.IsInternationalBusinessPartners;
            item.CountryCode = requestDetail.CountryCode;
            item.CityName = requestDetail.CityName;
            item.Note = requestDetail.Note;
        }

        // リクエストで渡ってきていないIDは削除されたとみなす
        var deleteItems = currentItems.Except(requestItems, ci => ci.Id, ri => ri.Id).ToList();
        if (deleteItems.Any())
        {
            var repo = _unitOfWork.GetRepository<CommercialDistributionTemplateNode>();
            foreach (var deleteItem in deleteItems)
            {
                // リポジトリを利用して削除とマーク
                await repo.DeleteAsync(deleteItem);
                currentItems.Remove(deleteItem);
            }
        }

        // Updatedがfalseのものは追加されている
        var newItems = requestItems
            .Where(r => !r.Updated)
            .Select(r => new CommercialDistributionTemplateNode()
            {
                Id = r.Id,
                NodeType = r.NodeType,
                Left = r.Left,
                Top = r.Top,
                Width = r.Width,
                Height = r.Height,
                Title= r.Title,
                Text = r.Text,
                GroupingId = r.GroupingId,
                Color= r.Color,
            });
        currentItems.AddRange(newItems);

        return Result.Ok();
    }

    private async Task<Result> UpdateCommercialDistributionTemplateEdgesAsync(
   List<UpdateCommercialDistributionTemplateEdgesCommand> requestItems,
   List<CommercialDistributionTemplateEdge> currentItems)
    {
        // Idが設定されていて、Updatedがtrueのものは更新されている
        foreach (var requestDetail in requestItems.Where(r => r.Id is not null && r.Updated))
        {
            var item = currentItems.Where(i => i.Id == requestDetail.Id).FirstOrDefault();

            // 存在しない場合は、すでに更新されているとみなして同時更新エラーにする
            if (item is null) return Result.Error(new ChangeConflictException());

            item.SourceNode = requestDetail.SourceNode;
            item.Source = requestDetail.Source;
            item.TargetNode = requestDetail.TargetNode;
            item.Target = requestDetail.Target;
        }

        // リクエストで渡ってきていないIDは削除されたとみなす
        var deleteItems = currentItems.Except(requestItems, ci => ci.Id, ri => ri.Id).ToList();
        if (deleteItems.Any())
        {
            var repo = _unitOfWork.GetRepository<CommercialDistributionTemplateEdge>();
            foreach (var deleteItem in deleteItems)
            {
                // リポジトリを利用して削除とマーク
                await repo.DeleteAsync(deleteItem);
                currentItems.Remove(deleteItem);
            }
        }

        // Idが設定されておらず、Updatedがfalseのものは追加されている
        var newItems = requestItems
            .Where(r => r.Id is null && !r.Updated)
            .Select(r => new Domain.Entities.CommercialDistributionTemplateEdge()
            {
                Id = Ulid.NewUlid().ToString(),
                SourceNode = r.SourceNode,
                Source = r.Source,
                TargetNode = r.TargetNode,
                Target = r.Target,
            });
        currentItems.AddRange(newItems);

        return Result.Ok();
    }
}
