using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Services.Queries;
using MediatR;
using Nut.Results;
using Shared.Messaging;
using SharedKernel.ExternalApi.MessageContract.CustomerIdentifying;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartner.GetAllBusinessPartners;

public class GetAllBusinessPartnersHandler : IRequestHandler<GetAllBusinessPartnersQuery, Result<IEnumerable<GetAllBusinessPartnersResult>>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IGetBPAndCAByCustomerIdentificationIdQueryService _getBPAndCPQueryService;
    private readonly IMessageSender<FindCustomerByIdsQuery, IEnumerable<FindCustomerByIdsResult>> _findCustomerByIdsSender;

    public GetAllBusinessPartnersHandler(
        IUnitOfWork unitOfWork,
        IGetBPAndCAByCustomerIdentificationIdQueryService queryService,
        IMessageSender<FindCustomerByIdsQuery, IEnumerable<FindCustomerByIdsResult>> findCustomerByIdsSender
        )
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _getBPAndCPQueryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
        _findCustomerByIdsSender = findCustomerByIdsSender ?? throw new ArgumentNullException(nameof(findCustomerByIdsSender));
    }

    public async Task<Result<IEnumerable<GetAllBusinessPartnersResult>>> Handle(GetAllBusinessPartnersQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 事業性理解に紐づくビジネスパートナーを全件取得
        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessPartner>();
        var spec = new GetAllBusinessPartnersSpecification(request.businessUnderstandingId);

        var getBusinessPartnersResult = await repository.FindAsync(spec).ConfigureAwait(false);
        if (getBusinessPartnersResult.IsError) return getBusinessPartnersResult.PreserveErrorAs<IEnumerable<GetAllBusinessPartnersResult>>();
        var businessPartners = getBusinessPartnersResult.Get();

        // ビジネスパートナー顧客のうち、顧客識別IDがあるものについては提携・粉飾情報を取得
        var additionalInfos = new Dictionary<Guid, AdditionalInformationOfBusinessPartner>();
        var customerIdentificationIds = businessPartners.Where(x => x.BusinessPartnerCustomerIdentificationId is not null)
                                                        .Select(x => (Guid)x.BusinessPartnerCustomerIdentificationId!)
                                                        .ToHashSet();
        if (customerIdentificationIds.Any())
        {
            // 提携・粉飾情報を取得
            var getBPAndCAResult = await _getBPAndCPQueryService.Handle(customerIdentificationIds).ConfigureAwait(false);
            if (getBPAndCAResult.IsError) return getBPAndCAResult.PreserveErrorAs<IEnumerable<GetAllBusinessPartnersResult>>();
            var businessPartnershipAndCreativeAccounting = getBPAndCAResult.Get();

            // 顧客名を取得
            var getCustomersResult = await _findCustomerByIdsSender.SendAsync(new() { Ids = customerIdentificationIds });
            if (getCustomersResult.IsError) return getCustomersResult.PreserveErrorAs<IEnumerable<GetAllBusinessPartnersResult>>();
            var customers = getCustomersResult.Get();

            // 付加情報に格納
            additionalInfos = customerIdentificationIds.Select(x =>
                {
                    var customerName = customers.FirstOrDefault(y => y.Id == x)?.Name;
                    return new AdditionalInformationOfBusinessPartner(
                        CustomerIdentificationId: x,
                        Name: string.IsNullOrEmpty(customerName) ? null : customerName,
                        HasBusinessPartnershipWithOurCompany: businessPartnershipAndCreativeAccounting.FirstOrDefault(y => y.CustomerIdentificationId == x)?.HasBusinessPartnershipWithOurCompany,
                        HasCreativeAccountingIncident: businessPartnershipAndCreativeAccounting.FirstOrDefault(y => y.CustomerIdentificationId == x)?.HasCreativeAccountingIncident
                    );
                }
            ).ToDictionary(x => x.CustomerIdentificationId);
        }

        // 結合
        var combinedBusinessPartners = businessPartners.Select(
                bp =>
                {
                    AdditionalInformationOfBusinessPartner? additionalInfo = null;
                    if (bp.BusinessPartnerCustomerIdentificationId is not null)
                    {
                        additionalInfo = additionalInfos.GetValueOrDefault(bp.BusinessPartnerCustomerIdentificationId.Value);
                    }
                    return new GetAllBusinessPartnersResult(
                        Id: bp.Id,
                        BusinessUnderstandingId: bp.BusinessUnderstandingId,
                        BusinessPartnerCustomerIdentificationId: bp.BusinessPartnerCustomerIdentificationId,
                        BusinessPartnerCustomerName: string.IsNullOrEmpty(bp.BusinessPartnerCustomerName) ? additionalInfo?.Name : bp.BusinessPartnerCustomerName,
                        BusinessPartnerIndustry:
                            new(
                                Id: bp.BusinessPartnerIndustry.Id,
                                BusinessPartnerId: bp.BusinessPartnerIndustry.BusinessPartnerId,
                                SubIndustryCode: bp.BusinessPartnerIndustry.SubIndustryCode,
                                DetailIndustryCode: bp.BusinessPartnerIndustry.DetailIndustryCode,
                                IndustryCode: bp.BusinessPartnerIndustry.IndustryCode
                            ),
                        HasBusinessPartnershipWithOurCompany: additionalInfo?.HasBusinessPartnershipWithOurCompany,
                        HasCreativeAccountingIncident: additionalInfo?.HasCreativeAccountingIncident,
                        Note: bp.Note,
                        Version: bp.Version
                    );
                }
            );

        return Result.Ok(combinedBusinessPartners);
    }

    // 顧客識別IDが指定されているビジネスパートナーに付加すべき情報
    private record AdditionalInformationOfBusinessPartner(
        Guid CustomerIdentificationId,
        string? Name,
        bool? HasBusinessPartnershipWithOurCompany,
        bool? HasCreativeAccountingIncident
    );
}
