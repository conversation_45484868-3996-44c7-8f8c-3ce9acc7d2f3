using BusinessCustomerUnderstandingService.Domain.Entities;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistribution.GetCommercialDistribution;

public record GetCommercialDistributionResult(
    string Id,
    string CanvasColor,
    string? UpdaterId,
    string? UpdaterName,
    DateTimeOffset? UpdatedDateTime,
    List<CommercialDistributionNode> Nodes,
    List<CommercialDistributionEdge> Edges,
    string BusinessUnderstandingId,
    string Version
);
