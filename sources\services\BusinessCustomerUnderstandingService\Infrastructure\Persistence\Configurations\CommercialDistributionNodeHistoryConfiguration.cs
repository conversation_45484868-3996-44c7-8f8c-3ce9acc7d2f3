using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class CommercialDistributionNodeHistoryConfiguration : IEntityTypeConfiguration<CommercialDistributionNodeHistory>
{
    public void Configure(EntityTypeBuilder<CommercialDistributionNodeHistory> builder)
    {
        builder.HasOne<CommercialDistributionHistory>()
            .WithMany(c => c.CommercialDistributionNodeHistories)
            .HasForeignKey(n => n.CommercialDistributionHistoryId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(n => n.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(n => n.CommercialDistributionNodeId)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(n => n.Title)
            .HasMaxLength(64);

        builder.Property(n => n.GroupingId)
            .HasMaxLength(26);

        builder.Property(n => n.Color)
            .HasMaxLength(9);

        builder.Property(n => n.CustomerName)
            .HasMaxLength(100);

        builder.Property(n => n.BranchNumber)
            .HasMaxLength(3);

        builder.Property(n => n.CifNumber)
            .HasMaxLength(8);

        builder.Property(n => n.Merchandise)
            .HasMaxLength(500);

        builder.Property(n => n.Material)
            .HasMaxLength(500);

        builder.Property(n => n.Industry)
            .HasMaxLength(24);

        builder.Property(n => n.Area)
            .HasMaxLength(50);

        builder.Property(n => n.CountryCode)
            .HasMaxLength(3);

        builder.Property(n => n.CityName)
            .HasMaxLength(100);
    }
}
