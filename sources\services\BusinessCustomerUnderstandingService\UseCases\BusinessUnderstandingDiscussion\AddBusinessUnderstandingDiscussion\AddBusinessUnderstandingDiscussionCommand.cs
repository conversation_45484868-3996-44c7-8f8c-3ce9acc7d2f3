using BusinessCustomerUnderstandingService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussion.AddBusinessUnderstandingDiscussion;

[WithDefaultBehaviors]
public record AddBusinessUnderstandingDiscussionCommand(
    string Registrant,
    string Description,
    string ThreadId,
    string ThreadTitle,
    string CustomerName,
    IEnumerable<string>? MentionTargetUserIds,
    IEnumerable<string>? MentionTargetTeamIds,
    IEnumerable<string>? MentionTargetTeamMemberUserIds,
    DiscussionPurpose Purpose,
    string? Person,
    bool? IsPersonOfPower,
    bool? IsCorporateDepositTheme,
    bool? IsFundSettlementTheme,
    IEnumerable<IFormFile> UploadFiles
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
