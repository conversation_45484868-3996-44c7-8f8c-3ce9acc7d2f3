using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Services.Queries;
using Microsoft.EntityFrameworkCore;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;

internal class GetBPAndCAByCustomerIdentificationIdQueryService : IGetBPAndCAByCustomerIdentificationIdQueryService
{
    private readonly ApplicationDbContext _dbContext;

    public GetBPAndCAByCustomerIdentificationIdQueryService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    public async Task<Result<List<BusinessPartnershipAndCreativeAccounting>>> Handle(IEnumerable<Guid> customerIdentificationIds)
    {
        var businessCustomerAndBusinessUnderstandings =
            await _dbContext.BusinessCustomers.AsNoTracking()
                .Include(x => x.BusinessUnderstanding)
                // ThenIncludeでnull参照となる可能性があるとwarningが表示されるが、null参照自体は想定通りのため無視する
                .ThenInclude(x => x!.BusinessPartnershipAndCreativeAccountingDetail)
                .Where(x => customerIdentificationIds.Contains(x.CustomerIdentificationId))
                .ToListAsync();

        return Result.Ok(
            businessCustomerAndBusinessUnderstandings
                .Select(x =>
                    new BusinessPartnershipAndCreativeAccounting(
                        x.CustomerIdentificationId,
                        x.BusinessUnderstanding?.BusinessPartnershipAndCreativeAccountingDetail?.HasBusinessPartnershipWithOurCompany,
                        x.BusinessUnderstanding?.BusinessPartnershipAndCreativeAccountingDetail?.HasCreativeAccountingIncident
                    )
                ).ToList()
        );
    }
}
