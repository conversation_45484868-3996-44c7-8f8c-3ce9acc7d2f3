using Shared.Spec;

namespace CustomerProposalService.UseCases.Case.GetForeignExchangeTransactionCase;

public class GetForeignExchangeTransactionCaseSpecification : BaseSpecification<Domain.Entities.ForeignExchangeTransactionCase>
{
    public GetForeignExchangeTransactionCaseSpecification(GetForeignExchangeTransactionCaseQuery request)
    {
        Query
            .Where(x => x.Id == request.Id)
            .Include(x => x.CaseUpdateInformation)
            .Include(x => x.CaseFiles)
            .Include(x => x.CaseLinks)
            .Include(x => x.Guarantors)
            .AsNoTracking();
    }
}
