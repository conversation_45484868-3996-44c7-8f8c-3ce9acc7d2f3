using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.CustomerIdentification.GetCommercialDistributionByCustomerIdentificationId;

public class GetCommercialDistributionByCustomerIdentificationIdHandler : IRequestHandler<GetCommercialDistributionByCustomerIdentificationIdQuery, Result<Domain.Entities.CommercialDistribution>>
{
    private readonly IGetCommercialDistributionByCustomerIdentificationIdQueryService _queryService;


    public GetCommercialDistributionByCustomerIdentificationIdHandler(IGetCommercialDistributionByCustomerIdentificationIdQueryService queryService)
    {
        _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
    }

    public Task<Result<Domain.Entities.CommercialDistribution>> Handle(GetCommercialDistributionByCustomerIdentificationIdQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 検索を行います。
        return _queryService.Handle(request);
    }
}
