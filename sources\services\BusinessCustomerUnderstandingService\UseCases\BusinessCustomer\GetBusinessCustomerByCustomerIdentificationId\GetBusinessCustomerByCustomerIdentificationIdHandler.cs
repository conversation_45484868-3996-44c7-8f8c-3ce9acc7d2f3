using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessCustomer.GetBusinessCustomerByCustomerIdentificationId;

public class GetBusinessCustomerByCustomerIdentificationIdHandler : IRequestHandler<GetBusinessCustomerByCustomerIdentificationIdQuery, Result<GetBusinessCustomerByCustomerIdentificationIdResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetBusinessCustomerByCustomerIdentificationIdHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetBusinessCustomerByCustomerIdentificationIdResult>> Handle(GetBusinessCustomerByCustomerIdentificationIdQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessCustomer, string>();
        return repository.SingleAsync(new GetBusinessCustomerByCustomerIdentificationIdSpecification(request.CustomerIdentificationId))
            .Map(v => new GetBusinessCustomerByCustomerIdentificationIdResult(
                v.Id,
                v.CustomerIdentificationId,
                v.Version));
    }
}
