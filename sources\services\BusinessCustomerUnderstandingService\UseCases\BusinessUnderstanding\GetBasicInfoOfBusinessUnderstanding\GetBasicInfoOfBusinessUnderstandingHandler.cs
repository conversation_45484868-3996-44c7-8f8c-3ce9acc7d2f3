using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBasicInfoOfBusinessUnderstanding;

public class GetBasicInfoOfBusinessUnderstandingHandler : IRequestHandler<GetBasicInfoOfBusinessUnderstandingQuery, Result<GetBasicInfoOfBusinessUnderstandingResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetBasicInfoOfBusinessUnderstandingHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetBasicInfoOfBusinessUnderstandingResult>> Handle(GetBasicInfoOfBusinessUnderstandingQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding, string>();
        return repository.SingleAsync(new GetBasicInfoOfBusinessUnderstandingSpecification(request.Id))
            .Map(v => new GetBasicInfoOfBusinessUnderstandingResult(
                v.Id,
                v.BusinessCustomerId,
                v.BusinessCustomer.CustomerIdentificationId));
    }
}
