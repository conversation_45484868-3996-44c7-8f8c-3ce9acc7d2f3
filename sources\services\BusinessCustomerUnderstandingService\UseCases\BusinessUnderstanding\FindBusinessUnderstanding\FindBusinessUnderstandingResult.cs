using BusinessCustomerUnderstandingService.Domain.Enums;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindBusinessUnderstanding;

public record FindBusinessUnderstandingResult(
    string BusinessUnderstandingId,
    Guid CustomerIdentificationId,
    int CommunicationPlanCount,
    string? TransactionPolicy,
    BusinessUnderstandingApproachType? BusinessUnderstandingApproachType,
    string? CustomerStaffId,
    string? TransactionPolicyConfirmerId,
    DateTimeOffset? TransactionPolicyConfirmedDateTime,
    DateTimeOffset? LastUpdatedDateTime, // UpdatedDateTime
    string? UpdaterId,
    string? UpdaterName
);
