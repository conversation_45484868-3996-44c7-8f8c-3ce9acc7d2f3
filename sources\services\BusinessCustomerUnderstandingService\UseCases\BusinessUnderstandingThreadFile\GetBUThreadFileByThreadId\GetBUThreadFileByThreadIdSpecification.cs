using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThreadFile.GetBUThreadFileByThreadId;

public class GetBUThreadFileByThreadIdSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingThreadFile>
{
    public GetBUThreadFileByThreadIdSpecification(string threadId)
    {
        Query
            .Where(x => x.ThreadId == threadId)
            .AsNoTracking();
    }
}
