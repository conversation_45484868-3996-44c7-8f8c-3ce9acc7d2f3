using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.BatchUpdateCommunicationPlanStaff;

public class FindOurPolicyUnderstandingSpecification : BaseSpecification<Domain.Entities.OurPolicyUnderstanding>
{
    public FindOurPolicyUnderstandingSpecification(string currentStaffId, List<string> businessUnderstandingIdList)
    {
        Query
            .Where(x => x.StaffId == currentStaffId)
            .Where(x => businessUnderstandingIdList.Contains(x.BusinessUnderstandingId))
            .Where(x => x.Status != Domain.Enums.Status.Completed)
            .AsNoTracking();
    }
}
