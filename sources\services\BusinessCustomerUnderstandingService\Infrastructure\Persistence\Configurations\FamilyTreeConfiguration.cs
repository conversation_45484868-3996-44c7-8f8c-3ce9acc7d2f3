using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class FamilyTreeConfiguration : IEntityTypeConfiguration<FamilyTree>
{
    public void Configure(EntityTypeBuilder<FamilyTree> builder)
    {
        builder.HasOne<BusinessUnderstanding>()
            .WithOne(b => b.FamilyTree)
            .HasForeignKey<FamilyTree>(c => c.BusinessUnderstandingId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(c => c.Id)
            .IsRequired()
            .HasMaxLength(26);
    }
}
