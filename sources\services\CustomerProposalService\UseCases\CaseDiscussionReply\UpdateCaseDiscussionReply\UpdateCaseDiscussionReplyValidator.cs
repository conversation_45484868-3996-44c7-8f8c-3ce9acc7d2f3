using FluentValidation;

namespace CustomerProposalService.UseCases.CaseDiscussionReply.UpdateCaseDiscussionReply;

public class UpdateCaseDiscussionReplyValidator : AbstractValidator<UpdateCaseDiscussionReplyCommand>
{
    public UpdateCaseDiscussionReplyValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.CaseId).NotEmpty();
        RuleFor(v => v.CustomerIdentificationId).NotEmpty();
        RuleFor(v => v.CustomerName).NotEmpty();
        RuleFor(v => v.CustomerStaffId).NotEmpty();
        RuleFor(v => v.RegistrantId).NotEmpty();
        RuleFor(v => v.Purpose).IsInEnum().NotEqual(Domain.Enums.CaseDiscussionPurpose.Undefined);
        RuleFor(v => v.Person).MaximumLength(20);
        RuleFor(v => v.Description).NotEmpty();
        RuleFor(v => v.Version).NotEmpty();
    }
}
