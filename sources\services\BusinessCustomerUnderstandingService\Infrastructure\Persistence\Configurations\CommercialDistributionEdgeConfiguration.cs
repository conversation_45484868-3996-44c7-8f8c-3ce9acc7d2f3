using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class CommercialDistributionEdgeConfiguration : IEntityTypeConfiguration<CommercialDistributionEdge>
{
    public void Configure(EntityTypeBuilder<CommercialDistributionEdge> builder)
    {
        builder.HasOne<CommercialDistribution>()
            .WithMany(c => c.Edges)
            .HasForeignKey(e => e.CommercialDistributionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(e => e.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(e => e.SourceNode)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(e => e.Source)
            .IsRequired()
            .HasMaxLength(32);

        builder.Property(e => e.TargetNode)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(e => e.Target)
            .IsRequired()
            .HasMaxLength(32);
    }
}
