using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.AddCommunicationPlanFromFile;

public class AddCommunicationPlanFromFileValidator : AbstractValidator<AddCommunicationPlanFromFileCommand>
{
    public AddCommunicationPlanFromFileValidator()
    {
        RuleFor(v => v.FolderName).NotEmpty();
        RuleFor(v => v.UpdaterId).NotEmpty();
        RuleFor(v => v.UpdaterName).NotEmpty();
    }
}
