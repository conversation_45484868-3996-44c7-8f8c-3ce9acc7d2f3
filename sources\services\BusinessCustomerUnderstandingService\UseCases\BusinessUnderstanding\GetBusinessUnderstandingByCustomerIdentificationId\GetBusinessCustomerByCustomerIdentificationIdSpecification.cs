using Microsoft.EntityFrameworkCore;
using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessUnderstandingByCustomerIdentificationId;

public class GetBusinessCustomerByCustomerIdentificationIdSpecification : BaseSpecification<Domain.Entities.BusinessCustomer>
{
    public GetBusinessCustomerByCustomerIdentificationIdSpecification(Guid customerIdentificationId)
    {
        Query
            .Where(x => x.CustomerIdentificationId == customerIdentificationId)
            .Include(x=>x.BusinessUnderstanding)
            .AsNoTracking();
    }
}
