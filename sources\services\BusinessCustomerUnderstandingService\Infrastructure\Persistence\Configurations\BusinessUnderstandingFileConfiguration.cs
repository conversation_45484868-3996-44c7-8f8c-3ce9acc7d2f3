using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class BusinessUnderstandingFileConfiguration : IEntityTypeConfiguration<BusinessUnderstandingFile>
{
    public void Configure(EntityTypeBuilder<BusinessUnderstandingFile> builder)
    {
        builder.HasOne<BusinessUnderstanding>()
            .WithMany(l => l.BusinessUnderstandingFile)
            .HasForeignKey(j => j.BusinessUnderstandingId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);
    }
}
