using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class FiveForceFrameworkHistoryConfiguration : IEntityTypeConfiguration<FiveForceFrameworkHistory>
{
    public void Configure(EntityTypeBuilder<FiveForceFrameworkHistory> builder)
    {
        builder.HasOne<FiveForceFramework>()
            .WithMany(entity => entity.Histories)
            .HasForeignKey(entity => entity.OriginalId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(entity => entity.Id)
            .HasMaxLength(26);

        builder.Property(entity => entity.Ideal)
            .HasMaxLength(1000);

        builder.Property(entity => entity.Issue)
            .HasMaxLength(1000);

        builder.Property(entity => entity.NewComerComment)
            .HasMaxLength(1000);

        builder.Property(entity => entity.AbilityToNegotiateWithSalesPartnersComment)
            .HasMaxLength(1000);

        builder.Property(entity => entity.AbilityToNegotiateWithSuppliersComment)
            .HasMaxLength(1000);

        builder.Property(entity => entity.CompetitorComment)
            .HasMaxLength(1000);

        builder.Property(entity => entity.SubstituteArticleComment)
            .HasMaxLength(1000);

        builder.Property(entity => entity.OriginalId)
            .HasMaxLength(26);
    }
}
