using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionReplyReaction.GetCaseDiscussionReplyReaction;

public class GetCaseDiscussionReplyReactionHandler : IRequestHandler<GetCaseDiscussionReplyReactionQuery, Result<GetCaseDiscussionReplyReactionResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetCaseDiscussionReplyReactionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetCaseDiscussionReplyReactionResult>> Handle(GetCaseDiscussionReplyReactionQuery request, CancellationToken cancellationToken)
    {
        if (request is null) throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CaseDiscussionReplyReaction, string>();
        return repository.GetAsync(request.Id)
            .Map(v => new GetCaseDiscussionReplyReactionResult(
                v.Id,
                v.CaseDiscussionReplyId,
                v.UpdaterId,
                v.UpdaterName,
                v.ReactionType,
                v.UpdatedDateTime,
                v.Version));
    }
}
