using BusinessCustomerUnderstandingService.Externals.CustomerProposal;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Infrastructure.QueryServices;
using BusinessCustomerUnderstandingService.Tests.TestUtil;
using BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindCustomerIdentificationIdsBySearchCondition;
using FluentAssertions;
using Moq;
using Nut.Results;
using Nut.Results.FluentAssertions;
using Shared.Messaging;
using Xunit;

namespace BusinessCustomerUnderstandingService.Tests.UseCases.BusinessUnderstanding.FindBusinessUnderstandingsByCustomerIdentificationIds;

public class FindCustomerIdentificationIdsBySearchConditionTest : IAsyncLifetime
{
    private readonly ApplicationDbContext _dbContext;
    private readonly TestCurrentUserService _currentUserService;
    private readonly TestCurrentDateTimeService _currentDateTimeService;

    public FindCustomerIdentificationIdsBySearchConditionTest()
    {
        _currentUserService = new TestCurrentUserService();
        _currentDateTimeService = new TestCurrentDateTimeService();

        _dbContext = TestDbContextBuilder.Build(_currentUserService, _currentDateTimeService);
    }

    public Task InitializeAsync()
        => TestData.CreateAsync(_dbContext);

    public Task DisposeAsync() => Task.CompletedTask;

    [Fact]
    public void Ctor_引数にnullを渡すと例外が発生する()
    {
        var act = () => new FindCustomerIdentificationIdsBySearchConditionHandler(null!);
        act.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public async Task Handle_Requestがnullの場合は例外が発生する()
    {
        var teamMembersMessageSenderSender = new Mock<IMessageSender<FindTeamMemberByTeamId, IEnumerable<FindTeamMemberByTeamIdResult>>>();
        var findCustomerIdentificationIdsBySearchConditionSenderResult = new List<FindTeamMemberByTeamIdResult>();
        teamMembersMessageSenderSender.Setup(x => x.SendAsync(It.IsAny<FindTeamMemberByTeamId>())).Returns(Task.FromResult(Result.Ok<IEnumerable<FindTeamMemberByTeamIdResult>>(findCustomerIdentificationIdsBySearchConditionSenderResult)));
        var teamMembersMessageSender = teamMembersMessageSenderSender.Object;

        var unitOfWork = new UnitOfWork(_dbContext);
        var handler = new FindCustomerIdentificationIdsBySearchConditionHandler(new FindCustomerIdentificationIdsBySearchConditionQueryService(_dbContext, teamMembersMessageSender));

        var act = () => handler.Handle(null!, CancellationToken.None);
        await act.Should().ThrowAsync<ArgumentNullException>();
    }

    [Fact]
    public async Task Handle_引数で渡された値でデータが取得される()
    {
        var teamMembersMessageSenderSender = new Mock<IMessageSender<FindTeamMemberByTeamId, IEnumerable<FindTeamMemberByTeamIdResult>>>();
        var findCustomerIdentificationIdsBySearchConditionSenderResult = new List<FindTeamMemberByTeamIdResult>();
        teamMembersMessageSenderSender.Setup(x => x.SendAsync(It.IsAny<FindTeamMemberByTeamId>())).Returns(Task.FromResult(Result.Ok<IEnumerable<FindTeamMemberByTeamIdResult>>(findCustomerIdentificationIdsBySearchConditionSenderResult)));
        var teamMembersMessageSender = teamMembersMessageSenderSender.Object;
        // Query のインスタンスを作成します。
        var arg = new FindCustomerIdentificationIdsBySearchConditionQuery
        {
            CustomerIdentificationIds = new List<Guid> { Guid.Parse("00000000-0000-0000-0000-000000000001") },
            CountLmit = 1,
        };
        // Handlerを実行します。
        var actual = await new FindCustomerIdentificationIdsBySearchConditionHandler(new FindCustomerIdentificationIdsBySearchConditionQueryService(_dbContext, teamMembersMessageSender))
            .Handle(arg, CancellationToken.None)
            .ConfigureAwait(false);

        actual.Should().BeOk();
        var value = actual.Get();
        value.IndexOf(Guid.Parse("00000000-0000-0000-0000-000000000001")).Should().BeGreaterThanOrEqualTo(0);
    }
}
