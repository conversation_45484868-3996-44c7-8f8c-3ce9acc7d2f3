using FluentValidation;

namespace CustomerProposalService.UseCases.Case.UpdateNewLoanCase;

public class UpdateNewLoanCaseValidator : AbstractValidator<UpdateNewLoanCaseCommand>
{
    public UpdateNewLoanCaseValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.CaseName).NotEmpty().MaximumLength(50);
        RuleFor(v => v.CaseStatus).NotEmpty().IsInEnum();
        RuleFor(e => e.CaseOutline).CaseOutline();
        RuleFor(v => v.StaffId).NotEmpty().MaximumLength(50);
        RuleFor(v => v.StaffName).NotEmpty().MaximumLength(50);
        RuleFor(e => e.SubjectType).IsInEnum();
        RuleFor(e => e.InterestRateCustom).MaximumLength(50);
        RuleFor(e => e.UseOfFundsType).IsInEnum();
        RuleFor(e => e.UseOfFundsCustom).MaximumLength(50);
        RuleFor(e => e.RepaymentMethodType).IsInEnum();
        RuleFor(e => e.RepaymentMethodCustom).MaximumLength(50);
        RuleFor(e => e.CollateralOrGuaranteeCustom).MaximumLength(50);
        RuleFor(e => e.CancelTypeOfLoan).IsInEnum();
        RuleFor(e => e.CancelReason).CancelReason();
        RuleFor(e => e.TrafficSource).IsInEnum();
        RuleFor(v => v.Version).NotEmpty();
    }
}
