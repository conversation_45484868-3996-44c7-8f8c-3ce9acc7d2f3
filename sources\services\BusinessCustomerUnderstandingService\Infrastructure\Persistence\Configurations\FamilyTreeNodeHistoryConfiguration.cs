using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class FamilyTreeNodeHistoryConfiguration : IEntityTypeConfiguration<FamilyTreeNodeHistory>
{
    public void Configure(EntityTypeBuilder<FamilyTreeNodeHistory> builder)
    {
        builder.HasOne<FamilyTreeHistory>()
            .WithMany(c => c.FamilyTreeNodeHistories)
            .HasForeignKey(n => n.FamilyTreeHistoryId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(n => n.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(n => n.FamilyTreeNodeId)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(n => n.Name)
            .HasMaxLength(100);

        builder.Property(n => n.Relationship)
            .HasMaxLength(16);

        builder.Property(n => n.Note)
            .HasMaxLength(64);
    }
}

