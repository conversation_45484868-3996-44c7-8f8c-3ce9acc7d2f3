using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class CommercialDistributionConfiguration : IEntityTypeConfiguration<CommercialDistribution>
{
    public void Configure(EntityTypeBuilder<CommercialDistribution> builder)
    {
        builder.HasOne<BusinessUnderstanding>()
            .WithOne(b => b.CommercialDistribution)
            .HasForeignKey<CommercialDistribution>(c => c.BusinessUnderstandingId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(c => c.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(c => c.CanvasColor)
            .HasMaxLength(9);
    }
}
