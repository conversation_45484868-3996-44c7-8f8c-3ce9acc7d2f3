using Nut.Results;

namespace BusinessCustomerUnderstandingService.Services.Queries;

public interface IGetBPAndCAByCustomerIdentificationIdQueryService
{
    public Task<Result<List<BusinessPartnershipAndCreativeAccounting>>> Handle(IEnumerable<Guid> customerIdentificationIds);
}

public record BusinessPartnershipAndCreativeAccounting(
    Guid CustomerIdentificationId,
    bool? HasBusinessPartnershipWithOurCompany,
    bool? HasCreativeAccountingIncident
);
