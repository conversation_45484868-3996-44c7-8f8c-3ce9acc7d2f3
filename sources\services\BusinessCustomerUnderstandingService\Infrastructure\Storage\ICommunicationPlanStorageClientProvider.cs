using Azure.Storage.Blobs;
using Microsoft.Extensions.Azure;
using Shared.AzureBlob;
using Shared.ObjectStorage;

namespace BusinessCustomerUnderstandingService.Infrastructure.Storage;

/// <summary>
/// ファイルを利用するためのクライアントを提供するインターフェースです。
/// </summary>
public interface ICommunicationPlanStorageClientProvider : IObjectStorageClientProvider
{
}

/// <summary>
/// コミュニケーションプラン用ストレージクライアントプロバイダーの実装です。
/// </summary>
public class CommunicationPlanStorageClientProvider : BlobStorageClientProvider, ICommunicationPlanStorageClientProvider
{
    /// <summary>
    /// 使用するAzure Blobのクライアント名です。
    /// </summary>
    public const string AzureClientName = "CommunicationPlanBlob";

    /// <summary>
    /// ファイルを利用するためのクライアントを初期化します。
    /// </summary>
    /// <param name="clientFactory">Blobクライアントファクトリー</param>
    public CommunicationPlanStorageClientProvider(IAzureClientFactory<BlobServiceClient> clientFactory)
        : base(clientFactory, AzureClientName, "communication-plan")
    {
    }
}
