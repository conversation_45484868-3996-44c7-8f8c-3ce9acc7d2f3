using CustomerProposalService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.UpdateAcceptanceAndGuaranteeCase;

[WithDefaultBehaviors]
public record UpdateAcceptanceAndGuaranteeCaseCommand(
    // Case Entity
    string Id,
    string CaseName,
    CaseStatus CaseStatus,
    string? CaseOutline,
    DateTimeOffset? ExpiredAt,
    string StaffId,
    string StaffName,
    IEnumerable<IFormFile>? UploadFiles,
    IEnumerable<string>? CaseLinks,
    // AcceptanceAndGuaranteeCase Entity
    decimal? Amount,
    decimal? InterestRate,
    string? InterestRateCustom,
    bool RelatedEsg,
    bool PreConsultationStandard,
    bool IsEarthquakeRelated,
    string? RepaymentType,
    string? CollateralType,
    string? CollateralOrGuaranteeCustom,
    string? GuaranteeType,
    CancelTypeOfLoan? CancelTypeOfLoan,
    string? CancelReason, TrafficSource? TrafficSource,
    IEnumerable<Domain.Entities.Guarantor>? Guarantors,
    string Version
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
