using CustomerProposalService.Domain;
using CustomerProposalService.Domain.Enums;
using Microsoft.AspNetCore.Http;
using Nut.Results;
using BusinessMatching = SharedKernel.ExternalApi.Domain.MatchingRecommendationCandidate;
using BusinessMathingStatus = SharedKernel.ExternalApi.Domain.MatchingRecommendationCandidate.CaseStatus;
using MAndAMatchingStatus = SharedKernel.ExternalApi.Domain.MAndAMatchingCaseManagement.CaseStatus;
using MAndAMatching = SharedKernel.ExternalApi.Domain.MAndAMatchingCaseManagement;
using CaseWorkflowOptimization = SharedKernel.ExternalApi.Domain.CaseWorkflowOptimization;
using CommonCaseStatus = SharedKernel.ExternalApi.Domain.CaseWorkflowOptimization.CaseStatus;

namespace CustomerProposalService.UseCases.Case;
public interface ICaseUtility
{
    Task<Result<string>> AddFiles(IEnumerable<IFormFile>? files, string id);
    Task<Result<string>> DeleteAllFiles(IUnitOfWork unitOfWork, Domain.Entities.Case currentData);
    Task<Result<string>> DeleteAllLinks(IUnitOfWork unitOfWork, Domain.Entities.Case currentData);
    Task<Result<string>> ReplaceFiles(IUnitOfWork unitOfWork, IEnumerable<IFormFile>? uploadFiles, Domain.Entities.Case currentData);
    Task<Result<string>> ReplaceLinks(IUnitOfWork unitOfWork, IEnumerable<Domain.Entities.CaseLink> caseLinks, Domain.Entities.Case currentData);
    Task<Result<string>> UpdateFiles(IUnitOfWork unitOfWork, IEnumerable<IFormFile> uploadFiles, Domain.Entities.Case currentData);
    Task<Result<string>> UpdateLinks(IUnitOfWork unitOfWork, IEnumerable<Domain.Entities.CaseLink> caseLinks, Domain.Entities.Case currentData);
    IEnumerable<BusinessMathingStatus> ConvertCaseStatusToMatchingStatus(IEnumerable<string>? statuses);
    IEnumerable<MAndAMatchingStatus> ConvertCaseStatusToMAndAMatchingStatus(IEnumerable<string>? statuses);
    IEnumerable<BusinessMatching.BuySellType> ConvertCaseCategoryToMatchingBuySellType(IEnumerable<CaseCategory>? caseCategories);
    IEnumerable<MAndAMatching.BuySellType> ConvertCaseCategoryToMAndAMatchingBuySellType(IEnumerable<CaseCategory>? caseCategories);
    IEnumerable<CaseWorkflowOptimization.CaseStatus> ConvertCaseStatusToCommonCaseStatus(IEnumerable<string>? statuses);
    CommonCaseStatus GetStatusOfCommonCase(string commonCaseStatus);
}
