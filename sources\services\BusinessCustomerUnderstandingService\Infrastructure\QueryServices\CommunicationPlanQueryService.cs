using BusinessCustomerUnderstandingService.Domain.Dto.BusinessUnderstanding;
using BusinessCustomerUnderstandingService.Domain.Entities;
using BusinessCustomerUnderstandingService.Domain.Enums;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Services.Queries;
using Microsoft.EntityFrameworkCore;
using Nut.Results;
using Shared.Application;
using Shared.Linq;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;

internal class CommunicationPlanQueryService : IFindSpecificCustomerCommunicationPlanQueryService
{
    private readonly ApplicationDbContext _dbContext;

    public CommunicationPlanQueryService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    async Task<Result<PaginatedResult<CommunicationPlanBasicInfo>>> IFindSpecificCustomerCommunicationPlanQueryService.Handle(
        string id,
        string? title,
        string? staffName,
        IEnumerable<CommunicationPlanType> types,
        IEnumerable<Status?> statuses,
        DateTimeOffset? expiredAtFrom,
        DateTimeOffset? expiredAtTo,
        PaginationOption option)
    {
        var ourPolicyUnderstandings = (!(types?.Any() == true) || types.Contains(CommunicationPlanType.OurPolicyUnderstanding)) ?
                _dbContext.OurPolicyUnderstandings
                    .Where(x => x.BusinessUnderstandingId == id)
                    .WhereIfNotEmpty(title, x => x.Title.Contains(title!))
                    .WhereIfNotEmpty(staffName, x => x.StaffName == null || x.StaffName.Contains(staffName!))
                    .WhereIf(statuses?.Any() == true, x => statuses!.Contains(x.Status))
                    .WhereIf(expiredAtFrom != null, x => expiredAtFrom <= x.ExpiredAt)
                    .WhereIf(expiredAtTo != null, x => x.ExpiredAt <= expiredAtTo)
                    .AsNoTracking()
                : Enumerable.Empty<OurPolicyUnderstanding>();

        var customerIdeasUnderstandings = (!(types?.Any() == true) || types.Contains(CommunicationPlanType.CustomerIdeasUnderstanding)) ?
                _dbContext.CustomerIdeasUnderstandings
                    .Where(x => x.BusinessUnderstandingId == id)
                    .WhereIfNotEmpty(title, x => x.Title.Contains(title!))
                    .WhereIfNotEmpty(staffName, x => x.StaffName == null || x.StaffName.Contains(staffName!))
                    .WhereIf(statuses?.Any() == true, x => statuses!.Contains(x.Status))
                    .WhereIf(expiredAtFrom != null, x => expiredAtFrom <= x.ExpiredAt)
                    .WhereIf(expiredAtTo != null, x => x.ExpiredAt <= expiredAtTo)
                    .AsNoTracking()
                : Enumerable.Empty<CustomerIdeasUnderstanding>();

        var sharingOfFinances = (!(types?.Any() == true) || types.Contains(CommunicationPlanType.SharingOfFinance)) ?
                _dbContext.SharingOfFinances
                    .Where(x => x.BusinessUnderstandingId == id)
                    .WhereIfNotEmpty(title, x => x.Title!.Contains(title!))
                    .WhereIfNotEmpty(staffName, x => x.StaffName == null || x.StaffName.Contains(staffName!))
                    .WhereIf(statuses?.Any() == true, x => statuses!.Contains(x.Status))
                    .WhereIf(expiredAtFrom != null, x => expiredAtFrom <= x.ExpiredAt)
                    .WhereIf(expiredAtTo != null, x => x.ExpiredAt <= expiredAtTo)
                    .AsNoTracking()
                : Enumerable.Empty<SharingOfFinance>();

        var hypotheticalDiscussionOfIssues = (!(types?.Any() == true) || types.Contains(CommunicationPlanType.HypotheticalDiscussionOfIssues)) ?
                _dbContext.HypotheticalDiscussionOfIssues
                    .Where(x => x.BusinessUnderstandingId == id)
                    .WhereIfNotEmpty(title, x => x.Title!.Contains(title!))
                    .WhereIfNotEmpty(staffName, x => x.StaffName == null || x.StaffName.Contains(staffName!))
                    .WhereIf(statuses?.Any() == true, x => statuses!.Contains(x.Status))
                    .WhereIf(expiredAtFrom != null, x => expiredAtFrom <= x.ExpiredAt)
                    .WhereIf(expiredAtTo != null, x => x.ExpiredAt <= expiredAtTo)
                    .AsNoTracking()
                : Enumerable.Empty<HypotheticalDiscussionOfIssues>();

        var toDos = (!(types?.Any() == true) || types.Contains(CommunicationPlanType.ToDo)) ?
                _dbContext.ToDo
                    .Where(x => x.BusinessUnderstandingId == id)
                    .WhereIfNotEmpty(title, x => x.Title!.Contains(title!))
                    .WhereIfNotEmpty(staffName, x => x.StaffName == null || x.StaffName.Contains(staffName!))
                    .WhereIf(statuses?.Any() == true, x => statuses!.Contains(x.Status))
                    .WhereIfNotEmpty(staffName, x => x.StaffName!.Contains(staffName!))
                    .WhereIf(expiredAtFrom != null, x => expiredAtFrom <= x.ExpiredAt)
                    .WhereIf(expiredAtTo != null, x => x.ExpiredAt <= expiredAtTo)
                    .AsNoTracking()
                : Enumerable.Empty<ToDo>();

        var results = new List<CommunicationPlanBasicInfo>();
        results.AddRange(ourPolicyUnderstandings.Any() ? ourPolicyUnderstandings.Select(x => new CommunicationPlanBasicInfo { Id = x.Id, Status = x.Status, StaffName = x.StaffName, ExpiredAt = x.ExpiredAt, Title = x.Title, Type = CommunicationPlanType.OurPolicyUnderstanding }) : Enumerable.Empty<CommunicationPlanBasicInfo>());
        results.AddRange(customerIdeasUnderstandings.Any() ? customerIdeasUnderstandings.Select(x => new CommunicationPlanBasicInfo { Id = x.Id, Status = x.Status, StaffName = x.StaffName, ExpiredAt = x.ExpiredAt, Title = x.Title, Type = CommunicationPlanType.CustomerIdeasUnderstanding }) : Enumerable.Empty<CommunicationPlanBasicInfo>());
        results.AddRange(sharingOfFinances.Any() ? sharingOfFinances.Select(x => new CommunicationPlanBasicInfo { Id = x.Id, Status = x.Status, StaffName = x.StaffName, ExpiredAt = x.ExpiredAt, Title = x.Title, Type = CommunicationPlanType.SharingOfFinance }) : Enumerable.Empty<CommunicationPlanBasicInfo>());
        results.AddRange(hypotheticalDiscussionOfIssues.Any() ? hypotheticalDiscussionOfIssues.Select(x => new CommunicationPlanBasicInfo { Id = x.Id, Status = x.Status, StaffName = x.StaffName, ExpiredAt = x.ExpiredAt, Title = x.Title, Type = CommunicationPlanType.HypotheticalDiscussionOfIssues }) : Enumerable.Empty<CommunicationPlanBasicInfo>());
        results.AddRange(toDos.Any() ? toDos.Select(x => new CommunicationPlanBasicInfo { Id = x.Id, Status = x.Status, StaffName = x.StaffName, ExpiredAt = x.ExpiredAt, Title = x.Title, Type = CommunicationPlanType.ToDo }) : Enumerable.Empty<CommunicationPlanBasicInfo>());


        return await results.SortBy(option.Sort).ToPaginatedAsync(option.PageIndex, option.PageSize);
    }
}
