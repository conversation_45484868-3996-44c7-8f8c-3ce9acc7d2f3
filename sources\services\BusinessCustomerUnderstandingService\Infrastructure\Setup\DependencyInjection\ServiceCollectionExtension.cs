using System.Reflection;
using BusinessCustomerUnderstandingService.Configurations;
using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Infrastructure.QueryServices;
using BusinessCustomerUnderstandingService.Infrastructure.QueryServices.External;
using BusinessCustomerUnderstandingService.Infrastructure.Storage;
using BusinessCustomerUnderstandingService.Services.Domain;
using BusinessCustomerUnderstandingService.Services.Queries;
using BusinessCustomerUnderstandingService.UseCases;
using BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindCustomerIdentificationIdsByBusinessUnderstandingIds;
using BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.AddBusinessUnderstandingApproachFromCsv.Csv;
using BusinessCustomerUnderstandingService.UseCases.CommercialDistribution.GetCommercialDistributionSupportingData;
using BusinessCustomerUnderstandingService.UseCases.CustomerIdentification.GetCommercialDistributionByCustomerIdentificationId;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Nut.MediatR;
using Shared.Application;
using Shared.AzureBlob.QuillContents;
using Shared.Messaging;
using Shared.ServiceModule;
using Shared.Services;
using Shared.UseCase.PipelineBehaviors;
using SharedKernel.ExternalApi.Configurations;
using SharedKernel.ExternalApi.MessageContract.CustomerFixedInformation;
using SharedKernel.ExternalApi.MessageContract.CustomerIdentifying;
using SharedKernel.ExternalApi.MessageContract.Notification;
using SharedKernel.ExternalApi.Middleware;

namespace BusinessCustomerUnderstandingService.Infrastructure.Setup.DependencyInjection;

/// <summary>
/// <see cref="ServiceCollection"/> の拡張メソッドを定義します。
/// </summary>
public static class ServiceCollectionExtension
{
    public static IServiceBuilder AddBusinessCustomerUnderstandingService(this IServiceCollection services, ServiceSettings settings, IApplicationEnvironment env, ApiManagementSettings? apiManagementSettings, ExternalApiSettings? externalApiSettings)
    {
        if (services is null)
            throw new ArgumentNullException(nameof(services));
        if (settings is null)
            throw new ArgumentNullException(nameof(settings));
        if (env is null)
            throw new ArgumentNullException(nameof(env));

        // 設定をDIに登録
        services.AddSingleton(settings);
        if (apiManagementSettings != null)
        {
            services.AddSingleton(apiManagementSettings);
        }
        if (externalApiSettings != null)
        {
            services.AddSingleton(externalApiSettings);
        }
        // バリデーターを登録
        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());
        // メディエーターを登録
        services.AddMediatR(config =>
        {
            config.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
        });
        // メッセージ機能を登録
        services.AddMessaging(Assembly.GetExecutingAssembly());
        // IAuthorizer<>インターフェイスを実装したクラスを登録
        services.AddTransientGenericInterfaceType(typeof(IAuthorizer<>));
        // ILoggingInOutValueCollector<,>インターフェイスを実装したクラスを登録
        services.AddTransientGenericInterfaceType(typeof(ILoggingInOutValueCollector<,>));
        
        // 各ビヘイビアを登録
        services.TryAddTransient(typeof(LoggingIncludeInOutValueBehavior<,>));
        services.TryAddTransient(typeof(OpenTelemetryBehavior<,>));
        services.TryAddTransient(typeof(AuthorizationBehavior<,>));
        services.TryAddTransient(typeof(ExceptionResolveBehavior<,>));
        services.TryAddTransient(typeof(FluentValidationBehavior<,>));
        services.TryAddTransient(typeof(RoleAuthorizationBehavior<,>));
        // 現在日付の取得サービスを登録
        services.TryAddTransient<ICurrentDateTimeService, CurrentDateTimeService>();
        // サービスの初期化処理を登録
        services.AddTransient<IInitializer, ServiceInitializer>();
        // データベースを登録します。
        services.AddDbContext<ApplicationDbContext>(
            options =>
            {
                if (env.IsLocalOrDevelopment())
                {
                    options.EnableSensitiveDataLogging();
                    options.EnableDetailedErrors();
                }

                if (settings.Database.UseInMemoryDatabase)
                {
                    options.UseInMemoryDatabase(typeof(ServiceCollectionExtension).Assembly.FullName!);
                    options.ConfigureWarnings(
                        warnings => warnings.Ignore(InMemoryEventId.TransactionIgnoredWarning));
                }
                else
                {
                    if (!string.IsNullOrEmpty(settings.Database.ConnectionString))
                    {
                        options.UseSqlServer(
                        settings.Database.ConnectionString,
                        b =>
                        {
                            b.MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName);
                        });
                    }
                }
            },
            ServiceLifetime.Scoped);

        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddScoped<IRepositoryFactory>(provider => provider.GetService<IUnitOfWork>()!);

        // ここまでの設定は基本構成で必要な内容です。
        // サービスで必要な内容の設定を追加してください。
        services.AddScoped<IGetCommercialDistributionByCustomerIdentificationIdQueryService, GetCommercialDistributionByCustomerIdentificationIdQueryService>();
        services.AddScoped<IAddBusinessUnderstandingApproachFromCsvMapper, AddBusinessUnderstandingApproachFromCsvMapper>();
        services.AddScoped<IGetHypotheticalDiscussionOfIssuesQueryService, HypotheticalDiscussionOfIssuesQueryService>();
        services.AddScoped<IGetHypotheticalDiscussionOfIssuesByThreadIdQueryService, HypotheticalDiscussionOfIssuesQueryService>();
        services.AddScoped<IFindHypotheticalDiscussionOfIssuesQueryService, HypotheticalDiscussionOfIssuesQueryService>();
        services.AddScoped<IGetOurPolicyUnderstandingQueryService, OurPolicyUnderstandingQueryService>();
        services.AddScoped<IFindSpecificCustomerCommunicationPlanQueryService, CommunicationPlanQueryService>();
        services.AddScoped<IFindCustomerIdentificationIdsByBusinessUnderstandingIdsQueryService, FindCustomerIdentificationIdsByBusinessUnderstandingIdsQueryService>();
        services.AddScoped<IFindCommunicationPlanStaffQueryService, FindCommunicationPlanStaffQueryService>();
        services.AddScoped<IGetCommunicationPlanCountQueryService, GetCommunicationPlanCountQueryService>();
        services.AddScoped<IGetDirectLinkQueryService, GetDirectLinkQueryService>();
        services.AddScoped<IFindBusinessUnderstandingQueryService, FindBusinessUnderstandingQueryService>();
        services.AddScoped<IFindCustomerIdentificationIdsBySearchConditionQueryService, FindCustomerIdentificationIdsBySearchConditionQueryService>();
        services.AddScoped<IFindBusinessUnderstandingsByCustomerIdentificationIdsQueryService, FindBusinessUnderstandingsByCustomerIdentificationIdsQueryService>();
        services.AddScoped<IFileProcessingService, FileProcessingService>();
        services.AddScoped<IQuillDeltaImageCompressService, QuillDeltaImageCompressService>();
        services.AddScoped<IQuillContentsUtility, QuillContentsUtility>();
        services.AddScoped<IQuillImageService, QuillImageService>();
        services.AddScoped<IGetCommunicationPlanSummaryQueryService, GetCommunicationPlanSummaryQueryService>();
        services.AddScoped<IGetBusinessUnderstandingSummaryQueryService, GetBusinessUnderstandingSummaryQueryService>();
        services.AddScoped<IFindCalcTargetBusinessUnderstandingsByIdsQueryService, FindCalcTargetBusinessUnderstandingsByIdsQueryService>();
        services.AddScoped<IGetBPAndCAByCustomerIdentificationIdQueryService, GetBPAndCAByCustomerIdentificationIdQueryService>();
        services.AddScoped<IFindUserBusinessCustomerIdentificationSummaryQueryService, FindUserBusinessCustomerIdentificationSummaryQueryService>();
        services.AddScoped<IGetCommercialDistributionSupportingDataQueryService, GetCommercialDistributionSupportingDataQueryService>();
        services.AddScoped<IGetBusinessUnderstandingForCgcQueryService, GetBusinessUnderstandingForCgcQueryService>();

        // API
        services
            .AddGeneralPostApiClient<FindCustomerByIdsQuery, IEnumerable<FindCustomerByIdsResult>>("ibp-customer-identifying/", apiManagementSettings, externalApiSettings?.CustomerIdentifying, env.IsLocalOrDevelopment())
            .AddGeneralGetApiClient<FindCustomerQuery, PaginatedResult<FindCustomerResult>>("ibp-customer-identifying/", apiManagementSettings, externalApiSettings?.CustomerIdentifying, env.IsLocalOrDevelopment())
            .AddGeneralPostApiClient<AddBusinessUnderstandingCommentReplyNotificationQuery, string>("ibp-notification/", apiManagementSettings, externalApiSettings?.Notification, env.IsLocalOrDevelopment())
            .AddGeneralPostApiClient<AddCustomerIdeasUnderstandingCommentReplyNotificationQuery, string>("ibp-notification/", apiManagementSettings, externalApiSettings?.Notification, env.IsLocalOrDevelopment())
            .AddGeneralPostApiClient<AddHDOICommentReplyNotificationQuery, string>("ibp-notification/", apiManagementSettings, externalApiSettings?.Notification, env.IsLocalOrDevelopment())
            .AddGeneralPostApiClient<AddOurPolicyUnderstandingCommentReplyNotificationQuery, string>("ibp-notification/", apiManagementSettings, externalApiSettings?.Notification, env.IsLocalOrDevelopment())
            .AddGeneralPostApiClient<AddSharingOfFinanceCommentReplyNotificationQuery, string>("ibp-notification/", apiManagementSettings, externalApiSettings?.Notification, env.IsLocalOrDevelopment())
            .AddGeneralPostApiClient<AddToDoCommentReplyNotificationQuery, string>("ibp-notification/", apiManagementSettings, externalApiSettings?.Notification, env.IsLocalOrDevelopment())
            .AddGeneralPostApiClient<FindCustomerStaffQuery, IEnumerable<FindCustomerStaffResult>>("ibp-customer-fixed-information/", apiManagementSettings, externalApiSettings?.CustomerFixedInformation, env.IsLocalOrDevelopment());

        // blob
        services.AddAzureClients(cfg =>
        {
            cfg.AddBlobServiceClient(settings.Blob.ConnectionString!).WithName(AttachedFamilyTreeStorageClientProvider.AzureClientName);
        });
        services.AddTransient<IAttachedFamilyTreeStorageClientProvider, AttachedFamilyTreeStorageClientProvider>();

        services.AddAzureClients(cfg =>
        {
            cfg.AddBlobServiceClient(settings.Blob.ConnectionString!).WithName(BusinessUnderstandingStorageClientProvider.AzureClientName);
        });
        services.AddTransient<IBusinessUnderstandingStorageClientProvider, BusinessUnderstandingStorageClientProvider>();

        services.AddAzureClients(cfg =>
        {
            cfg.AddBlobServiceClient(settings.Blob.ConnectionString!).WithName(CommunicationPlanStorageClientProvider.AzureClientName);
        });
        services.AddTransient<ICommunicationPlanStorageClientProvider, CommunicationPlanStorageClientProvider>();

        services.AddAzureClients(cfg =>
        {
            cfg.AddBlobServiceClient(settings.Blob.ConnectionString!).WithName(BatchAddCommunicationPlanStorageClientProvider.AzureClientName);
        });
        services.AddTransient<IBatchAddCommunicationPlanStorageClientProvider, BatchAddCommunicationPlanStorageClientProvider>();

        services.AddAzureClients(cfg =>
        {
            cfg.AddBlobServiceClient(settings.Blob.ConnectionString!).WithName(ExternalEnvironmentMasterStorageClientProvider.AzureClientName);
        });
        services.AddTransient<IExternalEnvironmentMasterStorageClientProvider, ExternalEnvironmentMasterStorageClientProvider>();


        services.AddAzureClients(cfg =>
        {
            cfg.AddBlobServiceClient(settings.Blob.ConnectionString!).WithName(StorageClientProvider.AzureClientName);
        });
        services.AddTransient<IStorageClientProvider, StorageClientProvider>();


        return new ServiceBuilder(services);
    }

    /// <summary>
    /// 指定されたインターフェイスを実装しているクラスをアセンブリから探し出して登録します。
    /// </summary>
    private static IServiceCollection AddTransientGenericInterfaceType(this IServiceCollection source,
            Type serviceType)
    {
        if (source is null) throw new ArgumentNullException(nameof(source));
        if (serviceType is null) throw new ArgumentNullException(nameof(serviceType));

        foreach (var target in Assembly.GetExecutingAssembly().DefinedTypes.Where(t => !t.IsGenericType))
        {
            var interfaceType = target
                .GetInterfaces()
                .Where(i => i.IsGenericType)
                .FirstOrDefault(i => i.GetGenericTypeDefinition() == serviceType);
            if (interfaceType != null)
            {
                source.AddTransient(interfaceType, target);
            }
        }
        return source;
    }
}
