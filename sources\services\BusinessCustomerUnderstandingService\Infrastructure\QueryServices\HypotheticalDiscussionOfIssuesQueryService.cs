using BusinessCustomerUnderstandingService.Domain.Entities;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Services.Queries;
using Microsoft.EntityFrameworkCore;
using Nut.Results;
using Shared.Linq;
using Shared.Results.Errors;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;

internal class HypotheticalDiscussionOfIssuesQueryService : IGetHypotheticalDiscussionOfIssuesQueryService, IGetHypotheticalDiscussionOfIssuesByThreadIdQueryService, IFindHypotheticalDiscussionOfIssuesQueryService
{
    private readonly ApplicationDbContext _dbContext;

    public HypotheticalDiscussionOfIssuesQueryService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    async Task<Result<HypotheticalDiscussionOfIssues>> IGetHypotheticalDiscussionOfIssuesQueryService.Handle(string id)
    {
        var hypotheticalDiscussionOfIssue = await _dbContext.HypotheticalDiscussionOfIssues
           .Where(e => e.Id == id)
           .Include(x => x.Threads).ThenInclude(x => x.Comments).ThenInclude(x => x.Files)
           .Include(x => x.Threads).ThenInclude(x => x.Comments).ThenInclude(x => x.Reactions)
           .Include(x => x.Threads).ThenInclude(x => x.Files)
           .Include(x => x.Threads).ThenInclude(x => x.Reactions)
           .OrderBy(x => x.Order)
           .AsNoTracking()
           .FirstOrDefaultAsync();

        if (hypotheticalDiscussionOfIssue is null)
        {
            return Result.Error<HypotheticalDiscussionOfIssues>(new DataNotFoundException());
        }

        return Result.Ok<HypotheticalDiscussionOfIssues>(hypotheticalDiscussionOfIssue!);
    }

    async Task<Result<HypotheticalDiscussionOfIssues>> IGetHypotheticalDiscussionOfIssuesByThreadIdQueryService.Handle(string threadId)
    {

        var thread = await _dbContext.HypotheticalDiscussionOfIssuesThreads.AsNoTracking().SingleAsync(x => x.Id == threadId);
        var task = await _dbContext.HypotheticalDiscussionOfIssues.AsNoTracking().SingleAsync(x => x.Id == thread.HypotheticalDiscussionOfIssuesId);

        return Result.Ok<HypotheticalDiscussionOfIssues>(task);
    }

    async Task<Result<IEnumerable<HypotheticalDiscussionOfIssues>>> IFindHypotheticalDiscussionOfIssuesQueryService.Handle(GetHypotheticalDiscussionOfIssuesQuery request)
    {
        var hypotheticalDiscussionOfIssues = _dbContext.HypotheticalDiscussionOfIssues
           .WhereIfNotEmpty(request.Id, e => e.Id == request.Id)
           .WhereIfNotEmpty(request.HypothesisForIssue, e => e.CurrentSituation == request.HypothesisForIssue)
           .WhereIfNotEmpty(request.HypothesisForSolution, e => e.Ideal == request.HypothesisForSolution)
           .WhereIf(request.ExpiredDateTimeFrom is not null, l => request.ExpiredDateTimeFrom <= l.ExpiredAt)
           .WhereIf(request.ExpiredDateTimeTo is not null, l => l.ExpiredAt <= request.ExpiredDateTimeTo)
           .WhereIfNotEmpty(request.Title, e => e.Title == request.Title)
           .WhereIf(request.Status is not null, x => x.Status == request.Status)
           .WhereIfNotEmpty(request.BusinessUnderstandingId, e => e.BusinessUnderstandingId == request.BusinessUnderstandingId)
           .Include(x => x.Threads).ThenInclude(x => x.Comments).ThenInclude(x => x.Files)
           .Include(x => x.Threads).ThenInclude(x => x.Comments).ThenInclude(x => x.Reactions)
           .Include(x => x.Threads).ThenInclude(x => x.Files)
           .Include(x => x.Threads).ThenInclude(x => x.Reactions)
           .OrderBy(x => x.Order)
           .AsNoTracking();

        return Result.Ok(hypotheticalDiscussionOfIssues.AsEnumerable());
    }
}
