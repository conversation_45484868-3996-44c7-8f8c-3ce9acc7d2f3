using MediatR;
using BusinessCustomerUnderstandingService.Infrastructure.Storage;
using Nut.Results;
using Shared.Services;
using Shared.Messaging;
using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Externals.BusinessCustomerUnderstanding;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingFile.UploadBusinessUnderstandingFile;
public class UploadBusinessUnderstandingFileHandler : IRequestHandler<UploadBusinessUnderstandingFileCommand, Result<List<Domain.Entities.BusinessUnderstandingFile>>>
{
    private readonly IBusinessUnderstandingStorageClientProvider _objectStorageClientProvider;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDateTimeService;
    private readonly IMessageSender<UpdateBusinessUnderstandingUpdatedDateTime> _businessUnderstandingUpdateMessageSender;

    public UploadBusinessUnderstandingFileHandler(
        IBusinessUnderstandingStorageClientProvider objectStorageClientProvider,
        IUnitOfWork unitOfWork,
        ICurrentDateTimeService currentDateTimeService,
        IMessageSender<UpdateBusinessUnderstandingUpdatedDateTime> businessUnderstandingUpdateMessageSender
        )
    {
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
        _businessUnderstandingUpdateMessageSender = businessUnderstandingUpdateMessageSender ?? throw new ArgumentNullException(nameof(businessUnderstandingUpdateMessageSender));
    }

    public async Task<Result<List<Domain.Entities.BusinessUnderstandingFile>>> Handle(UploadBusinessUnderstandingFileCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var uploadFiles = request.UploadFiles ?? new List<Microsoft.AspNetCore.Http.IFormFile>();
        var selectedFilesNames = request.SelectedFileNames ?? new List<string>();

        //Blobとの接続を確立
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        var businessUnderstandingFiles = new List<Domain.Entities.BusinessUnderstandingFile>();

        foreach (var file in uploadFiles)
        {
            var findFileRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingFile>();
            var spec = new UploadBusinessUnderstandingFileSpecification(request.BusinessUnderstandingId, file.FileName);
            var result = await findFileRepository.FindAsync(spec);

            if (result.IsError)
            {
                return result.PreserveErrorAs<List<Domain.Entities.BusinessUnderstandingFile>>();
            }

            // 同じファイル名が存在している
            if (result.Get().Any())
            {
                var businessUnderstandingFile = result.Get().First();
                businessUnderstandingFile.FileName = file.FileName;
                businessUnderstandingFile.UpdatedDateTime = _currentDateTimeService.NowDateTimeOffset();
                businessUnderstandingFile.UpdaterId = request.UpdaterId;
                businessUnderstandingFile.UpdaterName = request.UpdaterName;
                businessUnderstandingFile.BusinessUnderstandingId = request.BusinessUnderstandingId;

                var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingFile>();
                await repository.UpdateAsync(businessUnderstandingFile).ConfigureAwait(false);
                await _unitOfWork.SaveEntitiesAsync().ConfigureAwait(false);

            }
            else
            {
                var businessUnderstandingFile = new Domain.Entities.BusinessUnderstandingFile()
                {
                    Id = Ulid.NewUlid().ToString(),
                    FileName = file.FileName,
                    UpdatedDateTime = _currentDateTimeService.NowDateTimeOffset(),
                    UpdaterId = request.UpdaterId,
                    UpdaterName = request.UpdaterName,
                    BusinessUnderstandingId = request.BusinessUnderstandingId,
                };

                var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingFile>();
                await repository.AddAsync(businessUnderstandingFile).ConfigureAwait(false);
                await _unitOfWork.SaveEntitiesAsync().ConfigureAwait(false);

                businessUnderstandingFiles.Add(businessUnderstandingFile);
            }

            // Blobに登録
            var stream = file.OpenReadStream();
            var addResult = await storageClient.PostAsync($"business-understanding/{request.BusinessUnderstandingId}/{file.FileName}", stream).ConfigureAwait(false);
            if (addResult.IsError) return addResult.PreserveErrorAs<List<Domain.Entities.BusinessUnderstandingFile>>();
        }

        if (uploadFiles.Any())
        {
            // 事業性理解最終更新日更新はアップロードファイル存在時のみ
            var updateBusinessUnderstandingResult = await _businessUnderstandingUpdateMessageSender.SendAsync(new UpdateBusinessUnderstandingUpdatedDateTime()
            {
                BusinessUnderstandingId = request.BusinessUnderstandingId,
                UpdaterId = request.UpdaterId,
                UpdaterName = request.UpdaterName,
            });
            if (updateBusinessUnderstandingResult.IsError) return updateBusinessUnderstandingResult.PreserveErrorAs<List<Domain.Entities.BusinessUnderstandingFile>>();
        }

        return businessUnderstandingFiles;
    }
}
