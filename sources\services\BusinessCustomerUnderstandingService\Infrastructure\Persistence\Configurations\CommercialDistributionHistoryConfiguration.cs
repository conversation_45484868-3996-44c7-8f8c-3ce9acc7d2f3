using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class CommercialDistributionHistoryConfiguration : IEntityTypeConfiguration<CommercialDistributionHistory>
{
    public void Configure(EntityTypeBuilder<CommercialDistributionHistory> builder)
    {
        builder.HasOne<CommercialDistribution>()
            .WithMany(entity => entity.Histories)
            .HasForeignKey(c => c.OriginalId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(c => c.Id)
            .IsRequired()
            .HasMax<PERSON>ength(26);

        builder.Property(c => c.CanvasColor)
            .HasMaxLength(9);
    }
}
