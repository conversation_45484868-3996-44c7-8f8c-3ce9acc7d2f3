using Microsoft.EntityFrameworkCore;
using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessUnderstandingByCustomerIdentificationId;

public class GetBusinessUnderstandingSpecification : BaseSpecification<Domain.Entities.BusinessUnderstanding>
{
    public GetBusinessUnderstandingSpecification(string id)
    {
        Query
            .Where(x => x.Id == id)
            .Include(x => x.Management)
            .Include(x => x.FiveStepFrameWork)
            .Include(x => x.FiveForceFramework)
            .Include(x => x.BusinessCustomer)
            .Include(x => x.ManagementPlan)
            .Include(x => x.ESGAndSDGs)
            .Include(x => x.ExternalEnvironment)
            .Include(x => x.ExternalEnvironment!.ExternalEnvironmentMaster)
            .Include(x => x.RelationLevel)
            .Include(x => x.ThreeCAnalysis)
            .Include(x => x.SWOTAnalysis)
            .Include(x => x.CommercialDistribution)
            .Include(x => x.FamilyTree)
            .Include(x => x.BusinessUnderstandingMaterializedView)
            .AsNoTracking();
    }
}
