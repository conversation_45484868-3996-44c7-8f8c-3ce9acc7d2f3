using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Externals.BusinessCustomerUnderstanding;
using MediatR;
using Nut.Results;
using Shared.Messaging;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartner.AddBusinessPartner;

public class AddBusinessPartnerHandler : IRequestHandler<AddBusinessPartnerCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageSender<UpdateBusinessUnderstandingUpdatedDateTime> _businessUnderstandingUpdateMessageSender;

    public AddBusinessPartnerHandler(IUnitOfWork unitOfWork, IMessageSender<UpdateBusinessUnderstandingUpdatedDateTime> businessUnderstandingUpdateMessageSender)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _businessUnderstandingUpdateMessageSender = businessUnderstandingUpdateMessageSender ?? throw new ArgumentNullException(nameof(businessUnderstandingUpdateMessageSender));
    }

    public async Task<Result<string>> Handle(AddBusinessPartnerCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessPartner>();

        var businessPartnerId = Ulid.NewUlid().ToString();
        var businessPartner = new Domain.Entities.BusinessPartner()
        {
            Id = businessPartnerId,
            BusinessUnderstandingId = request.BusinessUnderstandingId,
            BusinessPartnerCustomerIdentificationId = request.BusinessPartnerCustomerIdentificationId,
            BusinessPartnerCustomerName = request.BusinessPartnerCustomerName,
            BusinessPartnerIndustry = new()
            {
                Id = Ulid.NewUlid().ToString(),
                BusinessPartnerId = businessPartnerId,
                SubIndustryCode = request.BusinessPartnerIndustry.SubIndustryCode,
                DetailIndustryCode = request.BusinessPartnerIndustry.DetailIndustryCode,
                IndustryCode = request.BusinessPartnerIndustry.IndustryCode,
            },
            Note = request.Note,
        };

        var updateBusinessUnderstandingResult = await UpdateBusinessUnderstandingUpdatedDateTime(request.BusinessUnderstandingId, request.staffId, request.staffName);
        if (updateBusinessUnderstandingResult.IsError) return updateBusinessUnderstandingResult.PreserveErrorAs<string>();

        return await repository
            .AddAsync(businessPartner) // データを追加します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(businessPartner.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }

    private async Task<Result> UpdateBusinessUnderstandingUpdatedDateTime(string businessUnderstandingId, string updaterId, string updaterName)
    {
        var updateBusinessUnderstandingResult = await _businessUnderstandingUpdateMessageSender.SendAsync(
            new UpdateBusinessUnderstandingUpdatedDateTime()
            {
                BusinessUnderstandingId = businessUnderstandingId,
                UpdaterId = updaterId,
                UpdaterName = updaterName,
            }
        );

        return updateBusinessUnderstandingResult;
    }
}
