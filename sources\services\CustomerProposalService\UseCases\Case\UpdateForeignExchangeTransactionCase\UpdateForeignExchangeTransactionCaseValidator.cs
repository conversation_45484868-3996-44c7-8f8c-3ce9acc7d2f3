using FluentValidation;
using CustomerProposalService.Domain.Enums;

namespace CustomerProposalService.UseCases.Case.UpdateForeignExchangeTransactionCase;

public class UpdateForeignExchangeTransactionCaseValidator : AbstractValidator<UpdateForeignExchangeTransactionCaseCommand>
{
    public UpdateForeignExchangeTransactionCaseValidator()
    {
        // Case Entity
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.CaseName)
            .NotEmpty()
            .MaximumLength(50);
        RuleFor(v => v.CaseStatus)
            .NotEmpty()
            .IsInEnum()
            .NotEqual(CaseStatus.Undefined);
        RuleFor(e => e.CaseOutline).CaseOutline();
        RuleFor(v => v.StaffId).NotEmpty().MaximumLength(50);
        RuleFor(v => v.StaffName).NotEmpty().MaximumLength(50);
        // ForeignExchangeTransactionCase Entity
        RuleFor(e => e.ApprovalType).MaximumLength(50);
        RuleFor(e => e.CollateralType).MaximumLength(50);
        RuleFor(e => e.CollateralOrGuaranteeCustom).MaximumLength(50);
        RuleFor(e => e.GuaranteeType).MaximumLength(50);
        RuleFor(e => e.CancelTypeOfLoan).IsInEnum();
        RuleFor(e => e.CancelReason).CancelReason();
        RuleFor(v => v.Version).NotEmpty();
    }
}
