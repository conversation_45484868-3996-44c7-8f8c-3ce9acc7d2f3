using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingMaterializedViewQueue.AddBusinessUnderstandingMaterializedViewQueue;

public class FindBusinessUnderstandingSpecification : BaseSpecification<Domain.Entities.BusinessUnderstanding>
{
    public FindBusinessUnderstandingSpecification(IEnumerable<Guid> CustomerIdentificationIds)
    {
        Query
            .Include(x => x.BusinessCustomer)
            .Where(x => CustomerIdentificationIds.Contains(x.BusinessCustomer.CustomerIdentificationId))
            .AsNoTracking();
    }
}
