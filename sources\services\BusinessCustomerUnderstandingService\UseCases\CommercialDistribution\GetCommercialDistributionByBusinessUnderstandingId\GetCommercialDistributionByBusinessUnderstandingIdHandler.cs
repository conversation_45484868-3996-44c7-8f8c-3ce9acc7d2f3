using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistribution.GetCommercialDistributionByBusinessUnderstandingId;

public class GetCommercialDistributionByBusinessUnderstandingIdHandler : IRequestHandler<GetCommercialDistributionByBusinessUnderstandingIdQuery, Result<GetCommercialDistributionByBusinessUnderstandingIdResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetCommercialDistributionByBusinessUnderstandingIdHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetCommercialDistributionByBusinessUnderstandingIdResult>> Handle(GetCommercialDistributionByBusinessUnderstandingIdQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CommercialDistribution, string>();

        return repository.SingleAsync(new GetCommercialDistributionByBusinessUnderstandingIdSpecification(request.BusinessUnderstandingId))
            .Map(v => new GetCommercialDistributionByBusinessUnderstandingIdResult
            (
                v.Id,
                v.CanvasColor,
                v.UpdaterId,
                v.UpdaterName,
                v.UpdatedDateTime,
                v.Nodes,
                v.Edges,
                v.BusinessUnderstandingId,
                v.Version));
    }
}
