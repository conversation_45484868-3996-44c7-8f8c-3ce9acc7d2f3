using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Domain.Entities;
using BusinessCustomerUnderstandingService.Domain.Enums;
using BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.AddBusinessUnderstandingApproachFromCsv.Csv;
using CsvHelper;
using MediatR;
using Nut.Results;
using Shared.Messaging;
using Shared.Results.Errors;
using Shared.Services;
using SharedKernel.ExternalApi.MessageContract.CustomerIdentifying;
using ValidationException = Shared.Results.Errors.ValidationException;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.AddBusinessUnderstandingApproachFromCsv;

public class AddBusinessUnderstandingApproachFromCsvHandler : IRequestHandler<AddBusinessUnderstandingApproachFromCsvCommand, Result>
{
    private const string FormatErrorMessage = "CSVのレイアウトまたは、文字コード、データ形式が正しくありません。";
    private const string BadDataErrorMessage = "CSVファイルではありません。";
    private const string LayoutErrorMessage = "CSVのレイアウトが正しくありません。";
    private readonly IUnitOfWork _unitOfWork;
    private readonly IAddBusinessUnderstandingApproachFromCsvMapper _mapper;
    private readonly IMessageSender<FindCustomerByCifsQuery, IEnumerable<FindCustomerByCifsResult>> _findCustomerByCifsSender;
    private readonly ICurrentDateTimeService _currentDateTimeService;

    public AddBusinessUnderstandingApproachFromCsvHandler(
        IUnitOfWork unitOfWork,
        IAddBusinessUnderstandingApproachFromCsvMapper mapper,
        IMessageSender<FindCustomerByCifsQuery, IEnumerable<FindCustomerByCifsResult>> findCustomerByCifsSender,
        ICurrentDateTimeService currentDateTimeService
        )
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _findCustomerByCifsSender = findCustomerByCifsSender ?? throw new ArgumentNullException(nameof(findCustomerByCifsSender));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
    }

    public async Task<Result> Handle(AddBusinessUnderstandingApproachFromCsvCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var now = _currentDateTimeService.NowDateTimeOffset();

        var csvRecords = Enumerable.Empty<AddBusinessUnderstandingApproachFromCsvDto>();

        try
        {
            csvRecords = _mapper.Map(request.CsvFile);
        }
        catch (BadDataException)
        {
            return Result.Error(new ValidationException(new ValidationExceptionItem(BadDataErrorMessage, "Messages")));
        }
        catch (HeaderValidationException ex)
        {
            return Result.Error(new ValidationException(new ValidationExceptionItem(LayoutErrorMessage, "Messages")));
        }
        catch (CsvHelper.MissingFieldException)
        {
            return Result.Error(new ValidationException(new ValidationExceptionItem(LayoutErrorMessage, "Messages")));
        }
        catch (Exception)
        {
            return Result.Error(new ValidationException(new ValidationExceptionItem(FormatErrorMessage, "Messages")));
        }

        // セグメント確認
        var approachTypes = csvRecords.Select(x => x.ApproachType).Distinct();
        var incorrectApproachTypeList = approachTypes.Where(
            x => x != BusinessUnderstandingApproachType.Necessary &&
            x != BusinessUnderstandingApproachType.HasIssue &&
            x != BusinessUnderstandingApproachType.PartlyUnderstanding
        );
        if (incorrectApproachTypeList.Any())
        {
            return Result.Error(new ValidationException(incorrectApproachTypeList.Select(x => new ValidationExceptionItem($"存在しないセグメントです(値：{x})", "Messages")).ToArray()));
        }

        var cifs = csvRecords.Select(x => new Cif { BranchNumber = x.BranchNumber, CifNumber = x.CifNumber });
        var customerList = new List<FindCustomerByCifsResult>();
        // 5000件ずつAPIを叩く
        foreach (var cifsChunk in cifs.Chunk(5000))
        {
            var findCustomersQuery = new FindCustomerByCifsQuery() { Cifs = cifsChunk };
            var findCustomerResult = await _findCustomerByCifsSender.SendAsync(findCustomersQuery);
            if (findCustomerResult.IsError) return findCustomerResult.PreserveErrorAs();

            var customers = findCustomerResult.Get();
            customerList.AddRange(customers);
        }

        // 顧客の存在チェック
        var notFoundCustomers = new List<(string branchNumber, string cifNumber)>();
        foreach (var record in csvRecords)
        {

            if (!customerList.Any(x => x.BranchNumber == record.BranchNumber && x.CifNumber == record.CifNumber))
            {
                notFoundCustomers.Add((record.BranchNumber, record.CifNumber));
            }
        }

        if (notFoundCustomers.Any())
        {
            return Result.Error(new ValidationException(notFoundCustomers.Select(x => new ValidationExceptionItem($"店番: {x.branchNumber} CIF番号: {x.cifNumber} は存在しないCIFです。", "Messages")).ToArray()));
        }

        // 事業性理解を取得
        var bizUnderstandingRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding>();
        var getBizUnderstandingResult = await bizUnderstandingRepository.FindAsync(
            new FindBusinessUnderstandingSpecification(customerList.Select(x => x.Id))
            ).ConfigureAwait(false);
        if (getBizUnderstandingResult.IsError) return getBizUnderstandingResult.PreserveErrorAs();
        var bizUnderstandings = getBizUnderstandingResult.Get();

        // 取組方を取得
        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingApproach>();
        var currentBusinessUnderstandingApproachesResult = await repository.AllAsync();
        if (currentBusinessUnderstandingApproachesResult.IsError) return currentBusinessUnderstandingApproachesResult.PreserveErrorAs();
        var currentBusinessUnderstandingApproaches = currentBusinessUnderstandingApproachesResult.Get();

        var historyRepository = _unitOfWork.GetRepository<BusinessUnderstandingHistory>();
        var queueRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingMaterializedViewQueue>();
        foreach (var csvRecord in csvRecords)
        {
            // 必ず存在する(上記でチェック済み)
            var customerId = customerList.Find(x => x.BranchNumber == csvRecord.BranchNumber && x.CifNumber == csvRecord.CifNumber)!.Id;

            // 事業性理解が作成されていない場合
            var bizUnderstanding = bizUnderstandings.FirstOrDefault(x => x.BusinessCustomer.CustomerIdentificationId == customerId);
            if (bizUnderstanding is null)
            {
                return Result.Error(new ValidationException(new ValidationExceptionItem($"店番: {csvRecord.BranchNumber} CIF番号: {csvRecord.CifNumber} の顧客の事業性理解が作成されていません。", "Messages")));
            }

            var currentData = currentBusinessUnderstandingApproaches.FirstOrDefault(x => x.CustomerIdentificationId == customerId);
            if (currentData is not null)
            {
                // 取引方針に変更なしならば何もしない(履歴も追加しない)
                if (currentData.ApproachType == csvRecord.ApproachType) continue;

                currentData.ApproachType = csvRecord.ApproachType;
                await repository.UpdateAsync(currentData);
            }
            else
            {
                currentData = new Domain.Entities.BusinessUnderstandingApproach
                {
                    // キーの値をUlidで生成します。
                    Id = Ulid.NewUlid().ToString(),
                    CustomerIdentificationId = customerId,
                    ApproachType = csvRecord.ApproachType
                };
                await repository.AddAsync(currentData);
            }

            var history = (bizUnderstanding.Histories.Any()) ? bizUnderstanding.Histories.OrderByDescending(x => x.UpdatedDateTime).First() : null;

            // 履歴の追加
            var newHistory = new BusinessUnderstandingHistory()
            {
                Id = Ulid.NewUlid().ToString(),
                // 取引方針
                TransactionPolicy = history is not null ? history.TransactionPolicy : string.Empty,
                // 事業性理解評点
                BusinessEvaluation = history is not null ? history.BusinessEvaluation : 0,
                ManagementPlanScore = history is not null ? history.ManagementPlanScore : 0,
                ManagementScore = history is not null ? history.ManagementScore : 0,
                FiveForceScore = history is not null ? history.FiveForceScore : 0,
                FiveStepScore = history is not null ? history.FiveStepScore : 0,
                ExternalEnvironmentScore = history is not null ? history.ExternalEnvironmentScore : 0,
                ESGAndSDGsScore = history is not null ? history.ESGAndSDGsScore : 0,
                // リレーションレベル評点
                RelationLevelEvaluation = history is not null ? history.RelationLevelEvaluation : 0,
                AuthorityScore = history is not null ? history.AuthorityScore : 0,
                RelationScore = history is not null ? history.RelationScore : 0,
                DisclosureScore = history is not null ? history.DisclosureScore : 0,
                // 事業整理解の取り組み方
                ApproachType = currentData!.ApproachType,
                // 更新日時等
                UpdatedDateTime = now,
                UpdaterDisplayName = "System",
                UpdaterId = "System",
                OriginalId = bizUnderstanding.Id,
            };
            await historyRepository.AddAsync(newHistory);

            // キューの登録
            var queue = new Domain.Entities.BusinessUnderstandingMaterializedViewQueue()
            {
                Id = Ulid.NewUlid().ToString(),
                BusinessUnderstandingId = bizUnderstanding.Id,
                CustomerIdentificationId = customerId,
                NumberOfRetries = 0,
                UpdaterId = "System",
                UpdaterName = "System",
                UpdatedDateTime = now,
            };
            await queueRepository.AddAsync(queue);
        }

        return await _unitOfWork.SaveEntitiesAsync() // データを保存します。
            .FlatMap(() => Result.Ok()) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
