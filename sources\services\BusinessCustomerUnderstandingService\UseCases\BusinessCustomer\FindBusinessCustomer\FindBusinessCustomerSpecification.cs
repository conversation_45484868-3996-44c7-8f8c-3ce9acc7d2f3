using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessCustomer.FindBusinessCustomer;

public class FindBusinessCustomerSpecification : BaseSpecification<Domain.Entities.BusinessCustomer>
{
    public FindBusinessCustomerSpecification(FindBusinessCustomerQuery request)
    {
        Query
            .WhereIf(request.CustomerIdentificationId is not null, e => e.CustomerIdentificationId == request.CustomerIdentificationId)
            .AsNoTracking();
    }
}
