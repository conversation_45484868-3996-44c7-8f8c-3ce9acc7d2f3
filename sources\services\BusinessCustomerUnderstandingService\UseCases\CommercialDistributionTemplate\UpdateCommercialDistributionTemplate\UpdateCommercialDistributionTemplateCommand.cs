using BusinessCustomerUnderstandingService.Domain.Enums;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistributionTemplate.UpdateCommercialDistributionTemplate;

[WithDefaultBehaviors]
public record UpdateCommercialDistributionTemplateCommand(
    string Id,
    string TemplateName,
    string CanvasColor,
    string Version
) : IRequest<Result<string>>
{
    public List<UpdateCommercialDistributionTemplateNodesCommand> Nodes { get; init; } = new();
    public List<UpdateCommercialDistributionTemplateEdgesCommand> Edges { get; init; } = new();
}

public record UpdateCommercialDistributionTemplateNodesCommand(
    // 追加・更新・(削除)の判定に使用する項目
    string Id,
    bool Updated,

    // 更新項目
    CommercialDistributionNodeType NodeType,
    double Left,
    double Top,
    double Width,
    double Height,
    string? Title,
    string? Text,
    string? GroupingId,
    string Color,
    string? CustomerName,
    PersonType? PersonType,
    string? BranchNumber,
    string? CifNumber,
    string? Merchandise,
    string? Material,
    double? Amount,
    string? Industry,
    string? Area,
    bool? IsInternationalBusinessPartners,
    string? CountryCode,
    string? CityName,
    string? Note
);

public record UpdateCommercialDistributionTemplateEdgesCommand(
    // 追加・更新・(削除)の判定に使用する項目
    string? Id,
    bool Updated,

    // 更新項目
    string SourceNode,
    string Source,
    string TargetNode,
    string Target
);
