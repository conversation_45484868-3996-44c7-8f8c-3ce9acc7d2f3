using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class FamilyTreeEdgeHistoryConfiguration : IEntityTypeConfiguration<FamilyTreeEdgeHistory>
{
    public void Configure(EntityTypeBuilder<FamilyTreeEdgeHistory> builder)
    {
        builder.HasOne<FamilyTreeHistory>()
            .WithMany(c => c.FamilyTreeEdgeHistories)
            .HasForeignKey(e => e.FamilyTreeHistoryId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(e => e.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(e => e.FamilyTreeEdgeId)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(e => e.SourceNode)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(e => e.Source)
            .IsRequired()
            .HasMaxLength(32);

        builder.Property(e => e.TargetNode)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(e => e.Target)
            .IsRequired()
            .HasMaxLength(32);
    }
}
