using CustomerProposalService.Domain.Enums;

namespace CustomerProposalService.UseCases.Case.GetOtherLoanCase;

public record GetOtherLoanCaseResult(
    // Case Entity
    string Id,
    Guid CustomerIdentificationId,
    CaseCategory CaseCategory,
    string CaseName,
    CaseStatus CaseStatus,
    string? CaseOutline,
    DateTimeOffset? ExpiredAt,
    string StaffId,
    string StaffName,
    DateTimeOffset RegisteredAt,
    DateTimeOffset CaseUpdatedAt,
    DateTimeOffset LastUpdatedAt,
    IEnumerable<Domain.Entities.CaseFile>? CaseFiles,
    IEnumerable<Domain.Entities.CaseLink>? CaseLinks,
    string Version,
    // OtherLoanCase Entity
    bool? PreConsultationStandardTarget,
    bool IsEarthquakeRelated
);
