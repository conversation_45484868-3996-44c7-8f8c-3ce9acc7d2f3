using CustomerProposalService.Domain.Enums;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionThreadReaction.AddCaseDiscussionThreadReaction;

[WithDefaultBehaviors]
public record AddCaseDiscussionThreadReactionCommand(
    string CaseDiscussionThreadId,
    string UpdaterId,
    string UpdaterName,
    ReactionType ReactionType,
    DateTimeOffset UpdatedDateTime
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
