using Shared.Spec;

namespace CustomerProposalService.UseCases.CaseDiscussionReply.GetCaseDiscussionReply;

public class GetCaseDiscussionReplySpecification : BaseSpecification<Domain.Entities.CaseDiscussionReply>
{
    public GetCaseDiscussionReplySpecification(GetCaseDiscussionReplyQuery request)
    {
        Query
            .Where(x => x.Id == request.Id)
            .Include(x => x.Files)
            .AsNoTracking();
    }
}
