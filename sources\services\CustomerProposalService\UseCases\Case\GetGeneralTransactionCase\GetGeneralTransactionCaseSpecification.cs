using Shared.Spec;

namespace CustomerProposalService.UseCases.Case.GetGeneralTransactionCase;

public class GetGeneralTransactionCaseSpecification : BaseSpecification<Domain.Entities.GeneralTransactionCase>
{
    public GetGeneralTransactionCaseSpecification(GetGeneralTransactionCaseQuery request)
    {
        Query
            .Where(x => x.Id == request.Id)
            .Include(x => x.CaseUpdateInformation)
            .Include(x => x.CaseFiles)
            .Include(x => x.CaseLinks)
            .AsNoTracking();
    }
}
