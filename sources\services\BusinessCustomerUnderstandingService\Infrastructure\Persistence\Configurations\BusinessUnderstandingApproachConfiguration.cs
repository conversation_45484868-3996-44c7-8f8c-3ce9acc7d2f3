using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class BusinessUnderstandingApproachConfiguration : IEntityTypeConfiguration<BusinessUnderstandingApproach>
{
    public void Configure(EntityTypeBuilder<BusinessUnderstandingApproach> builder)
    {
        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.CustomerIdentificationId)
            .IsRequired()
            .HasMaxLength(36);
    }
}
