using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.GetLeaseCaseDiscussionThread;

public class GetLeaseCaseDiscussionThreadHandler : IRequestHandler<GetLeaseCaseDiscussionThreadQuery, Result<GetLeaseCaseDiscussionThreadResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetLeaseCaseDiscussionThreadHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetLeaseCaseDiscussionThreadResult>> Handle(GetLeaseCaseDiscussionThreadQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.LeaseCaseDiscussionThread, string>();
        return repository.SingleAsync(new GetLeaseCaseDiscussionThreadSpecification(request))
            .Map(v =>
                new GetLeaseCaseDiscussionThreadResult(
                    v.Id,
                    v.RegisteredAt,
                    v.ThreadName,
                    v.RegistrantId,
                    v.RegistrantName,
                    v.Purpose,
                    v.Person,
                    v.IsPersonOfPower,
                    v.MentionTargetsHtml,
                    v.MentionTargetUserIds,
                    v.MentionTargetTeamIds,
                    v.Description,
                    v.Files,
                    v.ThreadNameTypes,
                    v.ThreadNameForOther,
                    v.Version));
    }
}
