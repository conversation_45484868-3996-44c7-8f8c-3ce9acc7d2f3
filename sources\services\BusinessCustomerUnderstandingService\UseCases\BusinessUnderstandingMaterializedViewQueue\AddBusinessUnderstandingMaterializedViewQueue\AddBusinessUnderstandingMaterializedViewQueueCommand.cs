using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingMaterializedViewQueue.AddBusinessUnderstandingMaterializedViewQueue;

[WithDefaultBehaviors]
public record AddBusinessUnderstandingMaterializedViewQueueCommand(
    IEnumerable<Guid> CustomerIdentificationIds,
    string UpdaterId,
    string UpdaterName) : IRequest<Result<AddBusinessUnderstandingMaterializedViewQueueResult>>
{
}
