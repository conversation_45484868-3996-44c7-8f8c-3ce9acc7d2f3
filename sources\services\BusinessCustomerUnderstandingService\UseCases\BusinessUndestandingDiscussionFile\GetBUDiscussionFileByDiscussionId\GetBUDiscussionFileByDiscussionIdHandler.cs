using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussionFile.GetBUDiscussionFileByDiscussionId;

public class GetBUDiscussionFileByDiscussionIdHandler : IRequestHandler<GetBUDiscussionFileByDiscussionIdQuery, Result<List<GetBUDiscussionFileByDiscussionIdResult>>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetBUDiscussionFileByDiscussionIdHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<List<GetBUDiscussionFileByDiscussionIdResult>>> Handle(GetBUDiscussionFileByDiscussionIdQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingDiscussionFile, string>();

        var result = repository.FindAsync(new GetBUDiscussionFileByDiscussionIdSpecification(request.DiscussionId));

        return result.Map(val => val.Select(v => new GetBUDiscussionFileByDiscussionIdResult(
                v.Id,
                v.FileName,
                v.UpdatedDateTime,
                v.UpdaterId,
                v.UpdaterName,
                v.DiscussionId,
                v.Version
                )).ToList());
    }
}
