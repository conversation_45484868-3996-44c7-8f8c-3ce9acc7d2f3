using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThreadFile.UploadBUThreadFile;

public class UploadBUThreadFileValidator : AbstractValidator<UploadBUThreadFileCommand>
{
    public UploadBUThreadFileValidator()
    {
        RuleFor(v => v.ThreadId).NotEmpty();
        RuleFor(v => v.UpdaterId).NotEmpty();
        RuleFor(v => v.UpdaterName).NotEmpty();
    }
}
