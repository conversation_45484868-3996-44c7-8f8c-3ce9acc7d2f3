using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThread.DeleteBusinessUnderstandingThread;

public class DeleteBusinessUnderstandingThreadValidator : AbstractValidator<DeleteBusinessUnderstandingThreadCommand>
{
    public DeleteBusinessUnderstandingThreadValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.Version).NotEmpty();
    }
}
