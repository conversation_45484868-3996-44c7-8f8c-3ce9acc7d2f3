using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;

public class CustomerIdeasUnderstandingThreadFileConfiguration : IEntityTypeConfiguration<CustomerIdeasUnderstandingThreadFile>
{
    public void Configure(EntityTypeBuilder<CustomerIdeasUnderstandingThreadFile> builder)
    {
        builder.HasOne<CustomerIdeasUnderstandingThread>()
            .WithMany(l => l.Files)
            .HasForeignKey(j => j.ThreadId)
            .HasConstraintName("fk_customer_ideas_understanding_thread_customer_ideas_understanding_thread_file_id")
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.FileName)
            .IsRequired()
            .HasMax<PERSON>ength(300);
    }
}
