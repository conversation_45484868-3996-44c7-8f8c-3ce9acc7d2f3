using BusinessCustomerUnderstandingService.Services.Queries;
using MediatR;
using Nut.Results;
using Shared.Application;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.FindSpecificCustomerCommunicationPlan;

public class FindSpecificCustomerCommunicationPlanHandler : IRequestHandler<FindSpecificCustomerCommunicationPlanQuery, Result<PaginatedResult<FindSpecificCustomerCommunicationPlanResult>>>
{
    private readonly IFindSpecificCustomerCommunicationPlanQueryService _queryService;

    public FindSpecificCustomerCommunicationPlanHandler(IFindSpecificCustomerCommunicationPlanQueryService queryService)
    {
        _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
    }

    public Task<Result<PaginatedResult<FindSpecificCustomerCommunicationPlanResult>>> Handle(FindSpecificCustomerCommunicationPlanQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));
        // 検索を行います。
        var result = _queryService.Handle(
            request.BusinessUnderstandingId,
            request.Title,
            request.StaffName,
            request.Types,
            request.Statuses,
            request.ExpiredAtFrom,
            request.ExpiredAtTo,
            request.ExtractSafePagenationOption(sort: new List<Sort>()
            {
                new Sort() { Target = nameof(Domain.Dto.BusinessUnderstanding.CommunicationPlanBasicInfo.Type) },
                new Sort() { Target = nameof(Domain.Dto.BusinessUnderstanding.CommunicationPlanBasicInfo.ExpiredAt) },
            }));
        // 検索結果を FindCommunicationPlanResult にマップして返します。
        return result.Map(pr =>
            pr.Map(v => new FindSpecificCustomerCommunicationPlanResult(v.Id, v.Status, v.Title, v.StaffName, v.Type, v.ExpiredAt)));
    }
}
