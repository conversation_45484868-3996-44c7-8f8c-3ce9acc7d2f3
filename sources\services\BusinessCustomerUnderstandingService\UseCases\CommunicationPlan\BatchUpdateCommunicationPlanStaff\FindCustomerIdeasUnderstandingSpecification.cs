using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.BatchUpdateCommunicationPlanStaff;

public class FindCustomerIdeasUnderstandingSpecification : BaseSpecification<Domain.Entities.CustomerIdeasUnderstanding>
{
    public FindCustomerIdeasUnderstandingSpecification(string currentStaffId, List<string> businessUnderstandingIdList)
    {
        Query
            .Where(x => x.StaffId == currentStaffId)
            .Where(x => businessUnderstandingIdList.Contains(x.BusinessUnderstandingId))
            .Where(x => x.Status != Domain.Enums.Status.Completed)
            .AsNoTracking();
    }
}
