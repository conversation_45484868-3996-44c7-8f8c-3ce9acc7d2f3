using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.BatchUpdateCommunicationPlanStaff;

public class FindBusinessUnderstandingsByBusinessCustomersSpecification : BaseSpecification<Domain.Entities.BusinessUnderstanding>
{
    public FindBusinessUnderstandingsByBusinessCustomersSpecification(List<string> businessCustomerIdList)
    {
        Query
            .Where(x => businessCustomerIdList.Contains(x.BusinessCustomerId))
            .AsNoTracking();
    }
}
