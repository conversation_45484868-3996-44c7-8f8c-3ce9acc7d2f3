using BusinessCustomerUnderstandingService.Domain.Enums;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessUnderstandingWithHistory;

public record GetBusinessUnderstandingWithHistoryResult(
    string TransactionPolicy,
    BusinessUnderstandingApproachType? ApproachType,
    int BusinessEvaluation,
    int ManagementPlanScore,
    int ManagementScore,
    int FiveForceScore,
    int FiveStepScore,
    int ExternalEnvironmentScore,
    int ESGAndSDGsScore,
    double RelationLevelEvaluation,
    double AuthorityScore,
    double RelationScore,
    double DisclosureScore,
    DateTimeOffset? UpdatedDateTime,
    string? UpdaterDisplayName,
    string? UpdaterId,
    List<Domain.Entities.BusinessUnderstandingHistory> Histories
);
