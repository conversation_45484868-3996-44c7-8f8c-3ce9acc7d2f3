using BusinessCustomerUnderstandingService.Domain.Enums;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Services.Queries;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;

internal class GetCommunicationPlanCountQueryService : IGetCommunicationPlanCountQueryService
{
    private readonly ApplicationDbContext _dbContext;

    public GetCommunicationPlanCountQueryService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    async Task<Result<int>> IGetCommunicationPlanCountQueryService.Handle(string businessUnderstandingId)
    {
        var ourPolicyUnderstandings = _dbContext.OurPolicyUnderstandings
            .Where(o => businessUnderstandingId == o.BusinessUnderstandingId && o.Status != Status.Completed).ToList();

        var customerIdeasUnderstandings = _dbContext.CustomerIdeasUnderstandings
            .Where(o => businessUnderstandingId == o.BusinessUnderstandingId && o.Status != Status.Completed).ToList();

        var sharingOfFinances = _dbContext.SharingOfFinances
            .Where(o => businessUnderstandingId == o.BusinessUnderstandingId && o.Status != Status.Completed).ToList();

        var hypotheticalDiscussionOfIssues = _dbContext.HypotheticalDiscussionOfIssues
            .Where(o => businessUnderstandingId == o.BusinessUnderstandingId && o.Status != Status.Completed).ToList();

        var toDo = _dbContext.ToDo
            .Where(o => businessUnderstandingId == o.BusinessUnderstandingId && o.Status != Status.Completed).ToList();

        var total = ourPolicyUnderstandings.Count + customerIdeasUnderstandings.Count + sharingOfFinances.Count + hypotheticalDiscussionOfIssues.Count + toDo.Count;

        return total;
    }
}
