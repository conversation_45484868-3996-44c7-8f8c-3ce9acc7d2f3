using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Domain.Entities;
using MediatR;
using Nut.Results;
using Shared.Domain;
using Shared.Services;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.UpdateTransactionPolicyConfirmer;

public class UpdateTransactionPolicyConfirmerHandler : IRequestHandler<UpdateTransactionPolicyConfirmerCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDateTimeService;

    public UpdateTransactionPolicyConfirmerHandler(IUnitOfWork unitOfWork, ICurrentDateTimeService currentDateTimeService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
    }

    public async Task<Result<string>> Handle(UpdateTransactionPolicyConfirmerCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var now = _currentDateTimeService.NowDateTimeOffset();

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding, string>();

        // 既存データを取得します。
        var getResult = await repository.SingleAsync(
            new FindByIdSpecification<Domain.Entities.BusinessUnderstanding, string>(request.Id)
            .Include(x => x.BusinessCustomer)
            ).ConfigureAwait(false);
        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        // データ更新
        var currentData = getResult.Get();
        currentData.TransactionPolicyConfirmerId = request.TransactionPolicyConfirmerId;
        currentData.TransactionPolicyConfirmedDateTime = now;
        currentData.Version = request.Version;

        // ToDo:BusinessUnderstandingMaterializedViewQueueの本番適用後に削除
        currentData.UpdaterId = request.UpdaterId;
        currentData.UpdaterName = request.UpdaterName;

        // キューの登録
        var queue = new Domain.Entities.BusinessUnderstandingMaterializedViewQueue()
        {
            Id = Ulid.NewUlid().ToString(),
            BusinessUnderstandingId = currentData.Id,
            CustomerIdentificationId = currentData.BusinessCustomer.CustomerIdentificationId,
            NumberOfRetries = 0,
            UpdaterId = request.UpdaterId!,
            UpdaterName = request.UpdaterName!,
            UpdatedDateTime = now
        };
        var queueRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingMaterializedViewQueue>();
        await queueRepository.AddAsync(queue).ConfigureAwait(false);

        var saveResult = await _unitOfWork.SaveEntitiesAsync();
        if (saveResult.IsError) return saveResult.PreserveErrorAs<string>();

        return Result.Ok(currentData.Id);
    }
}
