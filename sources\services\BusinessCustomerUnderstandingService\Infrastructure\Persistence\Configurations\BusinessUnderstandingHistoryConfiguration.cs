using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class BusinessUnderstandingHistoryConfiguration : IEntityTypeConfiguration<BusinessUnderstandingHistory>
{
    public void Configure(EntityTypeBuilder<BusinessUnderstandingHistory> builder)
    {
        builder.HasOne<BusinessUnderstanding>()
            .WithMany(entity => entity.Histories)
            .HasForeignKey(entity => entity.OriginalId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(entity => entity.Id)
            .HasMaxLength(26);

        builder.Property(entity => entity.OriginalId)
            .HasMaxLength(26);
    }
}
