using Nut.Results;

namespace BusinessCustomerUnderstandingService.Services.Domain;

public interface IQuillImageService
{
    Task<Result<string>> ArrangeContentByUrlWhenSaveAsync(string containerName, string imageAddressPrefix, string delta, string id);
    Task<Result<string>> GetArrangedDeltaWhenPublish(string delta);
    Task<Result<string>> DeleteAllContentsAsync(string containerName, string imageAddressPrefix, string id);
}
