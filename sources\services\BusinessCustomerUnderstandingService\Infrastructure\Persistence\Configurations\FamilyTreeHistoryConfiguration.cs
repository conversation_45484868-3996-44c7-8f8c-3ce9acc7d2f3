using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class FamilyTreeHistoryConfiguration : IEntityTypeConfiguration<FamilyTreeHistory>
{
    public void Configure(EntityTypeBuilder<FamilyTreeHistory> builder)
    {
        builder.HasOne<FamilyTree>()
            .WithMany(entity => entity.Histories)
            .HasForeignKey(c => c.OriginalId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(c => c.Id)
            .IsRequired()
            .HasMaxLength(26);
    }
}
