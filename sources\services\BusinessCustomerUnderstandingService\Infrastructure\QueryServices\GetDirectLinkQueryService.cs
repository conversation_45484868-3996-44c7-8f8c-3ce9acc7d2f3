using BusinessCustomerUnderstandingService.Domain.Enums;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Services.Queries;
using Microsoft.EntityFrameworkCore;
using Nut.Results;
using Shared.Results.Errors;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;

internal class GetDirectLinkQueryService : IGetDirectLinkQueryService
{
    private readonly ApplicationDbContext _dbContext;

    public GetDirectLinkQueryService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    public Task<Result<string>> Handle(InteractionCategoryGroup interactionCategoryGroup, string id)
    {
        return Task.FromResult(interactionCategoryGroup switch
        {
            InteractionCategoryGroup.BusinessUnderstandingAndRelationLevel
                or InteractionCategoryGroup.BusinessModel => GetBusinessUnderstandingDirectLink(id),
            InteractionCategoryGroup.BusinessUnderstandingInternalDiscussion
                or InteractionCategoryGroup.BusinessUnderstandingExternalDiscussion => GetBusinessUnderstandingDiscussionDirectLink(id),
            InteractionCategoryGroup.OurPolicyUnderstanding
                or InteractionCategoryGroup.OurPolicyUnderstandingInternalDiscussion
                or InteractionCategoryGroup.OurPolicyUnderstandingExternalDiscussion => GetOPUTaskAndDiscussionDirectLink(interactionCategoryGroup, id),
            InteractionCategoryGroup.CustomerIdeasUnderstanding
                or InteractionCategoryGroup.CustomerIdeasUnderstandingInternalDiscussion
                or InteractionCategoryGroup.CustomerIdeasUnderstandingExternalDiscussion => GetCIUTaskAndDiscussionDirectLink(interactionCategoryGroup, id),
            InteractionCategoryGroup.SharingOfFinance
                or InteractionCategoryGroup.SharingOfFinanceInternalDiscussion
                or InteractionCategoryGroup.SharingOfFinanceExternalDiscussion => GetSOFTaskAndDiscussionDirectLink(interactionCategoryGroup, id),
            InteractionCategoryGroup.HypotheticalDiscussionOfIssue
                or InteractionCategoryGroup.HypotheticalDiscussionOfIssueInternalDiscussion
                or InteractionCategoryGroup.HypotheticalDiscussionOfIssueExternalDiscussion => GetHDOITaskAndDiscussionDirectLink(interactionCategoryGroup, id),
            InteractionCategoryGroup.ToDo
                or InteractionCategoryGroup.ToDoInternalDiscussion
                or InteractionCategoryGroup.ToDoExternalDiscussion => GetTodoTaskAndDiscussionDirectLink(interactionCategoryGroup, id),
            _ => Result.Error<string>(new Exception("想定されていない種類のデータへのリンクが要求されました"))
        });
    }

    // 事業性理解
    private Result<string> GetBusinessUnderstandingDirectLink(string id)
    {
        var getDataResult = _dbContext.BusinessCustomers
            .Where(x => x.CustomerIdentificationId.ToString() == id)
            .AsNoTracking()
            .FirstOrDefault();

        if (getDataResult is null) return Result.Error<string>(new DataNotFoundException());

        return Result.Ok($"/business-understanding?customerIdentificationId={getDataResult.CustomerIdentificationId}");
    }

    // 事業性理解の社内外協議
    private Result<string> GetBusinessUnderstandingDiscussionDirectLink(string id)
    {
        var getDataResult = _dbContext.BusinessUnderstandingThreads
            .Where(x => x.Id == id)
            .AsNoTracking()
            .FirstOrDefault();

        if (getDataResult is null) return Result.Error<string>(new DataNotFoundException());

        return Result.Ok($"/business-understanding/comments?businessUnderstandingId={getDataResult.BusinessUnderstandingId}&threadId={id}");
    }

    // ##### コミュニケーションプランのタスクと協議 #####
    // OurPolicyUnderstanding
    private Result<string> GetOPUTaskAndDiscussionDirectLink(InteractionCategoryGroup interactionCategoryGroup, string id)
    {
        var query = _dbContext.OurPolicyUnderstandings.Include(x => x.Threads).AsNoTracking();

        switch (interactionCategoryGroup)
        {
            case InteractionCategoryGroup.OurPolicyUnderstanding:
                query = query.Where(task => task.Id == id).AsNoTracking();
                break;
            case InteractionCategoryGroup.OurPolicyUnderstandingInternalDiscussion:
            case InteractionCategoryGroup.OurPolicyUnderstandingExternalDiscussion:
                query = query.Join(
                        _dbContext.OurPolicyUnderstandingThreads.Where(x => x.Id == id),
                        original => original.Id,
                        thread => thread.OurPolicyUnderstandingId,
                        (original, thread) => original
                    ).AsNoTracking();
                break;
        }

        var getDataResult = query.FirstOrDefault();
        if (getDataResult is null) return Result.Error<string>(new DataNotFoundException());

        var baseUrl = $"/business-understanding/communication-plan?businessUnderstandingId={getDataResult.BusinessUnderstandingId}&communicationPlanCategory=当社の方針・施策をご理解いただく&taskId={getDataResult.Id}";

        return interactionCategoryGroup switch
        {
            InteractionCategoryGroup.OurPolicyUnderstanding => Result.Ok(baseUrl),
            InteractionCategoryGroup.OurPolicyUnderstandingInternalDiscussion
                or InteractionCategoryGroup.OurPolicyUnderstandingExternalDiscussion => Result.Ok($"{baseUrl}&threadId={id}"),
            _ => Result.Error<string>(new Exception())
        };
    }

    // CustomerIdeasUnderstanding
    private Result<string> GetCIUTaskAndDiscussionDirectLink(InteractionCategoryGroup interactionCategoryGroup, string id)
    {
        var query = _dbContext.CustomerIdeasUnderstandings.Include(x => x.Threads!).ThenInclude(x => x.Comments).AsNoTracking();

        var threadId = "";
        switch (interactionCategoryGroup)
        {
            case InteractionCategoryGroup.CustomerIdeasUnderstanding:
                query = query.Where(task => task.Id == id).AsNoTracking();
                break;
            case InteractionCategoryGroup.CustomerIdeasUnderstandingInternalDiscussion:
            case InteractionCategoryGroup.CustomerIdeasUnderstandingExternalDiscussion:
                query = query.Join(
                        _dbContext.CustomerIdeasUnderstandingThreads.Where(x => x.Id == id),
                        original => original.Id,
                        thread => thread.CustomerIdeasUnderstandingId,
                        (original, thread) => original
                    ).AsNoTracking();
                break;
        }

        var getDataResult = query.FirstOrDefault();
        if (getDataResult is null) return Result.Error<string>(new DataNotFoundException());

        var baseUrl = $"/business-understanding/communication-plan?businessUnderstandingId={getDataResult.BusinessUnderstandingId}&communicationPlanCategory=お客さまの考え方を理解する&taskId={getDataResult.Id}";

        return interactionCategoryGroup switch
        {
            InteractionCategoryGroup.CustomerIdeasUnderstanding => Result.Ok(baseUrl),
            InteractionCategoryGroup.CustomerIdeasUnderstandingInternalDiscussion
                or InteractionCategoryGroup.CustomerIdeasUnderstandingExternalDiscussion => Result.Ok($"{baseUrl}&threadId={id}"),
            _ => Result.Error<string>(new Exception())
        };
    }

    // SharingOfFinance
    private Result<string> GetSOFTaskAndDiscussionDirectLink(InteractionCategoryGroup interactionCategoryGroup, string id)
    {
        var query = _dbContext.SharingOfFinances.Include(x => x.Threads!).ThenInclude(x => x.Comments).AsNoTracking();

        switch (interactionCategoryGroup)
        {
            case InteractionCategoryGroup.SharingOfFinance:
                query = query.Where(task => task.Id == id).AsNoTracking();
                break;
            case InteractionCategoryGroup.SharingOfFinanceInternalDiscussion:
            case InteractionCategoryGroup.SharingOfFinanceExternalDiscussion:
                query = query.Join(
                        _dbContext.SharingOfFinanceThreads.Where(x => x.Id == id),
                        original => original.Id,
                        thread => thread.SharingOfFinanceId,
                        (original, thread) => original
                    ).AsNoTracking();
                break;
        }

        var getDataResult = query.FirstOrDefault();
        if (getDataResult is null) return Result.Error<string>(new DataNotFoundException());

        var baseUrl = $"/business-understanding/communication-plan?businessUnderstandingId={getDataResult.BusinessUnderstandingId}&communicationPlanCategory=財務の共有&taskId={getDataResult.Id}";

        return interactionCategoryGroup switch
        {
            InteractionCategoryGroup.SharingOfFinance => Result.Ok(baseUrl),
            InteractionCategoryGroup.SharingOfFinanceInternalDiscussion
                or InteractionCategoryGroup.SharingOfFinanceExternalDiscussion => Result.Ok($"{baseUrl}&threadId={id}"),
            _ => Result.Error<string>(new Exception())
        };
    }

    // HypotheticalDiscussionOfIssues
    private Result<string> GetHDOITaskAndDiscussionDirectLink(InteractionCategoryGroup interactionCategoryGroup, string id)
    {
        var query = _dbContext.HypotheticalDiscussionOfIssues.Include(x => x.Threads).ThenInclude(x => x.Comments).AsNoTracking();

        switch (interactionCategoryGroup)
        {
            case InteractionCategoryGroup.HypotheticalDiscussionOfIssue:
                query = query.Where(task => task.Id == id).AsNoTracking();
                break;
            case InteractionCategoryGroup.HypotheticalDiscussionOfIssueInternalDiscussion:
            case InteractionCategoryGroup.HypotheticalDiscussionOfIssueExternalDiscussion:
                query = query.Join(
                        _dbContext.HypotheticalDiscussionOfIssuesThreads.Where(x => x.Id == id),
                        original => original.Id,
                        thread => thread.HypotheticalDiscussionOfIssuesId,
                        (original, thread) => original
                    ).AsNoTracking();
                break;
        }

        var getDataResult = query.FirstOrDefault();
        if (getDataResult is null) return Result.Error<string>(new DataNotFoundException());

        var baseUrl = $"/business-understanding/communication-plan?businessUnderstandingId={getDataResult.BusinessUnderstandingId}&communicationPlanCategory=課題の仮説協議&taskId={getDataResult.Id}";

        return interactionCategoryGroup switch
        {
            InteractionCategoryGroup.HypotheticalDiscussionOfIssue => Result.Ok(baseUrl),
            InteractionCategoryGroup.HypotheticalDiscussionOfIssueInternalDiscussion
                or InteractionCategoryGroup.HypotheticalDiscussionOfIssueExternalDiscussion => Result.Ok($"{baseUrl}&threadId={id}"),
            _ => Result.Error<string>(new Exception())
        };
    }

    // Todo
    private Result<string> GetTodoTaskAndDiscussionDirectLink(InteractionCategoryGroup interactionCategoryGroup, string id)
    {
        var query = _dbContext.ToDo.Include(x => x.Threads!).ThenInclude(x => x.Comments).AsNoTracking();

        switch (interactionCategoryGroup)
        {
            case InteractionCategoryGroup.ToDo:
                query = query.Where(task => task.Id == id).AsNoTracking();
                break;
            case InteractionCategoryGroup.ToDoInternalDiscussion:
            case InteractionCategoryGroup.ToDoExternalDiscussion:
                query = query.Join(
                        _dbContext.ToDoThreads.Where(x => x.Id == id),
                        original => original.Id,
                        thread => thread.ToDoId,
                        (original, thread) => original
                    ).AsNoTracking();
                break;
        }

        var getDataResult = query.FirstOrDefault();
        if (getDataResult is null) return Result.Error<string>(new DataNotFoundException());

        var baseUrl = $"/business-understanding/communication-plan?businessUnderstandingId={getDataResult.BusinessUnderstandingId}&communicationPlanCategory=ToDo&taskId={getDataResult.Id}";

        return interactionCategoryGroup switch
        {
            InteractionCategoryGroup.ToDo => Result.Ok(baseUrl),
            InteractionCategoryGroup.ToDoInternalDiscussion
                or InteractionCategoryGroup.ToDoExternalDiscussion => Result.Ok($"{baseUrl}&threadId={id}"),
            _ => Result.Error<string>(new Exception())
        };
    }
}
