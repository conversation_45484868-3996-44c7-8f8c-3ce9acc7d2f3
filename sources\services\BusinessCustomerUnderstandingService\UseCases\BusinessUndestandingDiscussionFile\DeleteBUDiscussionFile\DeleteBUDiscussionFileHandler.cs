using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Infrastructure.Storage;
using BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussionFile.GetBUDiscussionFileByDiscussionId;
using MediatR;
using Nut.Results;
using Shared.ObjectStorage;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUndestandingDiscussionFile.DeleteBUDiscussionFile;

public class DeleteBUDiscussionFileHandler : IRequestHandler<DeleteBUDiscussionFileCommand, Result<string>>
{
    private readonly IBusinessUnderstandingStorageClientProvider _objectStorageClientProvider;
    private readonly IUnitOfWork _unitOfWork;

    private readonly string _folderName = "discussion";

    public DeleteBUDiscussionFileHandler(
        IBusinessUnderstandingStorageClientProvider objectStorageClientProvider,
        IUnitOfWork unitOfWork)
    {
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<string>> Handle(DeleteBUDiscussionFileCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // DBから添付ファイル情報を取得
        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingDiscussionFile>();
        var spec = new GetBUDiscussionFileByDiscussionIdSpecification(request.DiscussionId);
        var result = await repository.FindAsync(spec);
        var currentFiles = result.Get();

        // ファイル情報を削除
        foreach (var file in currentFiles)
            await repository.DeleteAsync(file).ConfigureAwait(false);
        // 保存
        await _unitOfWork.SaveEntitiesAsync().ConfigureAwait(false);

        // Blobからファイルを取得
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);
        var objects = await storageClient.GetObjectInformationsAsync($"{_folderName}/{request.DiscussionId}").ConfigureAwait(false);
        if (objects.IsError) return objects.PreserveErrorAs<string>();
        var files = objects.Get();

        // 取得したファイルをBlobから削除
        foreach (var file in files)
        {
            var fileName = Path.GetFileName(file.Name);
            var deleteFileResult = await storageClient.DeleteAsync($"{_folderName}/{request.DiscussionId}/{fileName}").ConfigureAwait(false);
            if (deleteFileResult.IsError) return deleteFileResult.PreserveErrorAs<string>();
        }

        return Result.Ok(request.DiscussionId);
    }

}
