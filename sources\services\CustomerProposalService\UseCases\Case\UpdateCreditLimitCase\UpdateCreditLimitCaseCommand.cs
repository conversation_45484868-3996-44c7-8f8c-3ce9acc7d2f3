using CustomerProposalService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.UpdateCreditLimitCase;

[WithDefaultBehaviors]
public record UpdateCreditLimitCaseCommand(
    // Case Entity
    string Id,
    string CaseName,
    CaseStatus CaseStatus,
    string? CaseOutline,
    DateTimeOffset? ExpiredAt,
    string StaffId,
    string StaffName,
    IEnumerable<IFormFile>? UploadFiles,
    IEnumerable<string>? CaseLinks,
    // CreditLimitCase Entity
    string? AccountType,
    decimal? Amount,
    int? Period,
    string? ApplicableBaseInterestRate,
    decimal? InterestRate,
    string? InterestRateCustom,
    decimal? BaseInterestRate,
    decimal? Spread,
    string? LoanPurposeCode,
    string? LoanPurposeCustom,
    bool RelatedEsg,
    bool PreConsultationStandard,
    bool IsEarthquakeRelated,
    string? OnSitePhysicalConfirmer,
    DateTimeOffset? OnSitePhysicalConfirmationDateTime,
    string? RepaymentType,
    string? CollateralType,
    string? CollateralOrGuaranteeCustom,
    string? GuaranteeType,
    TrafficSource? TrafficSource,
    string? CreditLimitProductCode,
    CancelTypeOfLoan? CancelTypeOfLoan,
    string? CancelReason,
    IEnumerable<Domain.Entities.Guarantor>? Guarantors,
    string Version
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
