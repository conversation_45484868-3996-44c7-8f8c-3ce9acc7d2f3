using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.FindCommunicationPlanByStaffIds;

public class FindHypotheticalDiscussionOfIssuesByStaffIdSpecification : BaseSpecification<Domain.Entities.HypotheticalDiscussionOfIssues>
{
    public FindHypotheticalDiscussionOfIssuesByStaffIdSpecification(string staffId)
    {
        Query
            .Where(e => e.StaffId == staffId)
            .Where(e => e.Status != Domain.Enums.Status.Completed)
            .AsNoTracking();
    }
}
