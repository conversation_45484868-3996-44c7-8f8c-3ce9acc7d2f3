using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class CommercialDistributionTemplateConfiguration : IEntityTypeConfiguration<CommercialDistributionTemplate>
{
    public void Configure(EntityTypeBuilder<CommercialDistributionTemplate> builder)
    {
        builder.Property(c => c.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(c => c.TemplateName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.CanvasColor)
            .IsRequired()
            .HasMaxLength(9);
    }
}
