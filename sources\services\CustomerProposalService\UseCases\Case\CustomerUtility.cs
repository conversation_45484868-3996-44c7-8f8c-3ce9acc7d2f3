using CustomerProposalService.Domain.Dto;
using Microsoft.AspNetCore.Http;
using Nut.Results;
using Shared.Application;
using Shared.Messaging;
using SharedKernel.ExternalApi.MessageContract.CustomerIdentifying;

namespace CustomerProposalService.UseCases.Case;
public class CustomerUtility : ICustomerUtility
{
    private readonly IMessageSender<FindCustomerByIdsQuery, IEnumerable<FindCustomerByIdsResult>> _findCustomerAcrossServiceByIdsSender;

    public CustomerUtility(
        IMessageSender<FindCustomerByIdsQuery, IEnumerable<FindCustomerByIdsResult>> findCustomerAcrossServiceByIdsSender
        )
    {
        _findCustomerAcrossServiceByIdsSender = findCustomerAcrossServiceByIdsSender ?? throw new ArgumentNullException(nameof(findCustomerAcrossServiceByIdsSender));
    }

    public virtual async Task<Result<IEnumerable<FindCaseCustomerDto>>> FindCustomersByCustomerIdentificationIdsAsync(FindCustomerByIdsQuery request)
    {
        var result = await _findCustomerAcrossServiceByIdsSender.SendAsync(request).ConfigureAwait(false);
        if (result.IsError) return Result.Error<IEnumerable<FindCaseCustomerDto>>("顧客検索でエラーが発生しました");

        // 100万件を超える場合はエラーとする
        if (result.Get().Count() > 1000000)
            return Result.Error<IEnumerable<FindCaseCustomerDto>>("指定した条件に該当する顧客数が多すぎます。検索条件を見直してください。");

        return result.Map(pr =>
            pr.Select(x =>
                new FindCaseCustomerDto()
                {
                    CustomerIdentificationId = x.Id,
                    BranchNumber = x.BranchNumber,
                    CifNumber = x.CifNumber,
                    NameKanji = x.NameKanji,
                    NameKana = x.NameKana,
                    StaffName = x.StaffName,
                    LoanRating = x.LoanRating,
                    TransactionPolicy = x.TransactionPolicy,
                })
            );
    }
}
