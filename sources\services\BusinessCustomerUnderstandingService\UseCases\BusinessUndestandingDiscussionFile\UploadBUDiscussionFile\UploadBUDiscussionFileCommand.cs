using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussionFile.UploadBUDiscussionFile;

[WithDefaultBehaviors]
public record UploadBUDiscussionFileCommand(
   string DiscussionId,
   string UpdaterId,
   string UpdaterName,
   List<string> SelectedFileNames,
   List<IFormFile> UploadFiles) : IRequest<Result<Domain.Entities.BusinessUnderstandingDiscussion>>
{
}
