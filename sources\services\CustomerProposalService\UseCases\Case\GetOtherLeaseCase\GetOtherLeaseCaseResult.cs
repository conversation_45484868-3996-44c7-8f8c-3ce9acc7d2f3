using CustomerProposalService.Domain.Enums;

namespace CustomerProposalService.UseCases.Case.GetOtherLeaseCase;

public record GetOtherLeaseCaseResult(
    string Id,
    Guid CustomerIdentificationId,
    CaseCategory CaseCategory,
    string CaseName,
    CaseStatus CaseStatus,
    string? CaseOutline,
    DateTimeOffset? ExpiredAt,
    string StaffId,
    string StaffName,
    DateTimeOffset RegisteredAt,
    DateTimeOffset CaseUpdatedAt,
    DateTimeOffset LastUpdatedAt,
    IEnumerable<Domain.Entities.CaseFile>? CaseFiles,
    IEnumerable<Domain.Entities.CaseLink>? CaseLinks,
    bool IsEarthquakeRelated,
    string Version
);
