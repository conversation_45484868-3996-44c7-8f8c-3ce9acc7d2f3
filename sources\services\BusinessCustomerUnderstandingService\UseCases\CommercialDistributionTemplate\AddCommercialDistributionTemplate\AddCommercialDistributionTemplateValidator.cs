using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistributionTemplate.AddCommercialDistributionTemplate;

public class AddCommercialDistributionTemplateValidator : AbstractValidator<AddCommercialDistributionTemplateCommand>
{
    public AddCommercialDistributionTemplateValidator()
    {
        RuleFor(v => v.TemplateName).NotEmpty().MaximumLength(100);
        RuleFor(v => v.CanvasColor).NotEmpty().MaximumLength(9);
        RuleForEach(v => v.Nodes).ChildRules(item =>
        {
            item.RuleFor(v => v.Id).NotEmpty();
            item.RuleFor(v => v.NodeType).NotEmpty();
            item.RuleFor(v => v.Left).NotEmpty();
            item.RuleFor(v => v.Top).NotEmpty();
            item.RuleFor(v => v.Width).NotEmpty();
            item.RuleFor(v => v.Height).NotEmpty();
            item.RuleFor(v => v.Title).MaximumLength(64);
            item.RuleFor(v => v.GroupingId).MaximumLength(26);
            item.RuleFor(v => v.Color).MaximumLength(9);
            item.RuleFor(v => v.CustomerName).MaximumLength(100);
            item.RuleFor(v => v.BranchNumber).MaximumLength(3);
            item.RuleFor(v => v.CifNumber).MaximumLength(8);
            item.RuleFor(v => v.Merchandise).MaximumLength(500);
            item.RuleFor(v => v.Material).MaximumLength(500);
            item.RuleFor(v => v.Industry).MaximumLength(20);
            item.RuleFor(v => v.Area).MaximumLength(50);
        });
        RuleForEach(v => v.Edges).ChildRules(item =>
        {
            item.RuleFor(v => v.SourceNode).NotEmpty();
            item.RuleFor(v => v.Source).NotEmpty();
            item.RuleFor(v => v.TargetNode).NotEmpty();
            item.RuleFor(v => v.Target).NotEmpty();
        });
    }
}
