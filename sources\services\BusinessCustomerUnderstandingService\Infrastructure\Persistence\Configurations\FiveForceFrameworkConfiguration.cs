using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class FiveForceFrameworkConfiguration : IEntityTypeConfiguration<FiveForceFramework>
{
    public void Configure(EntityTypeBuilder<FiveForceFramework> builder)
    {
        builder.Property(entity => entity.Id)
            .HasMaxLength(26);

        builder.Property(entity => entity.Ideal)
            .HasMaxLength(1000);

        builder.Property(entity => entity.Issue)
            .HasMaxLength(1000);

        builder.Property(entity => entity.NewComerComment)
            .HasMaxLength(1000);

        builder.Property(entity => entity.AbilityToNegotiateWithSalesPartnersComment)
            .HasMaxLength(1000);

        builder.Property(entity => entity.AbilityToNegotiateWithSuppliersComment)
            .HasMaxLength(1000);

        builder.Property(entity => entity.CompetitorComment)
            .HasMaxLength(1000);

        builder.Property(entity => entity.SubstituteArticleComment)
            .HasMaxLength(1000);

        builder.Property(entity => entity.BusinessUnderstandingId)
            .HasMaxLength(26);
    }
}
