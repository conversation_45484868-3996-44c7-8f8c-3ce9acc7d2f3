using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class ManagementHistoryConfiguration : IEntityTypeConfiguration<ManagementHistory>
{
    public void Configure(EntityTypeBuilder<ManagementHistory> builder)
    {
        builder.HasOne<Management>()
            .WithMany(l => l.Histories)
            .HasForeignKey(entity => entity.OriginalId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .HasMaxLength(26);

        builder.Property(u => u.Ideal)
            .HasMaxLength(1000);

        builder.Property(u => u.Issue)
            .HasMaxLength(1000);

        builder.Property(u => u.MediumToLongTermVisionComment)
            .HasMaxLength(1000);

        builder.Property(u => u.ExperienceComment)
            .HasMaxLength(1000);

        builder.Property(u => u.ExpertiseComment)
            .HasMaxLength(1000);

        builder.Property(u => u.CentripetalForceComment)
            .HasMaxLength(1000);

        builder.Property(u => u.SuccessorComment)
            .HasMaxLength(1000);

    }
}
