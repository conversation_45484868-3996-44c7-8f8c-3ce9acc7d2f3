using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Services.Queries;
using Microsoft.EntityFrameworkCore;
using Nut.Results;
using BusinessCustomerUnderstandingService.Domain.Dto.BusinessUnderstanding;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;

internal class FindBusinessUnderstandingsByCustomerIdentificationIdsQueryService : IFindBusinessUnderstandingsByCustomerIdentificationIdsQueryService
{
    private readonly ApplicationDbContext _dbContext;

    public FindBusinessUnderstandingsByCustomerIdentificationIdsQueryService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    public async Task<Result<List<BusinessUnderstandingDto>>> Handle(IEnumerable<Guid> customerIdentificationIds)
    {
        var query = _dbContext.BusinessUnderstandings
                    .Join(
                          _dbContext.BusinessUnderstandingMaterializedViews
                          .Where(v => customerIdentificationIds.Contains(v.CustomerIdentificationId)
                          ),
                          bu => bu.Id,
                          v => v.BusinessUnderstandingId,
                          (bu, v) => new BusinessUnderstandingDto
                          {
                              BusinessUnderstandingId = bu.Id,
                              CustomerIdentificationId = v.CustomerIdentificationId,
                              TransactionPolicy = v.TransactionPolicy,
                              CustomerStaffId = v.CustomerStaffId,
                              UpdatedDateTime = v.UpdatedDateTime,
                              TransactionPolicyConfirmedDateTime = v.TransactionPolicyConfirmedDateTime,
                              CommunicationPlanCount = v.CommunicationPlanCount,
                              Version = bu.Version,
                          });
        return await query.AsNoTracking().ToListAsync();
    }
}
