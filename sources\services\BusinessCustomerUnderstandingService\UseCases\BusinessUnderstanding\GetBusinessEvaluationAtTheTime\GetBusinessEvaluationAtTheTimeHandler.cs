using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessEvaluationAtTheTime;

public class GetBusinessEvaluationAtTheTimeHandler : IRequestHandler<GetBusinessEvaluationAtTheTimeQuery, Result<GetBusinessEvaluationAtTheTimeResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetBusinessEvaluationAtTheTimeHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<GetBusinessEvaluationAtTheTimeResult>> Handle(GetBusinessEvaluationAtTheTimeQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding, string>();
        var spec = new GetBusinessEvaluationSpecification(request.Id);
        var getBizUnderstandingResult = await repository.SingleAsync(spec);
        if (getBizUnderstandingResult.IsError) return getBizUnderstandingResult.PreserveErrorAs<GetBusinessEvaluationAtTheTimeResult>();
        var bizUnderstanding = getBizUnderstandingResult.Get();

        var result = new GetBusinessEvaluationAtTheTimeResult();
        result.Id = bizUnderstanding.Id;

        // 指定した日付が最新(履歴ではない)の場合
        var materializedView = bizUnderstanding.BusinessUnderstandingMaterializedView;
        if (materializedView is not null && materializedView.UpdatedDateTime == request.TargetDateTime)
        {
            result.Current = bizUnderstanding;
            return Result.Ok(result); ;
        }

        // 経営計画
        var managementPlanHistoryRepository = _unitOfWork.GetRepository<Domain.Entities.ManagementPlanHistory, string>();
        var managementPlanHistorySpec = new GetManagementPlanHistorySpecification(bizUnderstanding.ManagementPlan!.Id, request.TargetDateTime);
        var managementPlanHistory = await managementPlanHistoryRepository.SingleAsync(managementPlanHistorySpec);
        result.ManagementPlan = (managementPlanHistory.IsOk) ? managementPlanHistory.Get() : new Domain.Entities.ManagementPlanHistory();

        // 経営
        var managementHistoryRepository = _unitOfWork.GetRepository<Domain.Entities.ManagementHistory, string>();
        var managementHistorySpec = new GetManagementHistorySpecification(bizUnderstanding.Management!.Id, request.TargetDateTime);
        var managementHistory = await managementHistoryRepository.SingleAsync(managementHistorySpec);
        result.Management = (managementHistory.IsOk) ? managementHistory.Get() : new Domain.Entities.ManagementHistory();

        // 5フォースフレームワーク
        var fiveForceFrameworkHistoryRepository = _unitOfWork.GetRepository<Domain.Entities.FiveForceFrameworkHistory, string>();
        var fiveForceFrameworkHistorySpec = new GetFiveForceFrameworkHistorySpecification(bizUnderstanding.FiveForceFramework!.Id, request.TargetDateTime);
        var fiveForceFrameworkHistory = await fiveForceFrameworkHistoryRepository.SingleAsync(fiveForceFrameworkHistorySpec);
        result.FiveForceFramework = (fiveForceFrameworkHistory.IsOk) ? fiveForceFrameworkHistory.Get() : new Domain.Entities.FiveForceFrameworkHistory();

        // 5フステップフレームワーク
        var fiveStepFrameWorkHistoryRepository = _unitOfWork.GetRepository<Domain.Entities.FiveStepFrameWorkHistory, string>();
        var fiveStepFrameWorkHistorySpec = new GetFiveStepFrameWorkHistorySpecification(bizUnderstanding.FiveStepFrameWork!.Id, request.TargetDateTime);
        var fiveStepFrameWorkHistory = await fiveStepFrameWorkHistoryRepository.SingleAsync(fiveStepFrameWorkHistorySpec);
        result.FiveStepFrameWork = (fiveStepFrameWorkHistory.IsOk) ? fiveStepFrameWorkHistory.Get() : new Domain.Entities.FiveStepFrameWorkHistory();

        // ESG・SDGs
        var eSGAndSDGsHistoryRepository = _unitOfWork.GetRepository<Domain.Entities.ESGAndSDGsHistory, string>();
        var eSGAndSDGsHistorySpec = new GetESGAndSDGsHistorySpecification(bizUnderstanding.ESGAndSDGs!.Id, request.TargetDateTime);
        var eSGAndSDGsHistory = await eSGAndSDGsHistoryRepository.SingleAsync(eSGAndSDGsHistorySpec);
        result.ESGAndSDGs = (eSGAndSDGsHistory.IsOk) ? eSGAndSDGsHistory.Get() : new Domain.Entities.ESGAndSDGsHistory();

        // 外部環境
        var externalEnvironmentHistoryRepository = _unitOfWork.GetRepository<Domain.Entities.ExternalEnvironmentHistory, string>();
        var externalEnvironmentHistorySpec = new GetExternalEnvironmentHistorySpecification(bizUnderstanding.ExternalEnvironment!.Id, request.TargetDateTime);
        var externalEnvironmentHistory = await externalEnvironmentHistoryRepository.SingleAsync(externalEnvironmentHistorySpec);
        result.ExternalEnvironment = (externalEnvironmentHistory.IsOk) ? externalEnvironmentHistory.Get() : new Domain.Entities.ExternalEnvironmentHistory();

        return Result.Ok(result);
    }
}
