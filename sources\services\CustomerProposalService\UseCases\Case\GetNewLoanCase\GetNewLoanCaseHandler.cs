using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.GetNewLoanCase;

public class GetNewLoanCaseHandler : IRequestHandler<GetNewLoanCaseQuery, Result<GetNewLoanCaseResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetNewLoanCaseHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetNewLoanCaseResult>> Handle(GetNewLoanCaseQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.NewLoanCase, string>();
        return repository.SingleAsync(new GetNewLoanCaseSpecification(request))
            .Map(v =>
                new GetNewLoanCaseResult
                (
                    v.Id,
                    v.CustomerIdentificationId,
                    v.CaseCategory,
                    v.CaseName,
                    v.CaseStatus,
                    v.CaseOutline,
                    v.ExpiredAt,
                    v.StaffId,
                    v.StaffName,
                    v.RegisteredAt,
                    v.CaseUpdatedAt,
                    v.CaseUpdateInformation.LastUpdatedAt,
                    v.CaseFiles,
                    v.CaseLinks,
                    v.Version,
                    v.SubjectType,
                    v.Amount,
                    v.Period,
                    v.InterestRate,
                    v.InterestRateCustom,
                    v.UseOfFundsType,
                    v.UseOfFundsCustom,
                    v.RepaymentMethodType,
                    v.RepaymentMethodCustom,
                    v.CollateralOrGuarantee,
                    v.CollateralOrGuaranteeCustom,
                    v.CancelTypeOfLoan,
                    v.CancelReason,
                    v.PreConsultationStandardTarget,
                    v.RelatedEsg,
                    v.IsEarthquakeRelated,
                    v.OnSitePhysicalConfirmer,
                    v.OnSitePhysicalConfirmationDateTime,
                    v.TrafficSource
                    ));
    }
}
