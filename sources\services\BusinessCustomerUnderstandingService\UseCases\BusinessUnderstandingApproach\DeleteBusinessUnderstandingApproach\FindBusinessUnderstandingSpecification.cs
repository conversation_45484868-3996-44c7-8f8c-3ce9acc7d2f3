using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.DeleteBusinessUnderstandingApproach;

public class FindBusinessUnderstandingSpecification : BaseSpecification<Domain.Entities.BusinessUnderstanding>
{
    public FindBusinessUnderstandingSpecification(Guid customerIdentificationId)
    {
        Query
            .Include(x => x.BusinessCustomer)
            .Include(x => x.ExternalEnvironment, x => x.ThenInclude(x => x!.ExternalEnvironmentMaster))
            .Include(x => x.Histories)
            .Where(x => x.BusinessCustomer.CustomerIdentificationId == customerIdentificationId)
            .AsNoTracking();
    }
}
