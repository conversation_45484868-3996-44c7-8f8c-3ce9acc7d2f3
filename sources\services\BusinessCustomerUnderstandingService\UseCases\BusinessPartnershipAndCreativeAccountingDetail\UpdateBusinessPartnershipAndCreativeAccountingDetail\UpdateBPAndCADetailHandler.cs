using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Externals.BusinessCustomerUnderstanding;
using MediatR;
using Nut.Results;
using Shared.Messaging;
using Shared.Domain;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartnershipAndCreativeAccountingDetail.UpdateBusinessPartnershipAndCreativeAccountingDetail;

public class UpdateBPAndCADetailHandler : IRequestHandler<UpdateBPAndCADetailCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageSender<UpdateBusinessUnderstandingUpdatedDateTime> _businessUnderstandingUpdateMessageSender;

    public UpdateBPAndCADetailHandler(
        IUnitOfWork unitOfWork,
        IMessageSender<UpdateBusinessUnderstandingUpdatedDateTime> businessUnderstandingUpdateMessageSender
        )
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _businessUnderstandingUpdateMessageSender = businessUnderstandingUpdateMessageSender ?? throw new ArgumentNullException(nameof(businessUnderstandingUpdateMessageSender));
    }

    public async Task<Result<string>> Handle(UpdateBPAndCADetailCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessPartnershipAndCreativeAccountingDetail, string>();

        // 既存データを取得します。
        var getResult = await repository.GetAsync(request.Id, true);

        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();

        currentData.HasBusinessPartnershipWithOurCompany = request.HasBusinessPartnershipWithOurCompany;
        currentData.FeatureOfBusinessPartnership = request.FeatureOfBusinessPartnership;
        currentData.HasCreativeAccountingIncident = request.HasCreativeAccountingIncident;
        currentData.DescriptionOfCreativeAccountingIncident = request.DescriptionOfCreativeAccountingIncident;
        // バージョン番号は、同時実行制御を有効にするために引数で受け取ったデータの値で必ず上書きします。
        currentData.Version = request.Version;

        // 事業性理解の最終更新日更新
        var updateBusinessUnderstandingResult = await UpdateBusinessUnderstandingUpdatedDateTime(request, currentData.BusinessUnderstandingId);
        if (updateBusinessUnderstandingResult.IsError) return updateBusinessUnderstandingResult.PreserveErrorAs<string>();

        return await repository
            .UpdateAsync(currentData) // データを更新として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }

    // 事業性理解の最終更新日を更新
    private async Task<Result> UpdateBusinessUnderstandingUpdatedDateTime(UpdateBPAndCADetailCommand request, string businessUnderstandingId)
    {
        var updateBusinessUnderstandingResult = await _businessUnderstandingUpdateMessageSender.SendAsync(new UpdateBusinessUnderstandingUpdatedDateTime()
        {
            BusinessUnderstandingId = businessUnderstandingId,
            UpdaterId = request.StaffId,
            UpdaterName = request.StaffName,
        });

        return updateBusinessUnderstandingResult;
    }
}
