using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingFile.UploadBusinessUnderstandingFile;

public class UploadBusinessUnderstandingFileSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingFile>
{
    public UploadBusinessUnderstandingFileSpecification(string businessUnderstandingId, string fileName)
    {
        Query
            .Where(e => e.BusinessUnderstandingId == businessUnderstandingId)
            .Where(e => e.FileName == fileName)
            .AsNoTracking();
    }
}
