using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingFile.DeleteBusinessUnderstandingFile;

public class DeleteBusinessUnderstandingFileSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingFile>
{
    public DeleteBusinessUnderstandingFileSpecification(DeleteBusinessUnderstandingFileCommand request)
    {
        Query
            .Where(e => e.BusinessUnderstandingId == request.BusinessUnderstandingId)
            .Where(e => e.FileName == request.FileName)
            .AsNoTracking();
    }
}
