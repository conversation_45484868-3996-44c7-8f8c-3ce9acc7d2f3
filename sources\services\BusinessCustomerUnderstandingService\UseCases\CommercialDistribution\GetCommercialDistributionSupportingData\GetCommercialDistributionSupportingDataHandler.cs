using MediatR;
using Nut.Results;


namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistribution.GetCommercialDistributionSupportingData;

public class GetCommercialDistributionSupportingDataHandler : IRequestHandler<GetCommercialDistributionSupportingDataQuery, Result<IEnumerable<GetCommercialDistributionSupportingDataResult>>>
{
    private readonly IGetCommercialDistributionSupportingDataQueryService _queryService;

    public GetCommercialDistributionSupportingDataHandler(IGetCommercialDistributionSupportingDataQueryService queryService)
    {
        _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
    }

    public Task<Result<IEnumerable<GetCommercialDistributionSupportingDataResult>>> Handle(GetCommercialDistributionSupportingDataQuery request, CancellationToken cancellationToken)
    {
        if (request == null)
            throw new ArgumentNullException(nameof(request));

        // 取得を行います。
        return _queryService.Handle(request);
    }
}
