using BusinessCustomerUnderstandingService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThread.AddBusinessUnderstandingThread;

[WithDefaultBehaviors]
public record AddBusinessUnderstandingThreadCommand(
    string Registrant,
    string Title,
    string Description,
    string BusinessUnderstandingId,
    string CustomerName,
    string? MentionTargetsHtml,
    IEnumerable<string>? MentionTargetUserIds,
    IEnumerable<string>? MentionTargetTeamIds,
    IEnumerable<string>? MentionTargetTeamMemberUserIds,
    DiscussionPurpose Purpose,
    string? Person,
    bool? IsPersonOfPower,
    DateTimeOffset? CorrespondenceDate,
    bool? IsCorporateDepositTheme,
    bool? IsFundSettlementTheme,
    IEnumerable<IFormFile>? UploadFiles
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
