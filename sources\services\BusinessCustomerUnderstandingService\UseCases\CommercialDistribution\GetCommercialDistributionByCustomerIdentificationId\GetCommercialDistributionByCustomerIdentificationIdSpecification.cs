using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistribution.GetCommercialDistributionByCustomerIdentificationId;

public class GetCommercialDistributionByCustomerIdentificationIdSpecification : BaseSpecification<Domain.Entities.CommercialDistribution>
{
    public GetCommercialDistributionByCustomerIdentificationIdSpecification(string Id)
    {
        Query
            .Where(x => x.Id == Id)
            .Include(x => x.Nodes)
            .Include(x => x.Edges)
            .AsNoTracking();
    }
}
