using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;

public class ToDoThreadFileConfiguration : IEntityTypeConfiguration<ToDoThreadFile>
{
    public void Configure(EntityTypeBuilder<ToDoThreadFile> builder)
    {
        builder.HasOne<ToDoThread>()
            .WithMany(l => l.Files)
            .HasForeignKey(j => j.ThreadId)
            .HasConstraintName("fk_todo_thread_todo_thread_file_id")
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.FileName)
            .IsRequired()
            .HasMaxLength(300);
    }
}
