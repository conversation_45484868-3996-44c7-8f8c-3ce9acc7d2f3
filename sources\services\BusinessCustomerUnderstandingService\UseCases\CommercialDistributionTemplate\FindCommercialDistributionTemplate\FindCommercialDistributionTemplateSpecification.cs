using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistributionTemplate.FindCommercialDistributionTemplate;

public class FindCommercialDistributionTemplateSpecification : BaseSpecification<Domain.Entities.CommercialDistributionTemplate>
{
    public FindCommercialDistributionTemplateSpecification(FindCommercialDistributionTemplateQuery request)
    {
        Query
            .WhereIfNotEmpty(request.TemplateName, e => e.TemplateName.Contains(request.TemplateName!))
            .Include(e => e.Nodes)
            .Include(e=>e.Edges)
            .AsNoTracking();
    }
}
