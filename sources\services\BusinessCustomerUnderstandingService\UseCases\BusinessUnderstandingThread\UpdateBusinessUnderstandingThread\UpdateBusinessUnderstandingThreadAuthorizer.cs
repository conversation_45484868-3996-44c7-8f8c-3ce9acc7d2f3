using Nut.MediatR;
using Shared.Services;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThread.UpdateBusinessUnderstandingThread;
public class UpdateBusinessUnderstandingThreadAuthorizer : IAuthorizer<UpdateBusinessUnderstandingThreadCommand>
{
    private readonly ICurrentUserService _currentUserService;

    public UpdateBusinessUnderstandingThreadAuthorizer(ICurrentUserService currentUserService)
    {
        _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
    }

    public async Task<AuthorizationResult> AuthorizeAsync(UpdateBusinessUnderstandingThreadCommand request, CancellationToken cancellationToken)
    {
        var currentUser = await _currentUserService.GetAsync().ConfigureAwait(false);

        if (currentUser.UserId == request.RegistrantId)
        {
            return AuthorizationResult.Success();
        }

        return AuthorizationResult.Failed("不正なリクエストです");
    }
}
