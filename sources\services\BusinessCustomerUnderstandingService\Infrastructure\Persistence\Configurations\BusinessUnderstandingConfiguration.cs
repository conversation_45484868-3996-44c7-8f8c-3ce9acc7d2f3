using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class BusinessUnderstandingConfiguration : IEntityTypeConfiguration<BusinessUnderstanding>
{
    public void Configure(EntityTypeBuilder<BusinessUnderstanding> builder)
    {
        builder.HasOne(b => b.BusinessCustomer)
            .WithOne(b => b.BusinessUnderstanding)
            .HasForeignKey<BusinessUnderstanding>(b => b.BusinessCustomerId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.TransactionPolicy)
            .IsRequired()
            .HasMaxLength(2);

        builder.Property(u => u.TransactionPolicyConfirmerId)
            .HasMaxLength(100);

        builder.Property(u => u.CustomerStaffId)
            .HasMaxLength(26);

        builder.Property(u => u.CommunicationPlanCount)
            .IsRequired()
            .HasDefaultValue(0);

        // 一意キー制約
        builder.HasIndex(u => u.BusinessCustomerId)
            .IsUnique();

        builder.HasIndex(u => u.BusinessCustomerId)
            .IsUnique(false)
            .IsClustered(false);
    }
}
