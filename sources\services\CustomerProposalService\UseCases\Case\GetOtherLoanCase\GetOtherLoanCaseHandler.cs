using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.GetOtherLoanCase;

public class GetOtherLoanCaseHandler : IRequestHandler<GetOtherLoanCaseQuery, Result<GetOtherLoanCaseResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetOtherLoanCaseHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetOtherLoanCaseResult>> Handle(GetOtherLoanCaseQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.OtherLoanCase, string>();
        return repository.SingleAsync(new GetOtherLoanCaseSpecification(request))
            .Map(v =>
                new GetOtherLoanCaseResult
                (
                    v.Id,
                    v.CustomerIdentificationId,
                    v.CaseCategory,
                    v.CaseName,
                    v.CaseStatus,
                    v.CaseOutline,
                    v.ExpiredAt,
                    v.StaffId,
                    v.StaffName,
                    v.RegisteredAt,
                    v.CaseUpdatedAt,
                    v.CaseUpdateInformation.LastUpdatedAt,
                    v.CaseFiles,
                    v.CaseLinks,
                    v.Version,
                    v.PreConsultationStandardTarget,
                    v.IsEarthquakeRelated
                    ));
    }
}
