using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Services.Queries;
using MediatR;
using Microsoft.Extensions.Logging;
using Nut.Results;
using Shared.Domain;
using Shared.Spec;
using SharedKernel.ExternalApi.MessageContract.CustomerFixedInformation;
using SharedKernel.ExternalApi.Services.ApiClient;
using Dto = BusinessCustomerUnderstandingService.Domain.Dto.BusinessUnderstanding;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingMaterializedView.UpdateBusinessUnderstandingMaterializedView;

public class UpdateBusinessUnderstandingMaterializedViewHandler : IRequestHandler<UpdateBusinessUnderstandingMaterializedViewCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IGeneralPostApiClient<FindCustomerStaffQuery, IEnumerable<FindCustomerStaffResult>> _findCustomerStaffApiClient;
    private readonly IGetCommunicationPlanCountQueryService _communicationPlanCountService;

    public UpdateBusinessUnderstandingMaterializedViewHandler(
        IUnitOfWork unitOfWork,
        IGeneralPostApiClient<FindCustomerStaffQuery, IEnumerable<FindCustomerStaffResult>> findCustomerStaffApiClient,
        IGetCommunicationPlanCountQueryService communicationPlanCountService
        )
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _findCustomerStaffApiClient = findCustomerStaffApiClient ?? throw new ArgumentNullException(nameof(findCustomerStaffApiClient));
        _communicationPlanCountService = communicationPlanCountService ?? throw new ArgumentNullException(nameof(communicationPlanCountService));
    }

    public async Task<Result<string>> Handle(UpdateBusinessUnderstandingMaterializedViewCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var logger = request.logger;
        logger.LogInformation("検索用事業性理解テーブルの更新バッチ起動");

        // 事業性理解のデータを取得
        var queueRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingMaterializedViewQueue, string>();
        var getQueueResult = await queueRepository.AllAsync().ConfigureAwait(false);
        if (getQueueResult.IsError)
        {
            logger.LogError("キューの取得失敗");
            return getQueueResult.PreserveErrorAs<string>();
        }
        var queues = getQueueResult.Get();
        if (!queues.Any())
        {
            logger.LogInformation("検索用事業性理解テーブルの更新バッチ終了(更新対象無し)");
            return Result.Ok("");
        }

        // 更新日付の降順にソート(5回実行済のキューは除く)
        queues = queues.Where(x => x.NumberOfRetries < 5).OrderByDescending(x => x.UpdatedDateTime).ToList();

        // 顧客担当者を一括取得する
        var customerIds = queues.Select(x => x.CustomerIdentificationId).Distinct().ToArray();
        var customerStaffResult = await _findCustomerStaffApiClient.SendAsync(new() { CustomerIdentificationIds = customerIds });
        if (customerStaffResult.IsError)
        {
            logger.LogError("顧客担当者取得失敗");
            return customerStaffResult.PreserveErrorAs<string>();
        }
        var staffIds = customerStaffResult.Get().ToDictionary(x => x.CustomerIdentificationId, x => x.StaffId);

        // 事業性理解IDごとに処理
        var businessUnderstandingIds = queues.Select(x => x.BusinessUnderstandingId).Distinct();
        var saveErrorCount = 0;
        foreach (var businessUnderstandingId in businessUnderstandingIds)
        {
            // 更新日付が最大のものを抽出
            var latestQue = queues.First(x => x.BusinessUnderstandingId == businessUnderstandingId);

            // 更新日付が最大でないものは削除
            var deleteTargets = queues.Where(x => x.BusinessUnderstandingId == businessUnderstandingId && x.Id != latestQue.Id);
            foreach (var deleteTarget in deleteTargets)
                await queueRepository.DeleteAsync(deleteTarget);

            // 検索用事業性理解の更新
            logger.LogInformation($"更新開始 - QueId:{latestQue.Id}, BusinessUnderstandingID:{latestQue.BusinessUnderstandingId}");
            var updateResult = await UpdateBusinessUnderstandingMaterializedView(latestQue, staffIds, logger);
            if (updateResult.IsError)
            {
                // 失敗時はリトライカウントを加算
                latestQue.NumberOfRetries++;
                logger.LogError($"更新失敗({latestQue.NumberOfRetries}回目) - QueId:{latestQue.Id}, BusinessUnderstandingID:{latestQue.BusinessUnderstandingId}, エラー内容：{updateResult.GetError().Message}");
                await queueRepository.UpdateAsync(latestQue);
            }
            else
            {
                // 正常終了したキューは削除
                logger.LogInformation($"更新成功 - QueId:{latestQue.Id}, BusinessUnderstandingID:{latestQue.BusinessUnderstandingId}");
                await queueRepository.DeleteAsync(latestQue);
            }

            var saveResult = await _unitOfWork.SaveEntitiesAsync();
            if (saveResult.IsError)
            {
                logger.LogError($"更新内容の保存に失敗:{saveResult.GetError().Message}");
                saveErrorCount++;
                if (saveErrorCount > 5)
                {
                    // 処理続行できないと判断して処理を終了する
                    return saveResult.PreserveErrorAs<string>();
                }
            }
        }

        logger.LogInformation("検索用事業性理解テーブルの更新バッチ終了");
        return Result.Ok("");
    }

    public async Task<Result<string>> UpdateBusinessUnderstandingMaterializedView(Domain.Entities.BusinessUnderstandingMaterializedViewQueue queue, Dictionary<Guid, string> staffIds, ILogger logger)
    {
        try
        {
            // 事業性理解のデータを取得
            var bizUnderstandingRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding, string>();
            var getBizUnderstandingResult = await bizUnderstandingRepository.SingleAsync(
                new FindByIdSpecification<Domain.Entities.BusinessUnderstanding, string>(queue.BusinessUnderstandingId)
                .Include(c => c.ManagementPlan)
                .Include(c => c.Management)
                .Include(c => c.FiveForceFramework)
                .Include(c => c.FiveStepFrameWork)
                .Include(c => c.ESGAndSDGs)
                .Include(c => c.ExternalEnvironment, c => c.ThenInclude(x => x!.ExternalEnvironmentMaster))
                .Include(c => c.BusinessCustomer)
                .Include(c => c.RelationLevel)
                ).ConfigureAwait(false);
            if (getBizUnderstandingResult.IsError)
            {
                logger.LogError("事業性理解取得失敗");
                return getBizUnderstandingResult.PreserveErrorAs<string>();
            }

            var bizUnderstanding = getBizUnderstandingResult.Get();

            // 事業性理解の取引方針(T1～T5)
            var calculationResult = GetCalculationResult(bizUnderstanding);
            var transactionPolicy = GetTransactionPolicy(calculationResult);

            // 事業性理解の取組方(必須先～簡易先)
            var approachRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingApproach>();

            var approachSpec = new GetBusinessUnderstandingApproachSpecification(queue.CustomerIdentificationId);

            var getApproachResult = await approachRepository.FindAsync(approachSpec);
            if (getApproachResult.IsError)
            {
                logger.LogError("事業性理解の取組方取得失敗");
                return getApproachResult.PreserveErrorAs<string>();
            }
            var approach = getApproachResult.Get();
            Domain.Enums.BusinessUnderstandingApproachType? approachType = approach.Any() ? approach.First().ApproachType : null!;

            // 顧客担当者
            if (!staffIds.TryGetValue(queue.CustomerIdentificationId, out var staffId)) staffId = null;

            // コミュニケーションプランの件数
            var getCommunicationPlanCountResult = await _communicationPlanCountService.Handle(queue.BusinessUnderstandingId);
            if (getCommunicationPlanCountResult.IsError)
            {
                logger.LogError("コミュニケーションプラン件数取得失敗");
                return getCommunicationPlanCountResult.PreserveErrorAs<string>();
            }
            var communicationPlanCount = getCommunicationPlanCountResult.Get();

            // 検索用事業性理解
            var materializedViewRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingMaterializedView, string>();
            var findSpec = new FindBusinessUnderstandingMaterializedViewSpecification(queue.BusinessUnderstandingId);
            var findMaterializedViewResult = await materializedViewRepository.FindAsync(findSpec).ConfigureAwait(false);
            if (findMaterializedViewResult.IsError)
            {
                logger.LogError("検索用事業性理解取得失敗");
                return findMaterializedViewResult.PreserveErrorAs<string>();
            }
            var materializedViewList = findMaterializedViewResult.Get();

            // 検索用事業性理解に存在しない場合は追加
            if (!materializedViewList.Any())
                materializedViewList.Add(new Domain.Entities.BusinessUnderstandingMaterializedView());
            var materializedView = materializedViewList.First();

            // 検索用事業性理解の更新
            materializedView.BusinessUnderstandingId = queue.BusinessUnderstandingId;
            materializedView.CommunicationPlanCount = communicationPlanCount;
            materializedView.TransactionPolicy = transactionPolicy;
            materializedView.BusinessUnderstandingApproachType = approachType;
            materializedView.CustomerStaffId = staffId;
            materializedView.TransactionPolicyConfirmerId = bizUnderstanding.TransactionPolicyConfirmerId;
            materializedView.TransactionPolicyConfirmedDateTime = bizUnderstanding.TransactionPolicyConfirmedDateTime;
            materializedView.UpdaterId = queue.UpdaterId;
            materializedView.UpdaterName = queue.UpdaterName;
            materializedView.UpdatedDateTime = queue.UpdatedDateTime;

            if (materializedView.Id is null)
            {
                materializedView.Id = Ulid.NewUlid().ToString();
                materializedView.CustomerIdentificationId = queue.CustomerIdentificationId;
                await materializedViewRepository.AddAsync(materializedView);
            }
            else
            {
                await materializedViewRepository.UpdateAsync(materializedView);
            }

            return Result.Ok("");
        }
        catch (Exception ex)
        {
            return Result.Error<string>(ex.ToString());
        }
    }

    // 全評点計算結果
    private Dto.CalculationResult GetCalculationResult(Domain.Entities.BusinessUnderstanding currentData)
    {
        return new Dto.CalculationResult(
            currentData.ManagementPlan!,
            currentData.Management!,
            currentData.FiveForceFramework!,
            currentData.FiveStepFrameWork!,
            currentData.ExternalEnvironment!,
            currentData.ESGAndSDGs!,
            0,
            currentData.RelationLevel!
        );
    }

    // 取引方針
    private string GetTransactionPolicy(Dto.CalculationResult calculationResult)
    {
        var transactionPolicy = new Dto.TransactionPolicyResult(
            calculationResult.MinimumRelationLevel,
            calculationResult.TotalScore
            ).TransactionPolicy;

        return transactionPolicy;
    }
}
