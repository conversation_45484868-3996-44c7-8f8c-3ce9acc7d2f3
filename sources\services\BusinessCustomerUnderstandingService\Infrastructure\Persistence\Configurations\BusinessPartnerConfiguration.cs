using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class BusinessPartnerConfiguration : IEntityTypeConfiguration<BusinessPartner>
{
    public void Configure(EntityTypeBuilder<BusinessPartner> builder)
    {
        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.BusinessUnderstandingId)
            .IsRequired()
            .HasMaxLength(26);

        builder.HasOne<BusinessUnderstanding>()
            .WithMany(u => u.BusinessPartners)
            .HasForeignKey(u => u.BusinessUnderstandingId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.BusinessPartnerCustomerIdentificationId)
            .HasMaxLength(36);
    }
}