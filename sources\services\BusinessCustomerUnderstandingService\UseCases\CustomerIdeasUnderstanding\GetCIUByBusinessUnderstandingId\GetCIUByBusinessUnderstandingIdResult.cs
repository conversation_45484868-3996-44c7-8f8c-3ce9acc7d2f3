using BusinessCustomerUnderstandingService.Domain.Enums;

namespace BusinessCustomerUnderstandingService.UseCases.CustomerIdeasUnderstanding.GetCIUByBusinessUnderstandingId;

public record GetCIUByBusinessUnderstandingIdResult(
    string Id,
    string? Title,
    Status? Status,
    int? Order,
    DateTimeOffset? ExpiredAt,
    string? StaffId,
    string? StaffName,
    string? TargetPerson,
    string? Description,
    string? Note,
    List<Domain.Entities.CustomerIdeasUnderstandingThread>? Threads,
    DateTimeOffset RegisteredDateTime,
    DateTimeOffset? UpdatedDateTime,
    DateTimeOffset? CompletedDateTime,
    string? UpdaterId,
    string? UpdaterName,
    string? RegistrantId,
    string? RegistrantName,
    string BusinessUnderstandingId,
    string Version
    );
