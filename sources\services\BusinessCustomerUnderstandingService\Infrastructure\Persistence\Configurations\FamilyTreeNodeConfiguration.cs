using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class FamilyTreeNodeConfiguration : IEntityTypeConfiguration<FamilyTreeNode>
{
    public void Configure(EntityTypeBuilder<FamilyTreeNode> builder)
    {
        builder.HasOne<FamilyTree>()
            .WithMany(c => c.FamilyTreeNodes)
            .HasForeignKey(n => n.FamilyTreeId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(n => n.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(n => n.Name)
            .HasMaxLength(100);

        builder.Property(n => n.Relationship)
            .HasMaxLength(16);

        builder.Property(n => n.Note)
            .HasMaxLength(64);
    }
}

