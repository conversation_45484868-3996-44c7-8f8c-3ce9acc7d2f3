using FluentValidation;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.UpdateLeaseCaseDiscussionThread;

public class UpdateLeaseCaseDiscussionThreadValidator : AbstractValidator<UpdateLeaseCaseDiscussionThreadCommand>
{
    public UpdateLeaseCaseDiscussionThreadValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.CustomerIdentificationId).NotEmpty();
        RuleFor(v => v.CustomerName).NotEmpty();
        RuleFor(v => v.RegistrantId).NotEmpty();
        RuleForEach(v => v.ThreadNameTypes).IsInEnum().NotEqual(Domain.Enums.ThreadNameType.Undefined);
        When(v => v.ThreadNameTypes.Any(x => x == Domain.Enums.ThreadNameType.Other) == true, () => {
            RuleFor(v => v.ThreadNameForOther).NotEmpty().MaximumLength(50);
        });
        RuleFor(v => v.Purpose).IsInEnum().NotEqual(Domain.Enums.CaseDiscussionPurpose.Undefined);
        RuleFor(v => v.Person).MaximumLength(20);
        RuleFor(v => v.Description).NotEmpty().MaximumLength(10000);
        RuleFor(v => v.Version).NotEmpty();
    }
}
