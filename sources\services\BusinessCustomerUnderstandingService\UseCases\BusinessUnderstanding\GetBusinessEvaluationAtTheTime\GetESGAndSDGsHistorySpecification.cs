using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.GetBusinessEvaluationAtTheTime;

public class GetESGAndSDGsHistorySpecification : BaseSpecification<Domain.Entities.ESGAndSDGsHistory>
{
    public GetESGAndSDGsHistorySpecification(string originalId, DateTimeOffset targetDateTime)
    {
        Query.Where(x => x.OriginalId == originalId && x.UpdatedDateTime == targetDateTime).AsNoTracking();
    }
}
