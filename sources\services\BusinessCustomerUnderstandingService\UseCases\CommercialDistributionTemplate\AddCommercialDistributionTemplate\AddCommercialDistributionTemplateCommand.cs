using BusinessCustomerUnderstandingService.Domain.Entities;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistributionTemplate.AddCommercialDistributionTemplate;

[WithDefaultBehaviors]
public record AddCommercialDistributionTemplateCommand(
    string TemplateName,
    string CanvasColor,
    List<CommercialDistributionTemplateNode> Nodes,
    List<CommercialDistributionTemplateEdge> Edges
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
