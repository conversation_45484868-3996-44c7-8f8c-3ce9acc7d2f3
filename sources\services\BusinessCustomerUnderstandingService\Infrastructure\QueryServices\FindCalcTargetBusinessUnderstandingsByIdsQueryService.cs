using BusinessCustomerUnderstandingService.Domain.Entities;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Services.Queries;
using Microsoft.EntityFrameworkCore;
using Nut.Results;
using Shared.Spec;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;
public class FindCalcTargetBusinessUnderstandingsByIdsQueryService : IFindCalcTargetBusinessUnderstandingsByIdsQueryService
{
    private readonly ApplicationDbContext _dbContext;

    public FindCalcTargetBusinessUnderstandingsByIdsQueryService(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    public async Task<Result<IEnumerable<BusinessUnderstanding>>> Handle()
    {

        var businessCustomers = await _dbContext.BusinessCustomers.AsNoTracking().ToArrayAsync();
        var businessCustomersDictionary = businessCustomers.ToDictionary(x => x.Id);

        var managementPlans = (await _dbContext.ManagementPlans.AsNoTracking().ToArrayAsync())
            .ToDictionary(x => x.BusinessUnderstandingId);

        var managements = (await _dbContext.Managements.AsNoTracking().ToArrayAsync())
            .ToDictionary(x => x.BusinessUnderstandingId);

        var fiveForceFrameworks = (await _dbContext.FiveForceFrameworks.AsNoTracking().ToArrayAsync())
            .ToDictionary(x => x.BusinessUnderstandingId);

        var fiveStepFrameWorks = (await _dbContext.FiveStepFrameWorks.AsNoTracking().ToArrayAsync())
            .ToDictionary(x => x.BusinessUnderstandingId);

        var esgAndSdgs = (await _dbContext.ESGAndSDGs.AsNoTracking().ToArrayAsync())
            .ToDictionary(x => x.BusinessUnderstandingId);

        var externalEnvironments = (await _dbContext.ExternalEnvironments.AsNoTracking().ToArrayAsync())
            .ToDictionary(x => x.BusinessUnderstandingId);

        var relationLevels = (await _dbContext.RelationLevels.AsNoTracking().ToArrayAsync())
            .ToDictionary(x => x.BusinessUnderstandingId);

        var materializedViews = (await _dbContext.BusinessUnderstandingMaterializedViews.AsNoTracking().ToArrayAsync())
            .ToDictionary(x => x.BusinessUnderstandingId);

        var businessUnderstandings = await _dbContext.BusinessUnderstandings.AsNoTracking().ToArrayAsync();

        foreach (var businessUnderstanding in businessUnderstandings)
        {
            businessUnderstanding.BusinessCustomer = GetEntity(businessCustomersDictionary, businessUnderstanding.BusinessCustomerId)!;
            businessUnderstanding.ManagementPlan = GetEntity(managementPlans, businessUnderstanding.Id);
            businessUnderstanding.Management = GetEntity(managements, businessUnderstanding.Id);
            businessUnderstanding.FiveForceFramework = GetEntity(fiveForceFrameworks, businessUnderstanding.Id);
            businessUnderstanding.FiveStepFrameWork = GetEntity(fiveStepFrameWorks, businessUnderstanding.Id);
            businessUnderstanding.ESGAndSDGs = GetEntity(esgAndSdgs, businessUnderstanding.Id);
            businessUnderstanding.ExternalEnvironment = GetEntity(externalEnvironments, businessUnderstanding.Id);
            businessUnderstanding.RelationLevel = GetEntity(relationLevels, businessUnderstanding.Id);
            businessUnderstanding.BusinessUnderstandingMaterializedView = GetEntity(materializedViews, businessUnderstanding.Id);
        }

        return businessUnderstandings;
    }

    private T? GetEntity<T>(Dictionary<string, T> entities, string id) where T : class
    {
        if (!entities.TryGetValue(id, out var result)) return default(T);

        return result;
    }
}
