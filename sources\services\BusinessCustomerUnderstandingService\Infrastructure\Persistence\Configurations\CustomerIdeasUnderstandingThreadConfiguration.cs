using BusinessCustomerUnderstandingService.Domain.Entities;
using BusinessCustomerUnderstandingService.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;
public class CustomerIdeasUnderstandingThreadConfiguration : IEntityTypeConfiguration<CustomerIdeasUnderstandingThread>
{
    public void Configure(EntityTypeBuilder<CustomerIdeasUnderstandingThread> builder)
    {
        builder.HasOne<CustomerIdeasUnderstanding>()
            .WithMany(l => l.Threads)
            .HasForeignKey(j => j.CustomerIdeasUnderstandingId)
            .HasConstraintName("fk_customer_ideas_understanding_threads_customer_ideas_understanding_id")
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
                .HasMaxLength(26);

        builder.Property(u => u.RegisteredDateTime)
                .IsRequired();

        builder.Property(u => u.Registrant)
                .IsRequired();

        builder.Property(u => u.RegistrantId)
                .IsRequired();

        builder.Property(u => u.Title)
                .IsRequired();

        builder.Property(u => u.Description)
                .IsRequired();

        builder.Property(u => u.CustomerIdeasUnderstandingId)
                .HasMaxLength(26)
                .IsRequired();
        
        builder.Property(e => e.Purpose)
                .HasConversion(
                        v => v.ToString(),
                        v => (DiscussionPurpose)Enum.Parse(typeof(DiscussionPurpose), v)
                )
                .HasDefaultValue(DiscussionPurpose.Internal);

        builder.Property(u => u.Person)
                .HasMaxLength(20);

        builder.Property(e => e.MentionTargetUserIds).HasConversion(
               v => JsonConvert.SerializeObject(v, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }),
               v => JsonConvert.DeserializeObject<IEnumerable<string>>(v, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }));

        builder.Property(e => e.MentionTargetTeamIds).HasConversion(
               v => JsonConvert.SerializeObject(v, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }),
               v => JsonConvert.DeserializeObject<IEnumerable<string>>(v, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }));

    }
}
