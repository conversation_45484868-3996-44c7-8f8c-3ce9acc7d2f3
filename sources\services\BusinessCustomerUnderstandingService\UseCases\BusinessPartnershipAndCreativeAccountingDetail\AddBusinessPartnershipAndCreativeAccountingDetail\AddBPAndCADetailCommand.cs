using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartnershipAndCreativeAccountingDetail.AddBusinessPartnershipAndCreativeAccountingDetail;

[WithDefaultBehaviors]
public record AddBPAndCADetailCommand(
    string BusinessUnderstandingId,
    bool? HasBusinessPartnershipWithOurCompany,
    string? FeatureOfBusinessPartnership,
    bool? HasCreativeAccountingIncident,
    string? DescriptionOfCreativeAccountingIncident,
    string StaffId,
    string StaffName
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
