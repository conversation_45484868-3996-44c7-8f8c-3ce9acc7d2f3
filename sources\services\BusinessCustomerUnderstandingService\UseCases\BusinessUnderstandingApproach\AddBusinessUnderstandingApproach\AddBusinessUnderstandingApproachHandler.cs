using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Domain.Entities;
using MediatR;
using Nut.Results;
using Shared.Results.Errors;
using Shared.Services;
using SharedKernel.ExternalApi.MessageContract.MyCareerUser;
using SharedKernel.ExternalApi.Services.ApiClient;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.AddBusinessUnderstandingApproach;

public class AddBusinessUnderstandingApproachHandler : IRequestHandler<AddBusinessUnderstandingApproachCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentDateTimeService _currentDatetimeService;
    private readonly ICurrentUserService _currentUserService;
    private readonly IGeneralGetApiClient<SearchUsers, SearchUsersResult> _searchUserApiClient;

    public AddBusinessUnderstandingApproachHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        ICurrentDateTimeService currentDateTimeService,
        IGeneralGetApiClient<SearchUsers, SearchUsersResult> searchUserApiClient
        )
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _currentDatetimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
        _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
        _searchUserApiClient = searchUserApiClient ?? throw new ArgumentNullException(nameof(searchUserApiClient));
    }

    public async Task<Result<string>> Handle(AddBusinessUnderstandingApproachCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var now = _currentDatetimeService.NowDateTimeOffset();

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingApproach>();

        // 顧客識別IDを持つ法人が存在しないかチェック
        var businessCustomerRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingApproach>();
        var findResult = await businessCustomerRepository.FindAsync(new FindBusinessUnderstandingApproachSpecification(request.CustomerIdentificationId)).ConfigureAwait(false);
        if (findResult.IsError) return findResult.PreserveErrorAs<string>();
        // 存在している場合はエラーを返す
        if (findResult.Get().Any()) return Result.Error<string>(new ChangeConflictException("データが既に存在しています。"));

        // 追加する値を作成します。
        var newData = new Domain.Entities.BusinessUnderstandingApproach()
        {
            // キーの値をUlidで生成します。
            Id = Ulid.NewUlid().ToString(),
            CustomerIdentificationId = request.CustomerIdentificationId,
            ApproachType = request.ApproachType,
            Comment = request.Comment,
        };

        // 事業性理解の取得
        var bizUnderstandingRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding>();
        var getBizUnderstandingResult = await bizUnderstandingRepository.SingleAsync(
            new FindBusinessUnderstandingSpecification(request.CustomerIdentificationId)
            ).ConfigureAwait(false);
        if (getBizUnderstandingResult.IsError) return getBizUnderstandingResult.PreserveErrorAs<string>();
        var bizUnderstanding = getBizUnderstandingResult.Get();

        // ユーザーの取得
        var userId = (await _currentUserService.GetAsync()).UserId!;
        var getUsersResult = await _searchUserApiClient.SendAsync(new SearchUsers { });
        if (getUsersResult.IsError) return　getUsersResult.PreserveErrorAs<string>();
        var users = getUsersResult.Get().ReturnSearchUsersResults;
        var userName = users.Where(u => u.UserId == userId).Select(u => u.NameKanji).SingleOrDefault();

        var history = (bizUnderstanding.Histories.Any()) ? bizUnderstanding.Histories.OrderByDescending(x => x.UpdatedDateTime).First() : null;

        // 履歴の追加
        var newHistory = new BusinessUnderstandingHistory()
        {
            Id = Ulid.NewUlid().ToString(),
            // 取引方針
            TransactionPolicy = history is not null ? history.TransactionPolicy : string.Empty,
            // 事業性理解評点
            BusinessEvaluation = history is not null ? history.BusinessEvaluation : 0,
            ManagementPlanScore = history is not null ? history.ManagementPlanScore : 0,
            ManagementScore = history is not null ? history.ManagementScore : 0,
            FiveForceScore = history is not null ? history.FiveForceScore : 0,
            FiveStepScore = history is not null ? history.FiveStepScore : 0,
            ExternalEnvironmentScore = history is not null ? history.ExternalEnvironmentScore : 0,
            ESGAndSDGsScore = history is not null ? history.ESGAndSDGsScore : 0,
            // リレーションレベル評点
            RelationLevelEvaluation = history is not null ? history.RelationLevelEvaluation : 0,
            AuthorityScore = history is not null ? history.AuthorityScore : 0,
            RelationScore = history is not null ? history.RelationScore : 0,
            DisclosureScore = history is not null ? history.DisclosureScore : 0,
            // 事業整理解の取り組み方
            ApproachType = request.ApproachType,
            // 更新日時等
            UpdatedDateTime = now,
            UpdaterDisplayName = userName,
            UpdaterId = userId,
            OriginalId = bizUnderstanding.Id,
        };
        var historyRepository = _unitOfWork.GetRepository<BusinessUnderstandingHistory>();
        await historyRepository.AddAsync(newHistory);

        // キューの作成
        var queue = new Domain.Entities.BusinessUnderstandingMaterializedViewQueue()
        {
            Id = Ulid.NewUlid().ToString(),
            BusinessUnderstandingId = bizUnderstanding.Id,
            CustomerIdentificationId = request.CustomerIdentificationId,
            NumberOfRetries = 0,
            UpdaterId = userId,
            UpdaterName = userName ?? string.Empty,
            UpdatedDateTime = now
        };
        var queueRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingMaterializedViewQueue>();
        await queueRepository.AddAsync(queue);

        return await repository
            .AddAsync(newData) // データを追加します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(newData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
