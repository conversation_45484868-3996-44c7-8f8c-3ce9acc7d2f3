using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartner.UpdateBusinessPartner;

[WithDefaultBehaviors]
public record UpdateBusinessPartnerCommand(
    string Id,
    string Version,
    Guid? BusinessPartnerCustomerIdentificationId,
    string? BusinessPartnerCustomerName,
    BusinessPartnerIndustry BusinessPartnerIndustry,
    string? Note,
    string staffId,
    string staffName
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}

public record BusinessPartnerIndustry(
    string SubIndustryCode,
    string DetailIndustryCode,
    string IndustryCode
);