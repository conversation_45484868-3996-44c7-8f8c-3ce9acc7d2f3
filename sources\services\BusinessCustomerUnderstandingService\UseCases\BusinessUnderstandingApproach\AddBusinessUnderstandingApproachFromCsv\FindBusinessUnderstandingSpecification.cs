using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.AddBusinessUnderstandingApproachFromCsv;

public class FindBusinessUnderstandingSpecification : BaseSpecification<Domain.Entities.BusinessUnderstanding>
{
    public FindBusinessUnderstandingSpecification(IEnumerable<Guid> customerIdentificationIds)
    {
        Query
            .Include(x => x.BusinessCustomer)
            .Include(x => x.ExternalEnvironment, x => x.ThenInclude(x => x!.ExternalEnvironmentMaster))
            .Include(x => x.Histories)
            .Where(x => customerIdentificationIds.Contains(x.BusinessCustomer.CustomerIdentificationId))
            .AsNoTracking();
    }
}
