using CustomerProposalService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.UpdateDeedLoanCase;

[WithDefaultBehaviors]
public record UpdateDeedLoanCaseCommand(
    // Case Entity
    string Id,
    string CaseName,
    CaseStatus CaseStatus,
    string? CaseOutline,
    DateTimeOffset? ExpiredAt,
    string StaffId,
    string StaffName,
    IEnumerable<IFormFile>? UploadFiles,
    IEnumerable<string>? CaseLinks,
    // DeedLoanCase Entity
    string? AccountType,
    decimal? Amount,
    int? Period,
    string? ApplicableBaseInterestRate,
    decimal? InterestRate,
    string? InterestRateCustom,
    decimal? BaseInterestRate,
    decimal? Spread,
    string? LoanPurposeCode,
    string? LoanPurposeCustom,
    bool RelatedEsg,
    bool PreConsultationStandardTarget,
    bool IsEarthquakeRelated,
    string? RepaymentType,
    string? RepaymentSourceCode,
    string? CollateralType,
    string? CollateralOrGuaranteeCustom,
    string? GuaranteeType,
    CancelTypeOfLoan? CancelTypeOfLoan,
    string? CancelReason,
    TrafficSource? TrafficSource,
    string Version
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
