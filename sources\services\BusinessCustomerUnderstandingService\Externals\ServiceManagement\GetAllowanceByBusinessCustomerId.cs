using BusinessCustomerUnderstandingService.Domain.Enums;
using Shared.Messaging;

namespace BusinessCustomerUnderstandingService.Externals.ServiceManagement;

[MessageEndpoint("/service-management/get-allowance-by-business-customer-id")]
public class GetAllowanceByBusinessCustomerId : ISendRequest<List<GetAllowanceByBusinessCustomerIdResult>>
{
    public string BusinessCustomerId { get; set; } = default!;
}

public record GetAllowanceByBusinessCustomerIdResult(
    string Id,
    string BusinessCustomerId,
    DateTimeOffset ReferenceDate,
    string LoanRating,
    string TransactionPolicy,
    PlanType Plan,
    AllowanceType AllowanceType,
    DateTimeOffset UpdatedDateTime,
    string UpdaterId,
    string UpdaterName,
    string Version
);
