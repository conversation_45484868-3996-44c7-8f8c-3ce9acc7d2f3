using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.DeleteBusinessUnderstandingApproach;

public class DeleteBusinessUnderstandingApproachValidator : AbstractValidator<DeleteBusinessUnderstandingApproachCommand>
{
    public DeleteBusinessUnderstandingApproachValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        RuleFor(v => v.Version).NotEmpty();
    }
}
