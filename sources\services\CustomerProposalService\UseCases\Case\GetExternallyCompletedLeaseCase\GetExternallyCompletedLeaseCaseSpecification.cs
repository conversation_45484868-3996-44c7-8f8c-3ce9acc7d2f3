using Shared.Spec;

namespace CustomerProposalService.UseCases.Case.GetExternallyCompletedLeaseCase;

public class GetExternallyCompletedLeaseCaseSpecification : BaseSpecification<Domain.Entities.ExternallyCompletedLeaseCase>
{
    public GetExternallyCompletedLeaseCaseSpecification(GetExternallyCompletedLeaseCaseQuery request)
    {
        Query
            .Where(x => x.Id == request.Id)
            .Include(x => x.CaseUpdateInformation)
            .Include(x => x.CaseFiles)
            .Include(x => x.CaseLinks)
            .AsNoTracking();
    }
}
