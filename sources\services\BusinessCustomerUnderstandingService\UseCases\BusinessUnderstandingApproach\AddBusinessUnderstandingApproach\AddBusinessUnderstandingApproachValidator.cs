using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.AddBusinessUnderstandingApproach;

public class AddBusinessUnderstandingApproachValidator : AbstractValidator<AddBusinessUnderstandingApproachCommand>
{
    public AddBusinessUnderstandingApproachValidator()
    {
        RuleFor(v => v.ApproachType).IsInEnum().NotEqual(Domain.Enums.BusinessUnderstandingApproachType.Undefined);
    }
}
