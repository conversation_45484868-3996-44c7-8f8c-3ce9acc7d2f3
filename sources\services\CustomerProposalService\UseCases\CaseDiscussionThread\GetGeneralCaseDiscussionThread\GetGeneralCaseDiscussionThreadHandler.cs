using CustomerProposalService.Domain;
using MediatR;
using Nut.Results;

namespace CustomerProposalService.UseCases.CaseDiscussionThread.GetGeneralCaseDiscussionThread;

public class GetGeneralCaseDiscussionThreadHandler : IRequestHandler<GetGeneralCaseDiscussionThreadQuery, Result<GetGeneralCaseDiscussionThreadResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetGeneralCaseDiscussionThreadHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetGeneralCaseDiscussionThreadResult>> Handle(GetGeneralCaseDiscussionThreadQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.GeneralCaseDiscussionThread, string>();
        return repository.SingleAsync(new GetGeneralCaseDiscussionThreadSpecification(request))
            .Map(v =>
                new GetGeneralCaseDiscussionThreadResult
                (
                    v.Id,
                    v.RegisteredAt,
                    v.ThreadName,
                    v.RegistrantId,
                    v.RegistrantName,
                    v.Purpose,
                    v.Person,
                    v.IsPersonOfPower,
                    v.MentionTargetsHtml,
                    v.MentionTargetUserIds,
                    v.MentionTargetTeamIds,
                    v.Description,
                    v.Files,
                    v.Version
                    ));
    }
}
