using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindBusinessUnderstandingId;

public class FindBusinessUnderstandingIdSpecification : BaseSpecification<Domain.Entities.BusinessUnderstanding>
{
    public FindBusinessUnderstandingIdSpecification(FindBusinessUnderstandingIdQuery request)
    {
        Query
            .WhereIfNotEmpty(request.BusinessCustomerId, e => e.BusinessCustomerId == request.BusinessCustomerId)
            .AsNoTracking();
    }
}
