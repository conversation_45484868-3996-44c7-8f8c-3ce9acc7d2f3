using Azure.Storage.Blobs;
using Microsoft.Extensions.Azure;
using Shared.AzureBlob;
using Shared.ObjectStorage;

namespace BusinessCustomerUnderstandingService.Infrastructure.Storage;

/// <summary>
/// ファイルを利用するためのクライアントを提供するインターフェースです。
/// </summary>
public interface IAttachedFamilyTreeStorageClientProvider : IObjectStorageClientProvider
{
}

/// <summary>
/// 家系図添付ファイル用ストレージクライアントプロバイダーの実装です。
/// </summary>
public class AttachedFamilyTreeStorageClientProvider : BlobStorageClientProvider, IAttachedFamilyTreeStorageClientProvider
{
    /// <summary>
    /// 使用するAzure Blobのクライアント名です。
    /// </summary>
    public const string AzureClientName = "AttachedFamilyTreeBlob";

    /// <summary>
    /// ファイルを利用するためのクライアントを初期化します。
    /// </summary>
    /// <param name="clientFactory">Blobクライアントファクトリー</param>
    public AttachedFamilyTreeStorageClientProvider(IAzureClientFactory<BlobServiceClient> clientFactory)
        : base(clientFactory, AzureClientName, "family-tree")
    {
    }
}
