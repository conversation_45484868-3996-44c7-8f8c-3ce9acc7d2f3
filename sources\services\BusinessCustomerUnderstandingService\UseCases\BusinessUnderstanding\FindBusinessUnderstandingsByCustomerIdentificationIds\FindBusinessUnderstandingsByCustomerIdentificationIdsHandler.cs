using BusinessCustomerUnderstandingService.Domain.Dto.BusinessUnderstanding;
using BusinessCustomerUnderstandingService.Services.Queries;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindBusinessUnderstandingsByCustomerIdentificationIds;

public class FindBusinessUnderstandingsByCustomerIdentificationIdsHandler : IRequestHandler<FindBusinessUnderstandingsByCustomerIdentificationIdsQuery, Result<List<BusinessUnderstandingDto>>>
{
    private readonly IFindBusinessUnderstandingsByCustomerIdentificationIdsQueryService _queryService;

    public FindBusinessUnderstandingsByCustomerIdentificationIdsHandler(IFindBusinessUnderstandingsByCustomerIdentificationIdsQueryService queryService)
    {
        if (queryService is null) throw new ArgumentNullException(nameof(queryService));
        _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
    }

    public async Task<Result<List<BusinessUnderstandingDto>>> Handle(FindBusinessUnderstandingsByCustomerIdentificationIdsQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // 検索を行います。
        return await _queryService.Handle(request.CustomerIdentificationIds);
    }
}
