using Azure.Core;
using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Domain.Entities;
using BusinessCustomerUnderstandingService.Externals.ServiceManagement;
using BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.FindBusinessUnderstandingApproach;
using MediatR;
using Nut.Results;
using Shared.Domain;
using Shared.Messaging;
using Shared.Services;
using Dto = BusinessCustomerUnderstandingService.Domain.Dto.BusinessUnderstanding;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.UpdateBusinessEvaluation;

public class UpdateBusinessEvaluationHandler : IRequestHandler<UpdateBusinessEvaluationCommand, Result<string>>
{
    private DateTimeOffset _updatedDateTime;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMessageSender<GetAllowanceByBusinessCustomerId, List<GetAllowanceByBusinessCustomerIdResult>> _getAllowanceSender;
    private readonly IMessageSender<DeleteAllowance, string> _deleteAllowanceSender;
    private readonly ICurrentDateTimeService _currentDateTimeService;

    public UpdateBusinessEvaluationHandler(
        IUnitOfWork unitOfWork,
        IMessageSender<GetAllowanceByBusinessCustomerId,
        List<GetAllowanceByBusinessCustomerIdResult>> getAllowanceSender, IMessageSender<DeleteAllowance, string> deleteAllowanceSender,
        ICurrentDateTimeService currentDateTimeService
        )
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _getAllowanceSender = getAllowanceSender ?? throw new ArgumentNullException(nameof(getAllowanceSender));
        _deleteAllowanceSender = deleteAllowanceSender ?? throw new ArgumentNullException(nameof(deleteAllowanceSender));
        _currentDateTimeService = currentDateTimeService ?? throw new ArgumentNullException(nameof(currentDateTimeService));
    }

    public async Task<Result<string>> Handle(UpdateBusinessEvaluationCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        _updatedDateTime = _currentDateTimeService.NowDateTimeOffset();

        // ======================================================================================================================================
        // データ取得
        // ======================================================================================================================================
        // 事業性理解(経営計画 ～ 外部環境は更新対象なのでIncludeしない)
        var bizUnderstandingRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding, string>();
        var getBizUnderstandingResult = await bizUnderstandingRepository.SingleAsync(
            new FindByIdSpecification<Domain.Entities.BusinessUnderstanding, string>(request.Id)
            .Include(c => c.BusinessCustomer)
            .Include(c => c.RelationLevel)
            ).ConfigureAwait(false);
        if (getBizUnderstandingResult.IsError) return getBizUnderstandingResult.PreserveErrorAs<string>();
        var bizUnderstanding = getBizUnderstandingResult.Get();

        // 経営計画
        var managementPlanRepository = _unitOfWork.GetRepository<ManagementPlan, string>();
        var getManagementPlanResult = await managementPlanRepository.GetAsync(request.ManagementPlan.Id).ConfigureAwait(false);
        if (getManagementPlanResult.IsError) return getManagementPlanResult.PreserveErrorAs<string>();
        var managementPlan = getManagementPlanResult.Get();

        // 経営
        var managementRepository = _unitOfWork.GetRepository<Management, string>();
        var getManagementResult = await managementRepository.GetAsync(request.Management.Id).ConfigureAwait(false);
        if (getManagementResult.IsError) return getManagementResult.PreserveErrorAs<string>();
        var management = getManagementResult.Get();

        // ファイブフォースフレームワーク
        var fiveForceFrameworkRepository = _unitOfWork.GetRepository<FiveForceFramework, string>();
        var getFiveForceFrameworkResult = await fiveForceFrameworkRepository.GetAsync(request.FiveForceFramework.Id).ConfigureAwait(false);
        if (getFiveForceFrameworkResult.IsError) return getFiveForceFrameworkResult.PreserveErrorAs<string>();
        var fiveForceFramework = getFiveForceFrameworkResult.Get();

        // ファイブステップフレームワーク
        var fiveStepFrameWorkRepository = _unitOfWork.GetRepository<FiveStepFrameWork, string>();
        var getFiveStepFrameWorkResult = await fiveStepFrameWorkRepository.GetAsync(request.FiveStepFrameWork.Id).ConfigureAwait(false);
        if (getFiveStepFrameWorkResult.IsError) return getFiveStepFrameWorkResult.PreserveErrorAs<string>();
        var fiveStepFrameWork = getFiveStepFrameWorkResult.Get();

        // ESG・SDGs
        var eSGAndSDGsRepository = _unitOfWork.GetRepository<ESGAndSDGs, string>();
        var getESGAndSDGsResult = await eSGAndSDGsRepository.GetAsync(request.ESGAndSDGs.Id).ConfigureAwait(false);
        if (getESGAndSDGsResult.IsError) return getESGAndSDGsResult.PreserveErrorAs<string>();
        var eSGAndSDGs = getESGAndSDGsResult.Get();

        // 外部環境
        var externalEnvironmentRepository = _unitOfWork.GetRepository<Domain.Entities.ExternalEnvironment, string>();
        var getExternalEnvironmentResult = await externalEnvironmentRepository.SingleAsync(
            new FindByIdSpecification<Domain.Entities.ExternalEnvironment, string>(request.ExternalEnvironment.Id)
            .Include(x => x.ExternalEnvironmentMaster)
            ).ConfigureAwait(false);
        if (getExternalEnvironmentResult.IsError) return getExternalEnvironmentResult.PreserveErrorAs<string>();
        var externalEnvironment = getExternalEnvironmentResult.Get();

        // ======================================================================================================================================
        // 評点・取引方針の計算
        // ======================================================================================================================================
        bizUnderstanding.ManagementPlan = managementPlan;
        bizUnderstanding.Management = management;
        bizUnderstanding.FiveForceFramework = fiveForceFramework;
        bizUnderstanding.FiveStepFrameWork = fiveStepFrameWork;
        bizUnderstanding.ESGAndSDGs = eSGAndSDGs;
        bizUnderstanding.ExternalEnvironment = externalEnvironment;

        // 全評点の計算
        var getCurrentCalculationResult = await GetCalculationResult(bizUnderstanding);
        if (getCurrentCalculationResult.IsError) return getCurrentCalculationResult.PreserveErrorAs<string>();
        var currentCalculationResult = getCurrentCalculationResult.Get();

        // 取引方針
        var currentTransactionPolicy = GetTransactionPolicy(currentCalculationResult);

        // ======================================================================================================================================
        // 各データ更新
        // ======================================================================================================================================
        var newManagementPlan = await UpdateManagementPlan(request.ManagementPlan, managementPlan);
        if (newManagementPlan.IsError) return newManagementPlan.PreserveErrorAs<string>();
        bizUnderstanding.ManagementPlan = newManagementPlan.Get();
        await managementPlanRepository.UpdateAsync(bizUnderstanding.ManagementPlan);

        var newManagement = await UpdateManagement(request.Management, management);
        if (newManagement.IsError) return newManagement.PreserveErrorAs<string>();
        bizUnderstanding.Management = newManagement.Get();
        await managementRepository.UpdateAsync(bizUnderstanding.Management);

        var newFiveForceFramework = await UpdateFiveForceFramework(request.FiveForceFramework, fiveForceFramework);
        if (newFiveForceFramework.IsError) return newFiveForceFramework.PreserveErrorAs<string>();
        bizUnderstanding.FiveForceFramework = newFiveForceFramework.Get();
        await fiveForceFrameworkRepository.UpdateAsync(bizUnderstanding.FiveForceFramework);

        var newFiveStepFrameWork = await UpdateFiveStepFrameWork(request.FiveStepFrameWork, fiveStepFrameWork);
        if (newFiveStepFrameWork.IsError) return newFiveStepFrameWork.PreserveErrorAs<string>();
        bizUnderstanding.FiveStepFrameWork = newFiveStepFrameWork.Get();
        await fiveStepFrameWorkRepository.UpdateAsync(bizUnderstanding.FiveStepFrameWork);

        var newESGAndSDGs = await UpdateESGAndSDGs(request.ESGAndSDGs, eSGAndSDGs);
        if (newESGAndSDGs.IsError) return newESGAndSDGs.PreserveErrorAs<string>();
        bizUnderstanding.ESGAndSDGs = newESGAndSDGs.Get();
        await eSGAndSDGsRepository.UpdateAsync(bizUnderstanding.ESGAndSDGs);

        var newExternalEnvironment = await UpdateExternalEnvironment(request.ExternalEnvironment, externalEnvironment);
        if (newExternalEnvironment.IsError) return newExternalEnvironment.PreserveErrorAs<string>();
        bizUnderstanding.ExternalEnvironment = newExternalEnvironment.Get();
        await externalEnvironmentRepository.UpdateAsync(bizUnderstanding.ExternalEnvironment);

        // ======================================================================================================================================
        // 更新後の評点・取引方針の計算
        // ======================================================================================================================================
        // 全評点の計算
        var getNewCalculationResult = await GetCalculationResult(bizUnderstanding);
        if(getNewCalculationResult.IsError) return getNewCalculationResult.PreserveErrorAs<string>();
        var newCalculationResult = getNewCalculationResult.Get();
        // 取引方針
        var newTransactionPolicy = GetTransactionPolicy(newCalculationResult);

        // ======================================================================================================================================
        // 事業性理解の履歴追加
        // ======================================================================================================================================
        // 取引方針・事業性評価・リレーションレベル評価に変更があるか確認
        var addHistory = false;
        if (currentCalculationResult.TotalScore != newCalculationResult.TotalScore) addHistory = true;
        if (currentCalculationResult.ManagementPlanScore != newCalculationResult.ManagementPlanScore) addHistory = true;
        if (currentCalculationResult.ManagementScore != newCalculationResult.ManagementScore) addHistory = true;
        if (currentCalculationResult.FiveForceScore != newCalculationResult.FiveForceScore) addHistory = true;
        if (currentCalculationResult.FiveStepScore != newCalculationResult.FiveStepScore) addHistory = true;
        if (currentCalculationResult.ExternalEnvironmentScore != newCalculationResult.ExternalEnvironmentScore) addHistory = true;
        if (currentCalculationResult.ESGAndSDGsScore != newCalculationResult.ESGAndSDGsScore) addHistory = true;
        if (currentCalculationResult.MinimumRelationLevel != newCalculationResult.MinimumRelationLevel) addHistory = true;
        if (currentCalculationResult.AuthorityScore != newCalculationResult.AuthorityScore) addHistory = true;
        if (currentCalculationResult.RelationScore != newCalculationResult.RelationScore) addHistory = true;
        if (currentCalculationResult.DisclosureScore != newCalculationResult.DisclosureScore) addHistory = true;

        // 評点に変更がある場合は履歴を追加
        if (addHistory)
        {
            // 事業性理解の取り組み方
            var approachTypeRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingApproach, string>();
            var query = new FindBusinessUnderstandingApproachQuery() { CustomerIdentificationId = bizUnderstanding.BusinessCustomer.CustomerIdentificationId };
            var spec = new FindBusinessUnderstandingApproachSpecification(query);
            var result = await approachTypeRepository.FindAsync(spec);
            if (result.IsError) return result.PreserveErrorAs<string>();
            var approachType = result.Get().FirstOrDefault();

            var history = new BusinessUnderstandingHistory()
            {
                Id = Ulid.NewUlid().ToString(),
                // 取引方針
                TransactionPolicy = newTransactionPolicy,
                // 事業性理解評点
                BusinessEvaluation = newCalculationResult.TotalScore,
                ManagementPlanScore = newCalculationResult.ManagementPlanScore,
                ManagementScore = newCalculationResult.ManagementScore,
                FiveForceScore = newCalculationResult.FiveForceScore,
                FiveStepScore = newCalculationResult.FiveStepScore,
                ExternalEnvironmentScore = newCalculationResult.ExternalEnvironmentScore,
                ESGAndSDGsScore = newCalculationResult.ESGAndSDGsScore,
                // リレーションレベル評点
                RelationLevelEvaluation = newCalculationResult.MinimumRelationLevel,
                AuthorityScore = newCalculationResult.AuthorityScore,
                RelationScore = newCalculationResult.RelationScore,
                DisclosureScore = newCalculationResult.DisclosureScore,
                // 事業整理解の取り組み方
                ApproachType = approachType?.ApproachType ?? null,
                // 更新日時等
                UpdatedDateTime = _updatedDateTime,
                UpdaterDisplayName = request.UpdaterDisplayName,
                UpdaterId = request.UpdaterId,
                OriginalId = bizUnderstanding.Id,
            };

            var historyRepository = _unitOfWork.GetRepository<BusinessUnderstandingHistory>();
            await historyRepository.AddAsync(history);

            // 取引方針がT3以外の場合は引当区分を削除
            if (newTransactionPolicy != "T3")
            {
                var allowanceResult = await _getAllowanceSender.SendAsync(new GetAllowanceByBusinessCustomerId() { BusinessCustomerId = bizUnderstanding.BusinessCustomerId }).ConfigureAwait(false);
                if (allowanceResult.IsError) return allowanceResult.PreserveErrorAs<string>();
                var allowanceList = allowanceResult.Get();
                if (allowanceList.Any())
                {
                    foreach (var allowance in allowanceList)
                    {
                        var deleteResult = await _deleteAllowanceSender.SendAsync(new DeleteAllowance() { Id = allowance.Id, Version = allowance.Version }).ConfigureAwait(false);
                        if (deleteResult.IsError) return deleteResult.PreserveErrorAs<string>();
                    }
                }
            }
        }

        // ======================================================================================================================================
        // 最終処理
        // ======================================================================================================================================
        var queue = new Domain.Entities.BusinessUnderstandingMaterializedViewQueue()
        {
            Id = Ulid.NewUlid().ToString(),
            BusinessUnderstandingId = bizUnderstanding.Id,
            CustomerIdentificationId = bizUnderstanding.BusinessCustomer.CustomerIdentificationId,
            NumberOfRetries = 0,
            UpdaterId = request.UpdaterId,
            UpdaterName = request.UpdaterDisplayName,
            UpdatedDateTime = _updatedDateTime
        };
        var queueRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingMaterializedViewQueue>();
        var addQueueResult = await queueRepository.AddAsync(queue).ConfigureAwait(false);
        if (addQueueResult.IsError) return addQueueResult.PreserveErrorAs<string>();

        // DB保存
        var saveResult = await _unitOfWork.SaveEntitiesAsync();
        if (saveResult.IsError)
            return saveResult.PreserveErrorAs<string>();

        return Result.Ok(bizUnderstanding.Id);
    }

    /// <summary>
    /// 経営計画
    /// </summary>
    public async Task<Result<ManagementPlan>> UpdateManagementPlan(ManagementPlan request, ManagementPlan currentData)
    {
        // 履歴の追加(更新後の値を履歴として登録)
        var history = new ManagementPlanHistory
        {
            Id = Ulid.NewUlid().ToString(),
            ManagementPlanOverview = request.ManagementPlanOverview,
            ManagementPlanCurrentComment = request.ManagementPlanCurrentComment,
            PlanningDate = request.PlanningDate,
            StatusOfAchievementOfManagementPlan = request.StatusOfAchievementOfManagementPlan,
            BusinessPlanRating = request.BusinessPlanRating,
            Ideal = request.Ideal,
            Issue = request.Issue,
            UpdatedDateTime = _updatedDateTime,
            UpdaterId = request.UpdaterId,
            UpdaterName = request.UpdaterName,
            OriginalId = request.Id
        };
        var historyRepository = _unitOfWork.GetRepository<ManagementPlanHistory, string>();
        await historyRepository.AddAsync(history);

        // 値の更新
        currentData.ManagementPlanOverview = request.ManagementPlanOverview;
        currentData.ManagementPlanCurrentComment = request.ManagementPlanCurrentComment;
        currentData.PlanningDate = (request.PlanningDate != null) ? request.PlanningDate.Value.UtcDateTime : null;
        currentData.StatusOfAchievementOfManagementPlan = request.StatusOfAchievementOfManagementPlan;
        currentData.BusinessPlanRating = request.BusinessPlanRating;
        currentData.Ideal = request.Ideal;
        currentData.Issue = request.Issue;
        currentData.UpdatedDateTime = _updatedDateTime;
        currentData.UpdaterId = request.UpdaterId;
        currentData.UpdaterName = request.UpdaterName;
        currentData.Version = request.Version;

        return currentData;
    }

    /// <summary>
    /// 経営
    /// </summary>
    public async Task<Result<Management>> UpdateManagement(Management request, Management currentData)
    {
        // 履歴の追加(更新後の値を履歴として登録)
        var history = new ManagementHistory
        {
            Id = Ulid.NewUlid().ToString(),
            Ideal = request.Ideal,
            Issue = request.Issue,
            MediumToLongTermVisionRating = request.MediumToLongTermVisionRating,
            MediumToLongTermVisionComment = request.MediumToLongTermVisionComment,
            ExperienceRating = request.ExperienceRating,
            ExperienceComment = request.ExperienceComment,
            ExpertiseRating = request.ExpertiseRating,
            ExpertiseComment = request.ExpertiseComment,
            CentripetalForceRating = request.CentripetalForceRating,
            CentripetalForceComment = request.CentripetalForceComment,
            SuccessorRating = request.SuccessorRating,
            SuccessorComment = request.SuccessorComment,
            UpdatedDateTime = _updatedDateTime,
            UpdaterId = request.UpdaterId,
            UpdaterName = request.UpdaterName,
            OriginalId = request.Id
        };
        var historyRepository = _unitOfWork.GetRepository<ManagementHistory, string>();
        await historyRepository.AddAsync(history);

        // 値の更新
        currentData.Ideal = request.Ideal;
        currentData.Issue = request.Issue;
        currentData.MediumToLongTermVisionRating = request.MediumToLongTermVisionRating;
        currentData.MediumToLongTermVisionComment = request.MediumToLongTermVisionComment;
        currentData.ExperienceRating = request.ExperienceRating;
        currentData.ExperienceComment = request.ExperienceComment;
        currentData.ExpertiseRating = request.ExpertiseRating;
        currentData.ExpertiseComment = request.ExpertiseComment;
        currentData.CentripetalForceRating = request.CentripetalForceRating;
        currentData.CentripetalForceComment = request.CentripetalForceComment;
        currentData.SuccessorRating = request.SuccessorRating;
        currentData.SuccessorComment = request.SuccessorComment;
        currentData.UpdatedDateTime = _updatedDateTime;
        currentData.UpdaterId = request.UpdaterId;
        currentData.UpdaterName = request.UpdaterName;
        currentData.Version = request.Version;

        return currentData;
    }

    /// <summary>
    /// 5フォースフレームワーク
    /// </summary>
    public async Task<Result<FiveForceFramework>> UpdateFiveForceFramework(FiveForceFramework request, FiveForceFramework currentData)
    {
        // 履歴の追加(更新後の値を履歴として登録)
        var history = new FiveForceFrameworkHistory
        {
            Id = Ulid.NewUlid().ToString(),
            Ideal = request.Ideal,
            Issue = request.Issue,
            NewComerComment = request.NewComerComment,
            NewComerScore = request.NewComerScore,
            AbilityToNegotiateWithSalesPartnersComment = request.AbilityToNegotiateWithSalesPartnersComment,
            AbilityToNegotiateWithSalesPartnersScore = request.AbilityToNegotiateWithSalesPartnersScore,
            AbilityToNegotiateWithSuppliersComment = request.AbilityToNegotiateWithSuppliersComment,
            AbilityToNegotiateWithSuppliersScore = request.AbilityToNegotiateWithSuppliersScore,
            CompetitorComment = request.CompetitorComment,
            CompetitorScore = request.CompetitorScore,
            SubstituteArticleComment = request.SubstituteArticleComment,
            SubstituteArticleScore = request.SubstituteArticleScore,
            UpdatedDateTime = _updatedDateTime,
            UpdaterId = request.UpdaterId,
            UpdaterName = request.UpdaterName,
            OriginalId = request.Id
        };
        var historyRepository = _unitOfWork.GetRepository<FiveForceFrameworkHistory, string>();
        await historyRepository.AddAsync(history);

        // 値の更新
        currentData.Ideal = request.Ideal;
        currentData.Issue = request.Issue;
        currentData.NewComerComment = request.NewComerComment;
        currentData.NewComerScore = request.NewComerScore;
        currentData.AbilityToNegotiateWithSalesPartnersComment = request.AbilityToNegotiateWithSalesPartnersComment;
        currentData.AbilityToNegotiateWithSalesPartnersScore = request.AbilityToNegotiateWithSalesPartnersScore;
        currentData.AbilityToNegotiateWithSuppliersComment = request.AbilityToNegotiateWithSuppliersComment;
        currentData.AbilityToNegotiateWithSuppliersScore = request.AbilityToNegotiateWithSuppliersScore;
        currentData.CompetitorComment = request.CompetitorComment;
        currentData.CompetitorScore = request.CompetitorScore;
        currentData.SubstituteArticleComment = request.SubstituteArticleComment;
        currentData.SubstituteArticleScore = request.SubstituteArticleScore;
        currentData.UpdatedDateTime = _updatedDateTime;
        currentData.UpdaterId = request.UpdaterId;
        currentData.UpdaterName = request.UpdaterName;
        currentData.Version = request.Version;

        return currentData;
    }

    /// <summary>
    /// 5ステップフレームワーク
    /// </summary>
    public async Task<Result<FiveStepFrameWork>> UpdateFiveStepFrameWork(FiveStepFrameWork request, FiveStepFrameWork currentData)
    {
        // 履歴の追加(更新後の値を履歴として登録)
        var history = new FiveStepFrameWorkHistory
        {
            Id = Ulid.NewUlid().ToString(),
            Ideal = request.Ideal,
            Issue = request.Issue,
            CostReductionComment = request.CostReductionComment,
            CostReductionRating = request.CostReductionRating,
            ManagementComment = request.ManagementComment,
            ManagementRating = request.ManagementRating,
            ICTAndBPRComment = request.ICTAndBPRComment,
            ICTAndBPRRating = request.ICTAndBPRRating,
            HumanResourcesAndEvaluationAndDevelopmentComment = request.HumanResourcesAndEvaluationAndDevelopmentComment,
            HumanResourcesAndEvaluationAndDevelopmentRating = request.HumanResourcesAndEvaluationAndDevelopmentRating,
            MarketingThinkingComment = request.MarketingThinkingComment,
            MarketingThinkingRating = request.MarketingThinkingRating,
            UpdatedDateTime = _updatedDateTime,
            UpdaterId = request.UpdaterId,
            UpdaterName = request.UpdaterName,
            OriginalId = request.Id
        };
        var historyRepository = _unitOfWork.GetRepository<FiveStepFrameWorkHistory, string>();
        await historyRepository.AddAsync(history);

        // 値の更新
        currentData.Ideal = request.Ideal;
        currentData.Issue = request.Issue;
        currentData.CostReductionComment = request.CostReductionComment;
        currentData.CostReductionRating = request.CostReductionRating;
        currentData.ManagementComment = request.ManagementComment;
        currentData.ManagementRating = request.ManagementRating;
        currentData.ICTAndBPRComment = request.ICTAndBPRComment;
        currentData.ICTAndBPRRating = request.ICTAndBPRRating;
        currentData.HumanResourcesAndEvaluationAndDevelopmentComment = request.HumanResourcesAndEvaluationAndDevelopmentComment;
        currentData.HumanResourcesAndEvaluationAndDevelopmentRating = request.HumanResourcesAndEvaluationAndDevelopmentRating;
        currentData.MarketingThinkingComment = request.MarketingThinkingComment;
        currentData.MarketingThinkingRating = request.MarketingThinkingRating;
        currentData.UpdatedDateTime = _updatedDateTime;
        currentData.UpdaterId = request.UpdaterId;
        currentData.UpdaterName = request.UpdaterName;
        currentData.Version = request.Version;

        return currentData;
    }

    /// <summary>
    /// ESG・SDGs
    /// </summary>
    public async Task<Result<ESGAndSDGs>> UpdateESGAndSDGs(ESGAndSDGs request, ESGAndSDGs currentData)
    {
        // 履歴の追加(更新後の値を履歴として登録)
        var history = new ESGAndSDGsHistory
        {
            Id = Ulid.NewUlid().ToString(),
            Ideal = request.Ideal,
            Issue = request.Issue,
            IsCertificationAndDeclaration = request.IsCertificationAndDeclaration,
            ConceptOfESGAndSDGs = request.ConceptOfESGAndSDGs,
            ESGAndSDGsComment = request.ESGAndSDGsComment,
            DepartmentOfEnvironmentAndManagementRating = request.DepartmentOfEnvironmentAndManagementRating,
            EffortsForGreenhouseGasEmissionsRating = request.EffortsForGreenhouseGasEmissionsRating,
            SubjectsOfCertificationAndDeclaration = request.SubjectsOfCertificationAndDeclaration,
            SubjectsOfCertificationAndDeclarationComment = request.SubjectsOfCertificationAndDeclarationComment,
            UpdatedDateTime = _updatedDateTime,
            UpdaterId = request.UpdaterId,
            UpdaterName = request.UpdaterName,
            OriginalId = request.Id,
        };
        var historyRepository = _unitOfWork.GetRepository<ESGAndSDGsHistory, string>();
        await historyRepository.AddAsync(history);

        // 値の更新
        currentData.Ideal = request.Ideal;
        currentData.Issue = request.Issue;
        currentData.ConceptOfESGAndSDGs = request.ConceptOfESGAndSDGs;
        currentData.IsCertificationAndDeclaration = request.IsCertificationAndDeclaration;
        currentData.ESGAndSDGsComment = request.ESGAndSDGsComment;
        currentData.DepartmentOfEnvironmentAndManagementRating = request.DepartmentOfEnvironmentAndManagementRating;
        currentData.EffortsForGreenhouseGasEmissionsRating = request.EffortsForGreenhouseGasEmissionsRating;
        currentData.SubjectsOfCertificationAndDeclaration = request.SubjectsOfCertificationAndDeclaration;
        currentData.SubjectsOfCertificationAndDeclarationComment = request.SubjectsOfCertificationAndDeclarationComment;
        currentData.UpdatedDateTime = _updatedDateTime;
        currentData.UpdaterId = request.UpdaterId;
        currentData.UpdaterName = request.UpdaterName;
        currentData.Version = request.Version;

        return currentData;
    }

    /// <summary>
    /// 外部環境
    /// </summary>
    public async Task<Result<Domain.Entities.ExternalEnvironment>> UpdateExternalEnvironment(Domain.Entities.ExternalEnvironment request, Domain.Entities.ExternalEnvironment currentData)
    {
        // 計算のため外部環境を作成
        var externalEnvironment = new Domain.Entities.ExternalEnvironment();

        var repository = _unitOfWork.GetRepository<Domain.Entities.ExternalEnvironmentMaster, string>();
        var getResult = await repository.FindAsync(
            new FindByIdSpecification<Domain.Entities.ExternalEnvironmentMaster, string>(request.ExternalEnvironmentMasterId!))
            .ConfigureAwait(false);
        if (getResult.IsError) return getResult.PreserveErrorAs<Domain.Entities.ExternalEnvironment>();

        var externalEnvironmentMasters = getResult.Get();
        var externalEnvironmentMaster = externalEnvironmentMasters.Any() ? getResult.Get().First() : new Domain.Entities.ExternalEnvironmentMaster() { Id = "9999", Score = -5 };
        externalEnvironment.ExternalEnvironmentMasterId = request.ExternalEnvironmentMasterId;
        externalEnvironment.ExternalEnvironmentMaster = externalEnvironmentMaster;

        // 履歴の追加(更新後の値を履歴として登録)
        var history = new ExternalEnvironmentHistory
        {
            Id = Ulid.NewUlid().ToString(),
            ExternalEnvironmentMasterId = request.ExternalEnvironmentMasterId,
            Score = externalEnvironment.ExternalEnvironmentMaster!.Score,
            UpdatedDateTime = _updatedDateTime,
            UpdaterId = request.UpdaterId,
            UpdaterName = request.UpdaterName,
            OriginalId = request.Id
        };
        var historyRepository = _unitOfWork.GetRepository<ExternalEnvironmentHistory, string>();
        await historyRepository.AddAsync(history);

        // 外部環境は画面から変更されることはない。備忘録のため以下のコードは残しておく。
        //currentData.ExternalEnvironmentMasterId = request.ExternalEnvironmentMasterId;
        //currentData.UpdatedDateTime = _updatedDateTime;
        //currentData.UpdaterId = request.UpdaterId;
        //currentData.UpdaterName = request.UpdaterName;
        //currentData.Version = request.Version;

        return currentData;
    }

    // 全評点計算結果
    private async Task<Result<Dto.CalculationResult>> GetCalculationResult(Domain.Entities.BusinessUnderstanding currentData)
    {
        // 計算のため外部環境を作成
        var externalEnvironment = new Domain.Entities.ExternalEnvironment();
        
        var repository = _unitOfWork.GetRepository<Domain.Entities.ExternalEnvironmentMaster, string>();
        var getResult = await repository.FindAsync(
            new FindByIdSpecification<Domain.Entities.ExternalEnvironmentMaster, string>(currentData.ExternalEnvironment!.ExternalEnvironmentMasterId!))            
            .ConfigureAwait(false);
        if (getResult.IsError) return getResult.PreserveErrorAs<Dto.CalculationResult>();

        var externalEnvironmentMasters = getResult.Get();
        var externalEnvironmentMaster = externalEnvironmentMasters.Any() ? getResult.Get().First() : new Domain.Entities.ExternalEnvironmentMaster() { Id = "9999", Score = -5 };
        externalEnvironment.ExternalEnvironmentMasterId = currentData.ExternalEnvironment!.ExternalEnvironmentMasterId;
        externalEnvironment.ExternalEnvironmentMaster = externalEnvironmentMaster;

        return new Dto.CalculationResult(
            currentData.ManagementPlan!,
            currentData.Management!,
            currentData.FiveForceFramework!,
            currentData.FiveStepFrameWork!,
            externalEnvironment,
            currentData.ESGAndSDGs!,
            0,
            currentData.RelationLevel!
        );
    }

    // 取引方針
    private string GetTransactionPolicy(Dto.CalculationResult calculationResult)
    {
        var transactionPolicy = new Dto.TransactionPolicyResult(
            calculationResult.MinimumRelationLevel,
            calculationResult.TotalScore
            ).TransactionPolicy;

        return transactionPolicy;
    }
}
