using BusinessCustomerUnderstandingService.Externals.CustomerProposal;
using BusinessCustomerUnderstandingService.Infrastructure.Persistence;
using BusinessCustomerUnderstandingService.Services.Queries;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Nut.Results;
using Shared.Application;
using Shared.EntityFrameworkCore;
using Shared.Linq;
using Shared.Messaging;

namespace BusinessCustomerUnderstandingService.Infrastructure.QueryServices;

internal class FindCustomerIdentificationIdsBySearchConditionQueryService : IFindCustomerIdentificationIdsBySearchConditionQueryService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMessageSender<FindTeamMemberByTeamId, IEnumerable<FindTeamMemberByTeamIdResult>> _teamMembersMessageSender;

    public FindCustomerIdentificationIdsBySearchConditionQueryService(
        ApplicationDbContext dbContext,
        IMessageSender<FindTeamMemberByTeamId, IEnumerable<FindTeamMemberByTeamIdResult>> teamMembersMessageSender)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        _teamMembersMessageSender = teamMembersMessageSender ?? throw new ArgumentNullException(nameof(teamMembersMessageSender));
    }

    public async Task<Result<List<Guid>>> Handle(FindCustomerIdentificationIdsBySearchConditionQuery request)
    {
        // UserIdsにはチームIDも混在しているためチームIDをユーザーIDに分解する
        var userIds = (request.UserIds != null) ? request.UserIds.ToList() : new List<string>();
        if (request.UserIds != null && request.UserIds.Any())
        {
            var teamMemberGetResult = await _teamMembersMessageSender.SendAsync(new FindTeamMemberByTeamId { TeamIds = request.UserIds.ToList() });
            if (teamMemberGetResult.IsError) return teamMemberGetResult.PreserveErrorAs<List<Guid>>();
            var teamMember = teamMemberGetResult.Get();
            userIds.AddRange(teamMember.Select(x => x.StaffId));
        }

        var query = (_dbContext.BusinessUnderstandingMaterializedViews
            .WhereIf(request.TransactionPolicies != null, x => request.TransactionPolicies!.Contains(x.TransactionPolicy))
            .WhereIf(request.UserIds != null, x => userIds.Contains(x.CustomerStaffId!))
            .WhereIf(request.CustomerIdentificationIds != null, x => request.CustomerIdentificationIds!.Contains(x.CustomerIdentificationId))
            .Select(x => new FindCustomerIdentificationIdsBySearchConditionResult
            {
                CustomerIdentificationId = x.CustomerIdentificationId,
                TransactionPolicy = x.TransactionPolicy,
                CommunicationPlanCount = x.CommunicationPlanCount,
                StaffId = x.CustomerStaffId,
                LastUpdatedDateTime = x.UpdatedDateTime,
                TransactionPolicyConfirmedDateTime = x.TransactionPolicyConfirmedDateTime
            }));


        // 事業性理解区分で絞り込む
        var customerIdentificationIdList = new List<Guid>();
        if (request.ApproachTypes != null)
        {
            var approachTypeQuery = _dbContext.BusinessUnderstandingApproaches.Where(x => request.ApproachTypes.Contains(x.ApproachType));
            customerIdentificationIdList = approachTypeQuery.Select(x => x.CustomerIdentificationId).ToList();
            query = query.Where(x => customerIdentificationIdList.Contains(x.CustomerIdentificationId));
        }

        var sortOption = new List<Sort>();
        foreach (var sort in request.Sort)
        {
            switch (sort.Target)
            {
                case "transactionPolicy":
                    sortOption.Add(new Sort() { Target = nameof(FindCustomerIdentificationIdsBySearchConditionResult.TransactionPolicy), Desc = sort.Desc });
                    break;
                case "communicationPlanCount":
                    sortOption.Add(new Sort() { Target = nameof(FindCustomerIdentificationIdsBySearchConditionResult.CommunicationPlanCount), Desc = sort.Desc });
                    break;
                case "staffName":
                    sortOption.Add(new Sort() { Target = nameof(FindCustomerIdentificationIdsBySearchConditionResult.StaffId), Desc = sort.Desc });
                    break;
                case "lastUpdatedDateTime":
                    sortOption.Add(new Sort() { Target = nameof(FindCustomerIdentificationIdsBySearchConditionResult.LastUpdatedDateTime), Desc = sort.Desc });
                    break;
                case "transactionPolicyConfirmedDateTime":
                    sortOption.Add(new Sort() { Target = nameof(FindCustomerIdentificationIdsBySearchConditionResult.TransactionPolicyConfirmedDateTime), Desc = sort.Desc });
                    break;
            }
        }
        // ソート条件がない場合のデフォルト
        if (!sortOption.Any())
            sortOption.Add(new Sort() { Target = nameof(FindCustomerIdentificationIdsBySearchConditionResult.CustomerIdentificationId), Desc = false });

        // ソートして上位**件を取得
        var result = query.SortBy(sortOption).Take(request.CountLimit).Select(x=>x.CustomerIdentificationId).ToList().Distinct();

        // 顧客識別番号のみ抽出してリターン
        return await Task.FromResult(Result.Ok(result.ToList()));
    }

    private class FindCustomerIdentificationIdsBySearchConditionResult
    {
        public Guid CustomerIdentificationId { get; set; }
        public string? TransactionPolicy { get; set; }
        public int? CommunicationPlanCount { get; set; }
        public string? StaffId { get; set; }
        public DateTimeOffset? LastUpdatedDateTime { get; set; }
        public DateTimeOffset? TransactionPolicyConfirmedDateTime { get; set; }
    }
}
