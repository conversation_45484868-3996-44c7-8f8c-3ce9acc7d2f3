using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.FindBusinessUnderstandingApproach;

public class FindBusinessUnderstandingApproachSpecification : BaseSpecification<Domain.Entities.BusinessUnderstandingApproach>
{
    public FindBusinessUnderstandingApproachSpecification(FindBusinessUnderstandingApproachQuery request)
    {
        Query
            .WhereIf(request.CustomerIdentificationId is not null, e => e.CustomerIdentificationId == request.CustomerIdentificationId)
            .AsNoTracking();
    }
}
