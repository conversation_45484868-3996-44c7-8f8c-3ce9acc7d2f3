using FluentValidation;

namespace CustomerProposalService.UseCases.Case.UpdateNewLoanOnSitePhysicalConfirm;

public class UpdateNewLoanOnSitePhysicalConfirmValidator : AbstractValidator<UpdateNewLoanOnSitePhysicalConfirmCommand>
{
    public UpdateNewLoanOnSitePhysicalConfirmValidator()
    {
        RuleFor(v => v.Id).NotEmpty();
        When(v => v.OnSitePhysicalConfirmer is null, () =>
        {
            RuleFor(v => v.OnSitePhysicalConfirmationDateTime).Null();
        }).Otherwise(() =>
        {
            RuleFor(v => v.OnSitePhysicalConfirmer).NotEmpty();
            RuleFor(v => v.OnSitePhysicalConfirmationDateTime).NotEmpty();
        });
        RuleFor(v => v.Version).NotEmpty();
    }
}
