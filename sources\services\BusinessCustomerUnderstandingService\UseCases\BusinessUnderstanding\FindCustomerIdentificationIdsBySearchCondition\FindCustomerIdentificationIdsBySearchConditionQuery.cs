using BusinessCustomerUnderstandingService.Domain.Enums;
using MediatR;
using Nut.MediatR.ServiceLike;
using Nut.Results;
using Shared.Application;
using Shared.UseCase.AsServiceFilters;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindCustomerIdentificationIdsBySearchCondition;

[AsService("/business-customer-understanding/find-customer-identification-ids-by-search-condition", typeof(StripResultFilter))]
[WithDefaultBehaviors]
public record FindCustomerIdentificationIdsBySearchConditionQuery : PageQuery, IRequest<Result<List<Guid>>>
{
    public IEnumerable<string>? UserIds { get; set; }
    public IEnumerable<BusinessUnderstandingApproachType>? ApproachTypes { get; set; }
    public IEnumerable<string>? TransactionPolicies { get; set; }
    public IEnumerable<Guid>? CustomerIdentificationIds { get; set; }
    public int CountLmit { get; set; }
}
