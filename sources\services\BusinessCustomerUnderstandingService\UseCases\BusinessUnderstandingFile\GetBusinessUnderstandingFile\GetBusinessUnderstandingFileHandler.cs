using BusinessCustomerUnderstandingService.Infrastructure.Storage;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingFile.GetBusinessUnderstandingFile;
public class GetBusinessUnderstandingFileHandler : IRequestHandler<GetBusinessUnderstandingFileQuery, Result<IEnumerable<GetBusinessUnderstandingFileResult>>>
{
    private readonly IBusinessUnderstandingStorageClientProvider _objectStorageClientProvider;

    public GetBusinessUnderstandingFileHandler(IBusinessUnderstandingStorageClientProvider objectStorageClientProvider)
    {
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
    }

    public async Task<Result<IEnumerable<GetBusinessUnderstandingFileResult>>> Handle(GetBusinessUnderstandingFileQuery request, CancellationToken cancellationToken)
    {
        if(request == null)
            throw new ArgumentNullException(nameof(request));

        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        var result = await storageClient.GetObjectInformationsAsync($"business-understanding/{request.BusinessUnderstandingId}/{request.FileName}").ConfigureAwait(false);

        return result.MapEach(r => new GetBusinessUnderstandingFileResult(r.Name));
    }
}
