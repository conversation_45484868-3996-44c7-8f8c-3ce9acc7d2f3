using CustomerProposalService.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace CustomerProposalService.UseCases.Case.UpdateExternallyCompletedLeaseCase;

[WithDefaultBehaviors]
public record UpdateExternallyCompletedLeaseCaseCommand(
    string Id,
    string CaseName,
    CaseStatus CaseStatus,
    string? CaseOutline,
    DateTimeOffset? ExpiredAt,
    string StaffId,
    string StaffName,
    IEnumerable<IFormFile>? UploadFiles,
    IEnumerable<string>? CaseLinks,
    // ExternallyCompletedLeaseCase Entity
    bool IsEarthquakeRelated,
    string Version
) : IRequest<Result<string>> // エンティティのキーの型を設定します。
{
}
