using BusinessCustomerUnderstandingService.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BusinessCustomerUnderstandingService.Infrastructure.Persistence.Configurations;

public class ToDoCommentFileConfiguration : IEntityTypeConfiguration<ToDoCommentFile>
{
    public void Configure(EntityTypeBuilder<ToDoCommentFile> builder)
    {
        builder.HasOne<ToDoComment>()
            .WithMany(l => l.Files)
            .HasForeignKey(l => l.CommentId)
            .HasConstraintName("fk_todo_comment_todo_comment_file_id")
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(u => u.Id)
            .IsRequired()
            .HasMaxLength(26);

        builder.Property(u => u.FileName)
            .IsRequired()
            .HasMaxLength(300);
    }
}
