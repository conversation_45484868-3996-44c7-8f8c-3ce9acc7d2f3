using FluentValidation;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingFile.DeleteBusinessUnderstandingFile;

public class DeleteBusinessUnderstandingFileValidator : AbstractValidator<DeleteBusinessUnderstandingFileCommand>
{
    public DeleteBusinessUnderstandingFileValidator()
    {
        RuleFor(v => v.BusinessUnderstandingId).NotEmpty();
        RuleFor(v => v.FileName).NotEmpty();
    }
}
