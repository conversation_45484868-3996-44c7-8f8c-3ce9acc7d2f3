using BusinessCustomerUnderstandingService.Domain.Enums;
using MediatR;
using Nut.Results;
using Shared.Application;

namespace BusinessCustomerUnderstandingService.UseCases.CommunicationPlan.FindSpecificCustomerCommunicationPlan;

[WithDefaultBehaviors]
public record FindSpecificCustomerCommunicationPlanQuery : PageQuery, IRequest<Result<PaginatedResult<FindSpecificCustomerCommunicationPlanResult>>>
{
    public string BusinessUnderstandingId { get; init; } = default!;

    public string Title { get; init; } = default!;

    public string StaffName { get; init; } = default!;

    public IEnumerable<Status?> Statuses { get; init; } = default!;

    public IEnumerable<CommunicationPlanType> Types { get; init; } = default!;

    public DateTimeOffset? ExpiredAtFrom { get; init; } = default!;

    public DateTimeOffset? ExpiredAtTo { get; init; } = default!;
}
