namespace BusinessCustomerUnderstandingService.UseCases.BusinessPartner.GetAllBusinessPartners;

public record GetAllBusinessPartnersResult(
    string Id,
    string BusinessUnderstandingId,
    Guid? BusinessPartnerCustomerIdentificationId,
    string? BusinessPartnerCustomerName,
    BusinessPartnerIndustry BusinessPartnerIndustry,
    bool? HasBusinessPartnershipWithOurCompany,
    bool? HasCreativeAccountingIncident,
    string? Note,
    string Version
);

public record BusinessPartnerIndustry(
    string Id,
    string BusinessPartnerId,
    string SubIndustryCode,
    string DetailIndustryCode,
    string IndustryCode
);