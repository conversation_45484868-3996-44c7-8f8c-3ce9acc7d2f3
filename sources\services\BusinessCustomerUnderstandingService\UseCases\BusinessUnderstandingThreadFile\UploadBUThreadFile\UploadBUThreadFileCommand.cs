using MediatR;
using Microsoft.AspNetCore.Http;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingThreadFile.UploadBUThreadFile;

[WithDefaultBehaviors]
public record UploadBUThreadFileCommand(
   string ThreadId,
   string UpdaterId,
   string UpdaterName,
   List<string> SelectedFileNames,
   List<IFormFile> UploadFiles) : IRequest<Result<List<Domain.Entities.BusinessUnderstandingThreadFile>>>
{
}
