using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.UseCases.CommercialDistributionTemplate.GetCommercialDistributionTemplate;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistributionTemplate.DeleteCommercialDistributionTemplate;

public class DeleteCommercialDistributionTemplateHandler : IRequestHandler<DeleteCommercialDistributionTemplateCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;

    public DeleteCommercialDistributionTemplateHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public async Task<Result<string>> Handle(DeleteCommercialDistributionTemplateCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CommercialDistributionTemplate, string>();
        // 既存データを取得します。
        var spec = new GetCommercialDistributionTemplateSpecification(request.Id);
        var getResult = await repository.SingleAsync(spec);

        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();
        // バージョン番号は、同時実行制御を有効にするために引数で受け取ったデータの値で必ず上書きします。
        currentData.Version = request.Version;

        return await repository
            .DeleteAsync(currentData) // データを削除として設定します。
            .FlatMap(() => _unitOfWork.SaveEntitiesAsync()) // データを保存します。
            .FlatMap(() => Result.Ok(currentData.Id)) // 結果としてIDを返します。
            .ConfigureAwait(false);
    }
}
