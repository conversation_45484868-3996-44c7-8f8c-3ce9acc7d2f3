using MediatR;
using Nut.Results;
using Shared.Application;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingFile.FindBusinessUnderstandingFile;

[WithDefaultBehaviors]
public record FindBusinessUnderstandingFileQuery : PageQuery, IRequest<Result<PaginatedResult<FindBusinessUnderstandingFileResult>>>
{
    public string BusinessUnderstandingId { get; init; } = default!;

}
