using CustomerProposalService.Infrastructure.Storage;
using MediatR;
using Nut.Results;
using Shared.ObjectStorage;
using Shared.Results.Errors;

namespace CustomerProposalService.UseCases.CaseDiscussionReply.DownloadCaseDiscussionReplyFile;
public class DownloadCaseDiscussionReplyFileHandler : IRequestHandler<DownloadCaseDiscussionReplyFileQuery, Result<DownloadCaseDiscussionReplyFileResult>>
{
    private readonly ICaseDiscussionThreadStorageClientProvider _objectStorageClientProvider;
    private readonly string _replyLinkedFilePathBase = "case-discussion-reply";

    public DownloadCaseDiscussionReplyFileHandler(ICaseDiscussionThreadStorageClientProvider objectStorageClientProvider)
    {
        _objectStorageClientProvider = objectStorageClientProvider ?? throw new ArgumentNullException(nameof(objectStorageClientProvider));
    }

    public async Task<Result<DownloadCaseDiscussionReplyFileResult>> Handle(DownloadCaseDiscussionReplyFileQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        // ObjectStorageのクライアントを作成します。
        var storageClient = await _objectStorageClientProvider.CreateAsync().ConfigureAwait(false);

        // 保存されているオブジェクトの一覧を取得
        var objects = await storageClient.GetObjectInformationsAsync($"{_replyLinkedFilePathBase}/{request.CaseDiscussionReplyId}").ConfigureAwait(false);
        if (objects.IsError) return objects.PreserveErrorAs<DownloadCaseDiscussionReplyFileResult>();

        // 指定されたファイルを取得
        var target = objects.Get().Where(e => e.Name == $"{_replyLinkedFilePathBase}/{request.CaseDiscussionReplyId}/{request.FileName}").FirstOrDefault();
        if (target is null) return Result.Error<DownloadCaseDiscussionReplyFileResult>(new DataNotFoundException());

        var result = await storageClient.GetAsync(target.Name);
        return result.Map(s => new DownloadCaseDiscussionReplyFileResult(s));
    }
}
