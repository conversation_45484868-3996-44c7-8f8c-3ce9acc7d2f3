using BusinessCustomerUnderstandingService.Domain.Enums;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingDiscussion.GetBusinessUnderstandingDiscussion;

public record GetBusinessUnderstandingDiscussionResult(
    string Id,
    DateTimeOffset? RegisteredDateTime,
    string? Registrant,
    string? RegistrantId,
    string Description,
    bool? IsCorporateDepositTheme,
    bool? IsFundSettlementTheme,
    IEnumerable<Domain.Entities.BusinessUnderstandingDiscussionReaction>? Reactions,
    IEnumerable<Domain.Entities.BusinessUnderstandingDiscussionFile> Files,
    string? ThreadId,
    DiscussionPurpose Purpose,
    string? Person,
    bool? IsPersonOfPower,
    string Version
);
