using BusinessCustomerUnderstandingService.Domain;
using BusinessCustomerUnderstandingService.Services.Queries;
using MediatR;
using Nut.Results;
using Shared.Domain;

namespace BusinessCustomerUnderstandingService.UseCases.CustomerIdeasUnderstanding.DeleteCustomerIdeasUnderstanding;

public class DeleteCustomerIdeasUnderstandingHandler : IRequestHandler<DeleteCustomerIdeasUnderstandingCommand, Result<string>>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IGetCommunicationPlanCountQueryService _queryService;

    public DeleteCustomerIdeasUnderstandingHandler(
        IUnitOfWork unitOfWork,
        IGetCommunicationPlanCountQueryService queryService)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        _queryService = queryService ?? throw new ArgumentNullException(nameof(queryService));
    }

    public async Task<Result<string>> Handle(DeleteCustomerIdeasUnderstandingCommand request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CustomerIdeasUnderstanding, string>();
        // 既存データを取得します。
        var getResult = await repository.GetAsync(request.Id);
        // 既存データの取得がエラーだった場合は、そのままエラーを返します。
        if (getResult.IsError) return getResult.PreserveErrorAs<string>();

        var currentData = getResult.Get();
        currentData.Version = request.Version;

        // 削除
        var deleteResult = await repository.DeleteAsync(currentData);
        if (deleteResult.IsError) return deleteResult.PreserveErrorAs<string>();

        // 保存
        var saveResult = await _unitOfWork.SaveEntitiesAsync();
        if (saveResult.IsError) return saveResult.PreserveErrorAs<string>();

        // コミュニケーションプランの件数を取得
        var getCountResult = await _queryService.Handle(currentData.BusinessUnderstandingId);
        if (getCountResult.IsError) Result.Error<int>("コミュニケーションプランの件数取得処理でエラーが発生しました。");

        // 事業性理解の取得
        var businessUnderstandingRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstanding>();
        var getBusinessUnderstandingResult = await businessUnderstandingRepository.SingleAsync(
            new FindByIdSpecification<Domain.Entities.BusinessUnderstanding, string>(currentData.BusinessUnderstandingId)
            .Include(x=>x.BusinessUnderstandingMaterializedView)
            );
        if (getBusinessUnderstandingResult.IsError) return getBusinessUnderstandingResult.PreserveErrorAs<string>();
        var businessUnderstanding = getBusinessUnderstandingResult.Get();

        // 事業性理解の更新
        var queue = new Domain.Entities.BusinessUnderstandingMaterializedViewQueue()
        {
            Id = Ulid.NewUlid().ToString(),
            BusinessUnderstandingId = businessUnderstanding.Id,
            CustomerIdentificationId = businessUnderstanding.BusinessUnderstandingMaterializedView!.CustomerIdentificationId,
            NumberOfRetries = 0,
            // 削除処理の場合、以下の項目は既存のままとする
            UpdaterId = businessUnderstanding.BusinessUnderstandingMaterializedView!.UpdaterId!,
            UpdaterName = businessUnderstanding.BusinessUnderstandingMaterializedView!.UpdaterName!,
            UpdatedDateTime = businessUnderstanding.BusinessUnderstandingMaterializedView!.UpdatedDateTime!.Value
        };
        var queueRepository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingMaterializedViewQueue>();
        await queueRepository.AddAsync(queue).ConfigureAwait(false);

        // 保存
        await _unitOfWork.SaveEntitiesAsync();

        return Result.Ok(currentData.Id);
    }
}
