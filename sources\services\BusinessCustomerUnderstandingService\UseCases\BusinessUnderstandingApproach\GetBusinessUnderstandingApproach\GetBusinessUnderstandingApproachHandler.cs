using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstandingApproach.GetBusinessUnderstandingApproach;

public class GetBusinessUnderstandingApproachHandler : IRequestHandler<GetBusinessUnderstandingApproachQuery, Result<GetBusinessUnderstandingApproachResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetBusinessUnderstandingApproachHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetBusinessUnderstandingApproachResult>> Handle(GetBusinessUnderstandingApproachQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.BusinessUnderstandingApproach, string>();
        return repository.GetAsync(request.Id)
            .Map(v => new GetBusinessUnderstandingApproachResult(v.Id, v.CustomerIdentificationId, v.ApproachType, v.Comment, v.Version));
    }
}
