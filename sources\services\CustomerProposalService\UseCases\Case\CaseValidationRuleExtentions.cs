using FluentValidation;

namespace CustomerProposalService.UseCases.Case;

public static class CaseValidationRuleExtentions
{
    public static IRuleBuilderOptions<T, string?> CaseOutline<T>(this IRuleBuilder<T, string?> source)
        => source.Must(value => LineEndingMaximumLength(value, 500))
        .WithMessage("案件概要は500文字以下で入力してください。");

    public static IRuleBuilderOptions<T, string?> CancelReason<T>(this IRuleBuilder<T, string?> source)
        => source.Must(value => LineEndingMaximumLength(value, 500))
        .WithMessage("取下げ・失注・謝絶理由は500文字以下で入力してください。");

    public static IRuleBuilderOptions<T, string?> QuotationNote<T>(this IRuleBuilder<T, string?> source)
        => source.Must(value => LineEndingMaximumLength(value, 1000))
        .WithMessage("備考は1000文字以下で入力してください。");

    private static bool LineEndingMaximumLength(string? value, int maxLength)
    {
        var modifiedValue = value?.Replace("\r\n", "\n");
        return modifiedValue == null || modifiedValue.Length <= maxLength;
    }
}
