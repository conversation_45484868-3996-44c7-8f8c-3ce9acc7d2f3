using BusinessCustomerUnderstandingService.Domain;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistribution.GetCommercialDistribution;

public class GetCommercialDistributionHandler : IRequestHandler<GetCommercialDistributionQuery, Result<GetCommercialDistributionResult>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetCommercialDistributionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    public Task<Result<GetCommercialDistributionResult>> Handle(GetCommercialDistributionQuery request, CancellationToken cancellationToken)
    {
        if (request is null)
            throw new ArgumentNullException(nameof(request));

        var repository = _unitOfWork.GetRepository<Domain.Entities.CommercialDistribution, string>();
        return repository.SingleAsync(new GetCommercialDistributionSpecification(request.Id))
            .Map(v => new GetCommercialDistributionResult(
                v.Id,
                v.CanvasColor,
                v.UpdaterId,
                v.UpdaterName,
                v.UpdatedDateTime,
                v.Nodes,
                v.Edges,
                v.BusinessUnderstandingId,
                v.Version));
    }
}
