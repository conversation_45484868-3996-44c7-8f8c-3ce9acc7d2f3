using Shared.Spec;

namespace BusinessCustomerUnderstandingService.UseCases.CommercialDistributionTemplate.GetCommercialDistributionTemplate;

public class GetCommercialDistributionTemplateSpecification : BaseSpecification<Domain.Entities.CommercialDistributionTemplate>
{
    public GetCommercialDistributionTemplateSpecification(string Id)
    {
        Query
            .Where(x => x.Id == Id)
            .Include(x => x.Nodes)
            .Include(x => x.Edges)
            .AsNoTracking();
    }
}
