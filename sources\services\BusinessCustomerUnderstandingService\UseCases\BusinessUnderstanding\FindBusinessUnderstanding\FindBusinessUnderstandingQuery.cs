using BusinessCustomerUnderstandingService.Domain.Enums;
using MediatR;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindBusinessUnderstanding;

[WithDefaultBehaviors]
public record FindBusinessUnderstandingQuery : IRequest<Result<List<FindBusinessUnderstandingResult>>>
{
    public IEnumerable<Guid>? CustomerIdentificationIds { get; init; }

    public IEnumerable<BusinessUnderstandingApproachType>? ApproachTypes { get; init; }

    public IEnumerable<string>? TransactionPolicies { get; init; }
}
