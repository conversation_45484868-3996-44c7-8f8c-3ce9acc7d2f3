using BusinessCustomerUnderstandingService.Domain.Dto.BusinessUnderstanding;
using MediatR;
using Nut.MediatR.ServiceLike;
using Nut.Results;
using Shared.UseCase.AsServiceFilters;

namespace BusinessCustomerUnderstandingService.UseCases.BusinessUnderstanding.FindBusinessUnderstandingsByCustomerIdentificationIds;

[AsService("/business-customer-understanding/find-businessunderstandings-by-customer-identification-ids", typeof(StripResultFilter))]
[WithDefaultBehaviors]
public record FindBusinessUnderstandingsByCustomerIdentificationIdsQuery : IRequest<Result<List<BusinessUnderstandingDto>>>
{
    public IEnumerable<Guid> CustomerIdentificationIds { get; init; } = default!;
}
