using BusinessCustomerUnderstandingService.Domain.Enums;
using Nut.Results;

namespace BusinessCustomerUnderstandingService.Services.Queries;

public interface IGetBusinessUnderstandingSummaryQueryService
{
    Task<Result<GetBusinessUnderstandingSummaryQueryResult>> Handle(Guid customerIdentificationId);
}

public record GetBusinessUnderstandingSummaryQueryResult
{
    public string? BusinessCustomerId { get; init; } = default!;
    public string? BusinessUnderstandingId { get; init; } = default!;
    public string? TransactionPolicy { get; init; } = default!;
    public FinancialsAlreadySharedType? FinancialsAlreadySharedType { get; init; } = default!;
    public DateTimeOffset? FinanceSharedAt { get; init; } = default!;
    public BusinessPlanRatingType? BusinessPlanRatingType { get; init; } = default!;
    public DateTimeOffset? CommercialDistributionUpdatedAt { get; init; } = default!;
}